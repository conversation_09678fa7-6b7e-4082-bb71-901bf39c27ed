AWSTemplateFormatVersion: "2010-09-09"
Description: Angular App on ECS Fargate

Parameters:
  Environment:
    Type: String
    Default: dev

Resources:

  AngularLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/ecs/angular-${Environment}"
      RetentionInDays: 7

  AngularCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub "angular-cluster-${Environment}"

  AngularTaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "ecsTaskExecutionRole-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy

  AngularTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub "angular-task-${Environment}"
      Cpu: "256"
      Memory: "512"
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !GetAtt AngularTaskExecutionRole.Arn
      ContainerDefinitions:
        - Name: angular-container
          Image: 203918857465.dkr.ecr.eu-west-1.amazonaws.com/angular-app:latest
          PortMappings:
            - ContainerPort: 80
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref AngularLogGroup
              awslogs-region: eu-west-1
              awslogs-stream-prefix: angular

  AngularService:
    Type: AWS::ECS::Service
    Properties:
      Cluster: !Ref AngularCluster
      DesiredCount: 1
      LaunchType: FARGATE
      TaskDefinition: !Ref AngularTaskDefinition
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: ENABLED
          Subnets:
            - subnet-07a383b136aec3f88  # Remplace ou ajoute d'autres subnets si nécessaire
          SecurityGroups:
            - sg-085e0b0a50cfe4d0e
