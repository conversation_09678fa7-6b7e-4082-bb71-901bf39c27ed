{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"mnf-front": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main.ts", "tsConfig": "tsconfig.app.json", "polyfills": ["@angular/localize/init"], "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["@angular/material/prebuilt-themes/rose-red.css", "src/styles.css"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1.1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "12kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "mnf-front:build:production"}, "development": {"buildTarget": "mnf-front:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["@angular/material/prebuilt-themes/rose-red.css", "src/styles.css"]}}}}}}