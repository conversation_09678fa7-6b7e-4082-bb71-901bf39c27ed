export interface Quiz {
  id?: number;
  name: string;
  description?: string;
  userId: number;
  questions?: number[]; // Keep for backward compatibility but mark as optional
  quizQuestions: QuizQuestion[]; // Make required to match QuizDto
  createdAt?: Date;
  updatedAt?: Date;
}

export interface QuizQuestion {
  id?: number;
  quizId?: number;
  questionId?: number; // Keep for backward compatibility but mark as optional
  question_id?: number; // Keep for backward compatibility but mark as optional
  position: number;
  question?: {
    id: number;
    title: string;
    statement: string;
    chapterId?: number; // Optional to match chapter_id in Question interface
    chapter_id?: number; // Optional to match backend
  };
}
