<div class="pagination-container">
  <nav class="pagination-nav" aria-label="Pagination">
    <button
      (click)="changePage(currentPage - 1)"
      [disabled]="currentPage === 1"
      class="pagination-button pagination-button-prev"
      [class.disabled]="currentPage === 1"
    >
      <span class="sr-only">Previous</span>
      &laquo;
    </button>

    <!-- Show first page -->
    <!-- Debug info -->
    <div style="display: none;">
      totalPages: {{ totalPages }}, currentPage: {{ currentPage }}
    </div>
    @if (totalPages > 0 && currentPage > 2) {
      <button
        (click)="changePage(1)"
        class="pagination-page-button"
      >
        1
      </button>
    }

    <!-- Show ellipsis if needed -->
    @if (currentPage > 3) {
      <span class="pagination-ellipsis">...</span>
    }

    <!-- Show previous page if not first -->
    @if (currentPage > 1) {
      <button
        (click)="changePage(currentPage - 1)"
        class="pagination-page-button"
      >
        {{ currentPage - 1 }}
      </button>
    }

    <!-- Current page -->
    <button
      class="pagination-page-button pagination-page-button-active"
    >
      {{ currentPage }}
    </button>

    <!-- Show next page if not last -->
    @if (currentPage < totalPages) {
      <button
        (click)="changePage(currentPage + 1)"
        class="pagination-page-button"
      >
        {{ currentPage + 1 }}
      </button>
    }

    <!-- Show ellipsis if needed -->
    @if (currentPage < totalPages - 2) {
      <span class="pagination-ellipsis">...</span>
    }

    <!-- Show last page -->
    @if (totalPages > 1 && currentPage < totalPages - 1) {
      <button
        (click)="changePage(totalPages)"
        class="pagination-page-button"
      >
        {{ totalPages }}
      </button>
    }

    <button
      (click)="changePage(currentPage + 1)"
      [disabled]="currentPage === totalPages"
      class="pagination-button pagination-button-next"
      [class.disabled]="currentPage === totalPages"
    >
      <span class="sr-only">Next</span>
      &raquo;
    </button>
  </nav>
</div>
