/* Pagination */
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-nav {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: 1px solid #CD7A88;
  background-color: white;
  color: black;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.25rem;
}

.pagination-button:hover {
  background-color: #F0DDDB;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button-prev {
  border-radius: 0.25rem;
}

.pagination-button-next {
  border-radius: 0.25rem;
}

.pagination-page-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  border: 1px solid #CD7A88;
  background-color: white;
  color: black;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.25rem;
  padding: 0 0.5rem;
}

.pagination-page-button:hover {
  background-color: #F0DDDB;
}

.pagination-page-button-active {
  background-color: #CD7A88;
  color: white;
  border-color: #CD7A88;
}

.pagination-ellipsis {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  color: black;
  font-size: 0.875rem;
  font-weight: 500;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
