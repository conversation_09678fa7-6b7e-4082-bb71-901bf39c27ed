import { Component, EventEmitter, Input, Output, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-pagination',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './pagination.html',
  styleUrl: './pagination.css'
})
export class PaginationComponent implements OnChanges {
  @Input() currentPage: number = 1;
  @Input() pageSize: number = 10;
  @Input() totalItems: number = 0;
  @Input() totalPages: number = 0;
  @Output() pageChange = new EventEmitter<number>();

  // Make Math available in the template
  Math = Math;

  ngOnChanges(changes: SimpleChanges): void {
    // Log when inputs change
    if (changes['totalPages']) {

      // Ensure totalPages is at least 1 if we have items
      if (this.totalItems > 0 && (this.totalPages === 0 || this.totalPages === undefined)) {
        this.totalPages = Math.max(1, Math.ceil(this.totalItems / this.pageSize));
      }
    }

    if (changes['totalItems']) {

      // Recalculate totalPages if it's not set or is 0
      if (this.totalItems > 0 && (this.totalPages === 0 || this.totalPages === undefined)) {
        this.totalPages = Math.max(1, Math.ceil(this.totalItems / this.pageSize));
        console.log(`PaginationComponent: totalPages calculated from totalItems: ${this.totalPages}`);
      }
    }
  }

  get pageNumbers(): number[] {
    const pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
    console.log(`PaginationComponent: pageNumbers() returning ${pages.length} pages`);
    return pages;
  }

  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      // Emit the page number (1-based)
      this.pageChange.emit(page);
    }
  }
}
