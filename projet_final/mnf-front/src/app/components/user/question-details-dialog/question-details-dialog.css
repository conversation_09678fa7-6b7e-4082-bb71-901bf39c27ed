.dialog-container {
  display: flex;
  flex-direction: column;
  min-width: 300px;
  max-width: 500px;
  padding: 0;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--primary-color);
  color: white;
}

.dialog-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

.close-button {
  color: white;
}

.question-details {
  padding: 24px;
  overflow-y: auto;
  max-height: 60vh;
}

.chapter-name, .question-title {
  margin-bottom: 16px;
}

.divider {
  margin: 16px 0;
}

.question-content {
  margin-top: 16px;
}

.statement {
  margin-top: 8px;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

/* Answers section */
.answers-section {
  margin-top: 16px;
}

.answers-list {
  margin-top: 12px;
}

.answer-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  border-left: 3px solid transparent;
}

.answer-item.correct-answer {
  background-color: rgba(76, 175, 80, 0.1);
  border-left-color: var(--primary-color);
}

.answer-label {
  font-weight: 500;
  margin-right: 8px;
  min-width: 20px;
}

.answer-text {
  flex: 1;
}

.answer-status {
  margin-left: 8px;
  display: flex;
  align-items: center;
}

/* Loading container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.loading-container span {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.6);
}

/* Error message */
.error-message {
  color: var(--warn-color);
  padding: 12px;
  text-align: center;
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: 4px;
  margin-top: 8px;
}

/* Empty message */
.empty-message {
  padding: 12px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  margin-top: 8px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 24px 16px;
}
