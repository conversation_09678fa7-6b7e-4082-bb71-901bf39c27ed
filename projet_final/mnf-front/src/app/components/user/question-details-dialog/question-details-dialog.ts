import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AnswerService } from '../../../services/admin/answer.service';
import { Answer } from '../../../models/admin/Answer';

@Component({
  selector: 'app-question-details-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './question-details-dialog.html',
  styleUrl: './question-details-dialog.css'
})
export class QuestionDetailsDialogComponent implements OnInit {
  answers: Answer[] = [];
  loading = false;
  error = '';

  constructor(
    private dialogRef: MatDialogRef<QuestionDetailsDialogComponent>,
    private answerService: AnswerService,
    @Inject(MAT_DIALOG_DATA) public data: {
      questionId: number;
      title: string;
      statement: string;
      chapterId: number;
      chapterName?: string;
    }
  ) {}

  ngOnInit(): void {
    this.loadAnswers();
  }

  /**
   * Load answers for the question
   */
  loadAnswers(): void {
    this.loading = true;
    this.error = '';

    this.answerService.getAnswersByQuestionId(this.data.questionId)
      .then(answers => {
        this.answers = answers;
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load answers';
        this.loading = false;
        console.error('Error loading answers:', error);
      });
  }

  /**
   * Close the dialog
   */
  close(): void {
    this.dialogRef.close();
  }
}
