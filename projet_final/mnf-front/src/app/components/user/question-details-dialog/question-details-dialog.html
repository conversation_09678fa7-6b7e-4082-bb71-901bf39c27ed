<div class="dialog-container">
  <!-- Dialog header -->
  <div class="dialog-header">
    <h2 class="dialog-title">Question Details</h2>
    <button mat-icon-button (click)="close()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Question details -->
  <div class="question-details">
    @if (data.chapterName) {
      <div class="chapter-name">
        <strong>Chapter:</strong> {{ data.chapterName }}
      </div>
    }

    <div class="question-title">
      <strong>Title:</strong> {{ data.title }}
    </div>

    <mat-divider class="divider" style="background: #fff; height: 2px;"></mat-divider>

    <div class="question-content">
      <strong>Question:</strong>
      <div class="statement" [innerHTML]="data.statement"></div>
    </div>

    <mat-divider class="divider" style="background: #fff; height: 2px;"></mat-divider>

    <!-- Answers section -->
    <div class="answers-section">
      <strong>Answers:</strong>

      @if (loading) {
        <div class="loading-container">
          <mat-spinner diameter="24"></mat-spinner>
          <span>Loading answers...</span>
        </div>
      } @else if (error) {
        <div class="error-message">{{ error }}</div>
      } @else if (answers.length === 0) {
        <div class="empty-message" style="color: #fff;">No answers available for this question</div>
      } @else {
        <div class="answers-list">
          @for (answer of answers; track answer.id) {
            <div class="answer-item" [class.correct-answer]="answer.valid_answer">
              <div class="answer-label">{{ answer.label }}.</div>
              <div class="answer-text" [innerHTML]="answer.text"></div>
              @if (answer.valid_answer) {
                <div class="answer-status">
                  <mat-icon color="primary">check_circle</mat-icon>
                </div>
              }
            </div>
          }
        </div>
      }
    </div>
  </div>

  <!-- Dialog actions -->
  <div class="dialog-actions">
    <button mat-button (click)="close()">Close</button>
  </div>
</div>
