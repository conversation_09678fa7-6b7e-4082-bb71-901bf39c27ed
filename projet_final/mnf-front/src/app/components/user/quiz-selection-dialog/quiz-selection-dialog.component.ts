import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MaterialModules } from '../../../material/material';
import { ChapitreList } from '../lobby/chapitre-list/chapitre-list';
import { Chapter } from '../../../models/admin/Chapter';
import { QuizService } from '../../../services/user/quiz.service';
import { Quiz } from '../../../models/user/Quiz';
import { TranslatePipe } from '../../../pipes/translate.pipe';

@Component({
  selector: 'app-quiz-selection-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatSelectModule, MatFormFieldModule, MaterialModules, ChapitreList, TranslatePipe],
  templateUrl: './quiz-selection-dialog.component.html',
  styleUrls: ['./quiz-selection-dialog.component.css']
})
export class QuizSelectionDialogComponent implements OnInit {
  selectedChapter: Chapter | null = null;
  quizzes: Quiz[] = [];
  selectedQuizId: number | null = null;

  constructor(
    public dialogRef: MatDialogRef<QuizSelectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private quizService: QuizService
  ) {}

  ngOnInit(): void {
    this.loadQuizzes();
  }

  loadQuizzes(): void {
    this.quizService.getQuizzes().then(quizzes => {
      this.quizzes = quizzes;
    });
  }

  onQuizSelected(event: any): void {
    const quizId = event.value;
    const selectedQuiz = this.quizzes.find(quiz => quiz.id === quizId);

    if (selectedQuiz) {
      // Convert Quiz to Chapter for backward compatibility
      this.selectedChapter = {
        id: selectedQuiz.id,
        name: selectedQuiz.name,
        title: selectedQuiz.name,
        path: '',
        description: selectedQuiz.description
      };
    }
  }

  onChapterSelected(chapter: Chapter): void {
    this.selectedChapter = chapter;
    // Update the selected quiz ID to match the selected chapter
    this.selectedQuizId = chapter.id || null;
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  selectQuiz(): void {
    this.dialogRef.close(this.selectedChapter);
  }
}
