<div class="quiz-selection-dialog">
  <h2 mat-dialog-title><span style="color: #fff; margin-bottom: 20px;" >{{ 'quizSelectionDialog.title' | translate }}</span></h2>

  <mat-dialog-content>


    <app-chapitre-list [selectionMode]="true" (chapterSelected)="onChapterSelected($event)"></app-chapitre-list>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="closeDialog()">{{ 'common.cancel' | translate }}</button>
    <button mat-raised-button color="primary" [disabled]="!selectedChapter" (click)="selectQuiz()">{{ 'quizSelectionDialog.select' | translate }}</button>
  </mat-dialog-actions>
</div>
