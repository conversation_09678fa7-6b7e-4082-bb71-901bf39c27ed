.reponse-button {
  padding: 15px;
  font-size: 20px;
  background-color: var(--background-color);
  color: #284f4f;
  border: 2px solid var(--primary-color);
  width: 100%;
  box-shadow: 2px 3px 5px #24634353;
}

.reponse-button:hover {
  background-color: #ffd4d485;
  transition: all 0.2s;
}

.reponse-button.selected {
  background-color: var(--surface-color);
  transition: all 0.2s;
}

.reponse-button.correct {
  /* vert */
  border-color: #284f2f;
  color: var(--background-color);
  background-color: #4c8250;
  pointer-events: none;
}

.reponse-button.incorrect {
  /* rouge clair */
  background-color: var(--secondary-color);
  border-color: #ff002b;
  color: var(--background-color);
  pointer-events: none;
  transition: all 0.2s;
}

.reponse-button.missed {
  background-color: #acdca2; /* vert très clair */
  border-color: #284f2f;
  color: #284f2f;
  pointer-events: none;
  transition: all 0.2s;
}

.reponse-button.validated {
  pointer-events: none;
  transition: all 0.2s;
}
