<h2 mat-dialog-title class="dialog-title">
  <mat-icon class="title-icon">analytics</mat-icon>
  Series Statistics
</h2>

<mat-dialog-content class="mat-typography">
  <div class="stats-container">
    <!-- Key metrics cards -->
    <div class="stats-cards">
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="card-icon-container primary">
            <mat-icon>question_answer</mat-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ data.totalQuestions }}</div>
            <div class="card-label">Total Questions</div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="card-icon-container accent">
            <mat-icon>category</mat-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ data.uniqueChapters }}</div>
            <div class="card-label">Unique Chapters</div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="card-icon-container warn">
            <mat-icon>schedule</mat-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ estimatedTime }}</div>
            <div class="card-label">Estimated Time</div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="card-icon-container success">
            <mat-icon>functions</mat-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ data.avgQuestionsPerChapter }}</div>
            <div class="card-label">Avg. Questions/Chapter</div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Tabs for different statistics views -->
    <mat-tab-group animationDuration="300ms" class="stats-tabs">
      <!-- Chapter Distribution Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">pie_chart</mat-icon>
          Chapter Distribution
        </ng-template>

        <div class="tab-content">
          <!-- Chapter highlights -->
          <div class="highlights-container" *ngIf="data.mostRepresentedChapter && data.leastRepresentedChapter">
            <div class="highlight-item">
              <div class="highlight-header">
                <mat-icon class="highlight-icon primary">trending_up</mat-icon>
                <span>Most Represented</span>
              </div>
              <div class="highlight-content">
                <div class="highlight-title">{{ data.mostRepresentedChapter.name }}</div>
                <div class="highlight-value">
                  {{ data.mostRepresentedChapter.count }} questions
                  <span class="highlight-percentage">({{ data.mostRepresentedChapter.percentage }}%)</span>
                </div>
              </div>
            </div>

            <div class="highlight-item">
              <div class="highlight-header">
                <mat-icon class="highlight-icon warn">trending_down</mat-icon>
                <span>Least Represented</span>
              </div>
              <div class="highlight-content">
                <div class="highlight-title">{{ data.leastRepresentedChapter.name }}</div>
                <div class="highlight-value">
                  {{ data.leastRepresentedChapter.count }} questions
                  <span class="highlight-percentage">({{ data.leastRepresentedChapter.percentage }}%)</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Chapter breakdown -->
          <h3 class="section-title">
            <mat-icon class="section-icon">bar_chart</mat-icon>
            Chapter Breakdown
          </h3>
          <div class="chapter-stats">
            @for (chapter of data.chapterStats; track chapter.name) {
              <div class="chapter-stat-item">
                <div class="chapter-header">
                  <div class="chapter-name">{{ chapter.name }}</div>
                  <div class="chapter-count">
                    <span class="count-value">{{ chapter.count }}</span>
                    <span class="count-percentage">({{ chapter.percentage }}%)</span>
                  </div>
                </div>
                <div class="progress-container">
                  <div class="progress-bar">
                    <div class="progress-fill" [style.width.%]="chapter.percentage"></div>
                  </div>
                </div>
              </div>
            }
          </div>
        </div>
      </mat-tab>

      <!-- Difficulty Distribution Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">equalizer</mat-icon>
          Difficulty Level
        </ng-template>

        <div class="tab-content">
          <h3 class="section-title">
            <mat-icon class="section-icon">speed</mat-icon>
            Difficulty Distribution
          </h3>

          <div class="difficulty-container">
            <div class="difficulty-item">
              <div class="difficulty-header easy">
                <mat-icon>sentiment_very_satisfied</mat-icon>
                <span>Easy</span>
              </div>
              <div class="difficulty-percentage">{{ data.difficultyDistribution.easy }}%</div>
              <div class="progress-bar">
                <div class="progress-fill easy" [style.width.%]="data.difficultyDistribution.easy"></div>
              </div>
            </div>

            <div class="difficulty-item">
              <div class="difficulty-header medium">
                <mat-icon>sentiment_satisfied</mat-icon>
                <span>Medium</span>
              </div>
              <div class="difficulty-percentage">{{ data.difficultyDistribution.medium }}%</div>
              <div class="progress-bar">
                <div class="progress-fill medium" [style.width.%]="data.difficultyDistribution.medium"></div>
              </div>
            </div>

            <div class="difficulty-item">
              <div class="difficulty-header hard">
                <mat-icon>sentiment_very_dissatisfied</mat-icon>
                <span>Hard</span>
              </div>
              <div class="difficulty-percentage">{{ data.difficultyDistribution.hard }}%</div>
              <div class="progress-bar">
                <div class="progress-fill hard" [style.width.%]="data.difficultyDistribution.hard"></div>
              </div>
            </div>
          </div>

          <div class="note-text">
            <mat-icon class="note-icon">info</mat-icon>
            Note: Difficulty levels are estimated based on question complexity.
          </div>
        </div>
      </mat-tab>

      <!-- Quiz Overview Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">assessment</mat-icon>
          Quiz Overview
        </ng-template>

        <div class="tab-content">
          <div class="overview-container">
            <div class="overview-item">
              <mat-icon class="overview-icon">timer</mat-icon>
              <div class="overview-content">
                <div class="overview-label">Estimated Completion Time</div>
                <div class="overview-value">{{ estimatedTime }}</div>
              </div>
            </div>

            <div class="overview-item">
              <mat-icon class="overview-icon">balance</mat-icon>
              <div class="overview-content">
                <div class="overview-label">Chapter Balance</div>
                <div class="overview-value">
                  {{ data.uniqueChapters > 1 ?
                    (data.mostRepresentedChapter?.percentage || 0) - (data.leastRepresentedChapter?.percentage || 0) <= 20 ?
                    'Well Balanced' : 'Needs Balancing' :
                    'Single Chapter' }}
                </div>
              </div>
            </div>

            <div class="overview-item">
              <mat-icon class="overview-icon">school</mat-icon>
              <div class="overview-content">
                <div class="overview-label">Learning Coverage</div>
                <div class="overview-value">{{ data.uniqueChapters > 3 ? 'Comprehensive' : 'Focused' }}</div>
              </div>
            </div>
          </div>

          <div class="tips-container">
            <h3 class="section-title">
              <mat-icon class="section-icon">lightbulb</mat-icon>
              Quiz Tips
            </h3>
            <ul class="tips-list">
              <li>A well-balanced quiz should cover multiple chapters</li>
              <li>Aim for a mix of difficulty levels for better learning outcomes</li>
              <li>{{ data.totalQuestions < 10 ? 'Consider adding more questions for a comprehensive assessment' :
                    data.totalQuestions > 30 ? 'Quiz may be too long, consider splitting into multiple quizzes' :
                    'Quiz length is optimal for most learners' }}</li>
            </ul>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="onClose()">Close</button>
</mat-dialog-actions>

<style>
  .dialog-title {
    display: flex;
    align-items: center;
    color: var(--primary-color);
  }

  .title-icon {
    margin-right: 8px;
  }

  .stats-container {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 24px;
  }

  .stat-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .card-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 16px;
  }

  .card-icon-container.primary {
    background-color: rgba(63, 81, 181, 0.1);
    color: #3f51b5;
  }

  .card-icon-container.accent {
    background-color: rgba(255, 64, 129, 0.1);
    color: #ff4081;
  }

  .card-icon-container.warn {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
  }

  .card-icon-container.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
  }

  mat-card-content {
    display: flex;
    align-items: center;
    padding: 16px;
  }

  .card-content {
    display: flex;
    flex-direction: column;
  }

  .card-value {
    font-size: 24px;
    font-weight: 500;
  }

  .card-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }

  .stats-tabs {
    margin-top: 16px;
  }

  .tab-icon {
    margin-right: 8px;
  }

  .tab-content {
    padding: 16px 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin: 16px 0;
    font-size: 18px;
    color: var(--primary-color);
  }

  .section-icon {
    margin-right: 8px;
  }

  .highlights-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
  }

  .highlight-item {
    flex: 1;
    padding: 16px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    margin: 0 8px;
  }

  .highlight-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .highlight-icon {
    margin-right: 8px;
  }

  .highlight-icon.primary {
    color: #3f51b5;
  }

  .highlight-icon.warn {
    color: #f44336;
  }

  .highlight-title {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .highlight-percentage {
    color: rgba(0, 0, 0, 0.6);
    margin-left: 8px;
  }

  .chapter-stats {
    margin-top: 16px;
  }

  .chapter-stat-item {
    margin-bottom: 16px;
  }

  .chapter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .chapter-name {
    font-weight: 500;
  }

  .chapter-count {
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  .count-value {
    font-weight: 500;
  }

  .count-percentage {
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.6);
  }

  .progress-container {
    width: 100%;
  }

  .progress-bar {
    height: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background-color: var(--primary-color);
  }

  .progress-fill.easy {
    background-color: #4caf50;
  }

  .progress-fill.medium {
    background-color: #ff9800;
  }

  .progress-fill.hard {
    background-color: #f44336;
  }

  .difficulty-container {
    margin-top: 16px;
  }

  .difficulty-item {
    margin-bottom: 16px;
  }

  .difficulty-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .difficulty-header.easy {
    color: #4caf50;
  }

  .difficulty-header.medium {
    color: #ff9800;
  }

  .difficulty-header.hard {
    color: #f44336;
  }

  .difficulty-header mat-icon {
    margin-right: 8px;
  }

  .difficulty-percentage {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .note-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-top: 16px;
    padding: 8px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
  }

  .note-icon {
    margin-right: 8px;
    font-size: 18px;
    color: #ff9800;
  }

  .overview-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
  }

  .overview-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
  }

  .overview-icon {
    margin-right: 16px;
    color: var(--primary-color);
  }

  .overview-label {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .overview-value {
    color: rgba(0, 0, 0, 0.87);
  }

  .tips-container {
    margin-top: 24px;
  }

  .tips-list {
    padding-left: 24px;
    margin-top: 8px;
  }

  .tips-list li {
    margin-bottom: 8px;
    color: rgba(0, 0, 0, 0.87);
  }

  @media (max-width: 600px) {
    .stats-cards {
      grid-template-columns: 1fr;
    }

    .highlights-container {
      flex-direction: column;
    }

    .highlight-item {
      margin: 0 0 16px 0;
    }
  }
</style>
