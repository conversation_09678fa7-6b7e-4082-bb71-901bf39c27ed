import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-series-statistics-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatDialogModule,
    MatDividerModule,
    MatIconModule,
    MatTabsModule,
    MatCardModule,
    MatTooltipModule
  ],
  templateUrl: './series-statistics-dialog.html',
})
export class SeriesStatisticsDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<SeriesStatisticsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      totalQuestions: number;
      chapterStats: Array<{
        name: string;
        count: number;
        percentage: number;
      }>;
      uniqueChapters: number;
      avgQuestionsPerChapter: number;
      mostRepresentedChapter: {
        name: string;
        count: number;
        percentage: number;
      } | null;
      leastRepresentedChapter: {
        name: string;
        count: number;
        percentage: number;
      } | null;
      difficultyDistribution: {
        easy: number;
        medium: number;
        hard: number;
      };
      estimatedTimeMinutes: number;
    }
  ) {}

  // Get estimated time in a readable format
  get estimatedTime(): string {
    const minutes = this.data.estimatedTimeMinutes;
    if (minutes < 60) {
      return `${minutes} minutes`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0
        ? `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`
        : `${hours} hour${hours > 1 ? 's' : ''}`;
    }
  }

  onClose(): void {
    this.dialogRef.close();
  }
}
