import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatListModule } from '@angular/material/list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FormsModule } from '@angular/forms';

import { FavoritesService } from '../../../services/favorites/favorites.service';
import { Favorite } from '../../../services/favorites/favorites.service';
import { QuizService } from '../../../services/user/quiz.service';

@Component({
  selector: 'app-share-quiz-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatDialogModule,
    MatDividerModule,
    MatIconModule,
    MatCheckboxModule,
    MatListModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './share-quiz-dialog.html',
})
export class ShareQuizDialogComponent {
  favorites: Favorite[] = [];
  selectedFavorites: { [id: number]: boolean } = {};
  isLoading = true;
  error = '';

  constructor(
    public dialogRef: MatDialogRef<ShareQuizDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      quizId: number;
      quizName: string;
    },
    private favoritesService: FavoritesService,
    private quizService: QuizService,
    private snackBar: MatSnackBar
  ) {
    this.loadFavorites();
  }

  loadFavorites(): void {
    this.isLoading = true;
    this.error = '';

    this.favoritesService.getCurrentUserFavorites()
      .then(favorites => {
        this.favorites = favorites;
        this.isLoading = false;
      })
      .catch(error => {
        this.error = 'Failed to load favorites';
        this.isLoading = false;
        console.error('Error loading favorites:', error);
      });
  }

  toggleSelection(favoriteId: number): void {
    this.selectedFavorites[favoriteId] = !this.selectedFavorites[favoriteId];
  }

  isSelected(favoriteId: number): boolean {
    return this.selectedFavorites[favoriteId] === true;
  }

  getSelectedFavorites(): Favorite[] {
    return this.favorites.filter(favorite => this.selectedFavorites[favorite.id]);
  }

  shareQuiz(): void {
    const selectedFavorites = this.getSelectedFavorites();
    if (selectedFavorites.length === 0) {
      this.snackBar.open('Please select at least one favorite to share with', 'Close', {
        duration: 3000
      });
      return;
    }

    // Set loading state
    this.isLoading = true;

    // Extract the favorite user IDs
    const favoriteUserIds = selectedFavorites.map(favorite => favorite.favoriteUserId);

    // Call the quiz service to share the quiz
    this.quizService.shareQuiz(this.data.quizId, favoriteUserIds)
      .then(success => {
        this.isLoading = false;
        if (success) {
          // Close the dialog and return the selected favorites for display in the parent component
          this.dialogRef.close(selectedFavorites);
        } else {
          this.snackBar.open('Failed to share quiz. Please try again.', 'Close', {
            duration: 3000
          });
        }
      })
      .catch(error => {
        this.isLoading = false;
        console.error('Error sharing quiz:', error);
        this.snackBar.open('An error occurred while sharing the quiz.', 'Close', {
          duration: 3000
        });
      });
  }

  onClose(): void {
    this.dialogRef.close();
  }
}
