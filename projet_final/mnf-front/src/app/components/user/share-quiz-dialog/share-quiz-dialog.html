<h2 mat-dialog-title class="dialog-title">
  Share Quiz: {{ data.quizName }}
</h2>

<mat-dialog-content class="mat-typography">
  <div class="share-container">
    <!-- Loading indicator -->
    @if (isLoading) {
    <div class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading favorites...</p>
    </div>
    }

    <!-- Error message -->
    @if (error && !isLoading) {
    <div class="error-container">
      <mat-icon class="error-icon">error</mat-icon>
      <p class="error-message">{{ error }}</p>
      <button mat-button color="primary" (click)="loadFavorites()">Retry</button>
    </div>
    }

    <!-- Empty state -->
    @if (!isLoading && !error && favorites.length === 0) {
    <div class="empty-state">
      <mat-icon class="empty-icon">people</mat-icon>
      <p class="empty-state-text">No favorites found.</p>
      <p class="empty-state-subtext">Add favorites to your profile to share quizzes with them.</p>
    </div>
    }

    <!-- Favorites list -->
    @if (!isLoading && !error && favorites.length > 0) {
    <div class="favorites-list-container">
      <p class="list-description">Select the favorites you want to share this quiz with:</p>

      <mat-list class="favorites-list">
        @for (favorite of favorites; track favorite.id) {
        <mat-list-item class="favorite-item">
          <div class="favorite-checkbox">
            <mat-checkbox
              [checked]="isSelected(favorite.id)"
              (change)="toggleSelection(favorite.id)"
            ></mat-checkbox>
          </div>
          <div class="favorite-info">
            <div class="favorite-name">{{ favorite.favoriteUserName }}</div>
            <div class="favorite-email">{{ favorite.favoriteUserEmail }}</div>
          </div>
        </mat-list-item>
        }
      </mat-list>
    </div>
    }
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="onClose()">Cancel</button>
  <button
    mat-raised-button
    color="primary"
    (click)="shareQuiz()"
    [disabled]="isLoading || favorites.length === 0"
  >
    Share
  </button>
</mat-dialog-actions>

<style>
  .dialog-title {
    display: flex;
    align-items: center;
    color: var(--primary-color);
  }

  .share-container {
    padding: 0;
    max-height: 60vh;
    overflow-y: auto;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
  }

  .loading-container p {
    margin-top: 16px;
    color: rgba(0, 0, 0, 0.6);
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    text-align: center;
  }

  .error-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    color: #f44336;
    margin-bottom: 16px;
  }

  .error-message {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.87);
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    text-align: center;
  }

  .empty-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    color: rgba(0, 0, 0, 0.3);
    margin-bottom: 16px;
  }

  .empty-state-text {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
    color: rgba(0, 0, 0, 0.87);
  }

  .empty-state-subtext {
    color: rgba(0, 0, 0, 0.6);
  }

  .favorites-list-container {
    padding: 16px 0;
  }

  .list-description {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.87);
  }

  .favorites-list {
    padding: 0;
  }

  .favorite-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  .favorite-checkbox {
    margin-right: 16px;
  }

  .favorite-info {
    display: flex;
    flex-direction: column;
  }

  .favorite-name {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.87);
  }

  .favorite-email {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }
</style>
