.dialog-container {
  display: flex;
  flex-direction: column;
  padding: 0;
  max-height: 80vh;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--primary-color);
  color: white;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.dialog-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

.close-button {
  color: white;
}

.channel-name-container {
  padding: 16px 24px 0;
}

.channel-name-field {
  width: 100%;
}

.channel-info {
  padding: 16px 24px 0;
}

.channel-info h3 {
  margin: 0;
  font-size: 18px;
  color: var(--primary-color);
}

.search-container {
  padding: 16px 24px 8px;
}

.search-field {
  width: 100%;
}

.section-container {
  padding: 0 24px 16px;
  overflow: hidden;
}

.section-title {
  margin: 16px 0 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-color);
}

.user-list {
  max-height: 200px;
  overflow-y: auto;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.user-item:last-child {
  border-bottom: none;
}

.user-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.user-item.selected {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.user-email {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
}

.user-action {
  margin-left: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 24px;
}

.error-message {
  color: var(--warn-color);
  padding: 16px;
  text-align: center;
}

.empty-message {
  padding: 16px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 24px 16px;
  gap: 8px;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.button-spinner {
  margin-right: 8px;
}

/* Scrollbar styling */
.user-list::-webkit-scrollbar {
  width: 6px;
}

.user-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.user-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.user-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
