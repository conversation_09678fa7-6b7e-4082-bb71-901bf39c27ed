.card {
  background-color: #f5f5f5;
  color: var(--text-color);
  border-radius: 12px;
  padding: 16px;
  margin: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: 0.3s ease;
}

.card:hover,
.card.selected {
  background-color: var(--secondary-color);
  color: rgb(252, 249, 244);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.card.selected:hover {
  background-color: #942c3d;
}
