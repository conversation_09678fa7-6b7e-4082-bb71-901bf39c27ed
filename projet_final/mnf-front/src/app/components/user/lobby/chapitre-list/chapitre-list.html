<div class="container-chapitres">
  <div class="search-bar">
    <input
      type="search"
      placeholder="Rechercher un quiz"
      class="search-field"
      style="margin-bottom: 12px"
      [(ngModel)]="searchTerm"
    />
  </div>

  <div class="quiz-grid">
    @for (quiz of filteredChapitres(); track quiz.id) {
    <app-chapitre-card
      [chapitre]="quiz"
      (click)="selectionMode ? selectChapter(quiz) : openChapterDialog(quiz)"
      [selected]="quiz.id === selectedQuizId()"
    ></app-chapitre-card>
    }
  </div>
</div>
