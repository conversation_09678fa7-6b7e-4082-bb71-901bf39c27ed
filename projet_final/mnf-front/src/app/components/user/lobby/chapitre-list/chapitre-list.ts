import { Component, computed, signal, OnInit, Output, EventEmitter, Input } from "@angular/core";
import { MaterialModules } from "../../../../material/material";
import { Chapter } from "../../../../models/admin/Chapter";
import { ChapitreCard } from "../chapitre-card/chapitre-card";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { ChapterService } from "../../../../services/admin/chapter.service";
import { QuestionService } from "../../../../services/admin/question.service";
import { Question } from "../../../../models/admin/Question";
import { MatDialog, MatDialogModule } from "@angular/material/dialog";
import { ChapterDialogComponent } from "../../../../components/user/lobby/chapter-dialog/chapter-dialog.component";
import { QuizService } from "../../../../services/user/quiz.service";
import { Quiz } from "../../../../models/user/Quiz";
import { TranslatePipe } from "../../../../pipes/translate.pipe";

@Component({
  selector: "app-chapitre-list",
  standalone: true,
  imports: [CommonModule, FormsModule, MaterialModules, ChapitreCard, MatDialogModule, TranslatePipe],
  templateUrl: "./chapitre-list.html",
  styleUrl: "./chapitre-list.css",
})
export class ChapitreList implements OnInit {
  searchTerm = signal("");
  selectedChapitres = signal<Chapter[]>([]);
  chapitres: Chapter[] = [];
  parentlessChapitres = signal<Chapter[]>([]);
  quizzes = signal<Quiz[]>([]);
  selectedQuizId = signal<number | undefined>(undefined);
  @Output() chapterSelected = new EventEmitter<Chapter>();
  // If true, clicking a chapter card will emit the selected chapter instead of opening the dialog
  @Input() selectionMode = false;

  constructor(
    public chapterService: ChapterService,
    private questionService: QuestionService,
    private dialog: MatDialog,
    private quizService: QuizService
  ) {}

  ngOnInit() {
    this.loadQuizzes();
  }

  loadQuizzes() {
    this.quizService.getQuizzes().then((quizzes) => {
      this.quizzes.set(quizzes);
    });
  }

  updateParentlessChapitres() {
    const parentless = this.chapitres.filter(
      (chapitre) => !chapitre.parent_path && !chapitre.parentPath && !chapitre.parentName
    );
    this.parentlessChapitres.set(parentless);
  }

  filteredChapitres = computed(() => {
    const term = this.searchTerm().toLowerCase().trim();
    const userQuizzes = this.quizzes();

    return userQuizzes.filter((quiz) => {
      // Filter out quizzes with no questions
      if (!quiz.quizQuestions || quiz.quizQuestions.length === 0) {
        return false;
      }
      return quiz.name.toLowerCase().includes(term);
    });
  });

  addChapitre(chapitre: Chapter) {
    this.selectedChapitres.update((chapitres) => [...chapitres, chapitre]);
  }

  removeChapitre(chapitre: Chapter) {
    this.selectedChapitres.update((chapitres) => {
      const index = chapitres.indexOf(chapitre);
      if (index !== -1) {
        return [...chapitres.slice(0, index), ...chapitres.slice(index + 1)];
      }
      return chapitres;
    });
  }

  openChapterDialog(quiz: Quiz) {
    // For now, we'll keep this method but adapt it to work with Quiz objects
    // In a real implementation, you might want to create a QuizDialogComponent
    const chapter: Chapter = {
      id: quiz.id,
      name: quiz.name,
      title: quiz.name,
      path: '',
      description: quiz.description
    };

    // Set the selected quiz ID
    this.selectedQuizId.set(quiz.id);

    this.dialog.open(ChapterDialogComponent, {
      width: '80%',
      maxWidth: '800px',
      data: { chapter }
    });
  }

  selectChapter(quiz: Quiz) {
    // Convert Quiz to Chapter for backward compatibility
    const chapter: Chapter = {
      id: quiz.id,
      name: quiz.name,
      title: quiz.name,
      path: '',
      description: quiz.description
    };

    // Set the selected quiz ID
    this.selectedQuizId.set(quiz.id);

    this.chapterSelected.emit(chapter);
  }
}
