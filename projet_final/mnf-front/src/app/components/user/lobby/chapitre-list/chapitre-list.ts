import { Component, computed, signal, OnInit } from "@angular/core";
import { MaterialModules } from "../../../../material/material";
import { Chapter } from "../../../../models/admin/Chapter";
import { ChapitreCard } from "../chapitre-card/chapitre-card";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { ChapterService } from "../../../../services/admin/chapter.service";
import { QuestionService } from "../../../../services/admin/question.service";
import { Question } from "../../../../models/admin/Question";
import { MatDialog, MatDialogModule } from "@angular/material/dialog";
import { ChapterDialogComponent } from "../../../../components/user/lobby/chapter-dialog/chapter-dialog.component";

@Component({
  selector: "app-chapitre-list",
  standalone: true,
  imports: [CommonModule, FormsModule, MaterialModules, ChapitreCard, MatDialogModule],
  templateUrl: "./chapitre-list.html",
  styleUrl: "./chapitre-list.css",
})
export class ChapitreList implements OnInit {
  searchTerm = signal("");
  selectedChapitres = signal<Chapter[]>([]);
  chapitres: Chapter[] = [];
  parentlessChapitres = signal<Chapter[]>([]);

  constructor(
    public chapterService: ChapterService,
    private questionService: QuestionService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.loadChapters();
  }

  loadChapters() {
    this.chapterService.getChapters().then((chaps) => {
      this.chapitres = chaps;
      this.updateParentlessChapitres();
    });
  }

  updateParentlessChapitres() {
    const parentless = this.chapitres.filter(
      (chapitre) => !chapitre.parent_path && !chapitre.parentPath && !chapitre.parentName
    );
    this.parentlessChapitres.set(parentless);
  }

  filteredChapitres = computed(() => {
    const term = this.searchTerm().toLowerCase().trim();
    const parentless = this.parentlessChapitres();

    return parentless.filter((chapitre) => {
      return chapitre.name.toLowerCase().includes(term);
    });
  });

  addChapitre(chapitre: Chapter) {
    this.selectedChapitres.update((chapitres) => [...chapitres, chapitre]);
  }

  removeChapitre(chapitre: Chapter) {
    this.selectedChapitres.update((chapitres) => {
      const index = chapitres.indexOf(chapitre);
      if (index !== -1) {
        return [...chapitres.slice(0, index), ...chapitres.slice(index + 1)];
      }
      return chapitres;
    });
  }

  openChapterDialog(chapter: Chapter) {
    this.dialog.open(ChapterDialogComponent, {
      width: '80%',
      maxWidth: '800px',
      data: { chapter }
    });
  }
}
