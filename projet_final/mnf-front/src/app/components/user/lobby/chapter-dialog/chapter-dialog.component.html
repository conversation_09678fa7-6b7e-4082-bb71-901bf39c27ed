<div class="chapter-dialog">
  <h2 mat-dialog-title>{{ data.chapter.name }}</h2>

  <mat-dialog-content>
    @if (loading()) {
      <div class="loading-spinner">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Chargement des chapitres et questions...</p>
      </div>
    } @else {
      <!-- Parent chapter questions -->
      @if (data.chapter.id && chapterQuestions()[data.chapter.id].length) {
        <div class="questions-section">
          <h3>Questions du chapitre</h3>
          <div class="questions-list">
            @for (question of chapterQuestions()[data.chapter.id]; track question.id) {
              <div class="question-item">
                <h4>{{ question.title }}</h4>
                <p>{{ question.statement }}</p>
              </div>
            }
          </div>
        </div>
      }

      <!-- Sub-chapters with accordion -->
      @if (subChapters().length > 0) {
        <div class="sub-chapters-section">
          <h3>Sous-chapitres</h3>

          <mat-accordion>
            @for (subChapter of subChapters(); track subChapter.id) {
              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    {{ subChapter.name }}
                  </mat-panel-title>
                </mat-expansion-panel-header>

                <!-- Sub-chapter content -->
                @if (subChapter.content) {
                  <div class="sub-chapter-content">
                    {{ subChapter.content }}
                  </div>
                }

                <!-- Sub-chapter questions -->
                @if (subChapter.id && chapterQuestions()[subChapter.id].length) {
                  <div class="questions-list">
                    <h4>Questions</h4>
                    @for (question of chapterQuestions()[subChapter.id]; track question.id) {
                      <div class="question-item">
                        <h5>{{ question.title }}</h5>
                        <p>{{ question.statement }}</p>
                      </div>
                    }
                  </div>
                } @else {
                  <p class="no-questions">Aucune question pour ce sous-chapitre</p>
                }
              </mat-expansion-panel>
            }
          </mat-accordion>
        </div>
      } @else {
        <p class="no-sub-chapters">Aucun sous-chapitre trouvé</p>
      }
    }
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="closeDialog()">Fermer</button>
  </mat-dialog-actions>
</div>
