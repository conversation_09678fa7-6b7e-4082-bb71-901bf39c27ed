import { Component, Inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { Chapter } from '../../../../models/admin/Chapter';
import { Question } from '../../../../models/admin/Question';
import { ChapterService } from '../../../../services/admin/chapter.service';
import { QuestionService } from '../../../../services/admin/question.service';
import { MaterialModules } from '../../../../material/material';
import { TranslatePipe } from '../../../../pipes/translate.pipe';

@Component({
  selector: 'app-chapter-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatExpansionModule, MaterialModules, TranslatePipe],
  templateUrl: './chapter-dialog.component.html',
  styleUrls: ['./chapter-dialog.component.css']
})
export class ChapterDialogComponent implements OnInit {
  subChapters = signal<Chapter[]>([]);
  chapterQuestions = signal<{ [chapterId: number]: Question[] }>({});
  loading = signal(true);

  constructor(
    public dialogRef: MatDialogRef<ChapterDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { chapter: Chapter },
    private chapterService: ChapterService,
    private questionService: QuestionService
  ) {}

  ngOnInit(): void {
    this.loadSubChapters();
  }

  loadSubChapters(): void {
    if (!this.data.chapter || !this.data.chapter.path) {
      this.loading.set(false);
      return;
    }

    this.chapterService.getChaptersByParentPath(this.data.chapter.path)
      .then(subChapters => {
        this.subChapters.set(subChapters);

        // Load questions for the parent chapter
        if (this.data.chapter.id) {
          this.loadQuestionsForChapter(this.data.chapter.id);
        }

        // Load questions for each sub-chapter
        const promises = subChapters.map(subChapter => {
          if (subChapter.id) {
            return this.loadQuestionsForChapter(subChapter.id);
          }
          return Promise.resolve();
        });

        return Promise.all(promises);
      })
      .finally(() => {
        this.loading.set(false);
      });
  }

  loadQuestionsForChapter(chapterId: number): Promise<void> {
    return this.questionService.getQuestionsByChapterId(chapterId)
      .then(questions => {
        this.chapterQuestions.update(current => ({
          ...current,
          [chapterId]: questions
        }));
      });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }
}
