/* Dialog container */
.dialog-container {
  display: flex;
  flex-direction: column;
  padding: 0;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

/* Dialog header */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--primary-color, #427373);
  color: white;
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
}

.close-button {
  color: white;
}

/* Search container */
.search-container {
  padding: 16px 24px 0;
}

.search-field {
  width: 100%;
}

/* Section container */
.section-container {
  padding: 16px 24px;
  overflow-y: auto;
  max-height: 200px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary, #333);
}

/* User list */
.user-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: var(--bg-card, #f5f5f5);
  transition: background-color 0.2s;
}

.user-item:hover {
  background-color: var(--bg-hover, #e0e0e0);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary, #333);
}

.user-email {
  font-size: 0.875rem;
  color: var(--text-secondary, #666);
}

/* Loading container */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 16px;
}

/* Error message */
.error-message {
  color: var(--error-color, #f44336);
  padding: 8px 0;
}

/* Empty message */
.empty-message {
  color: var(--text-secondary, #666);
  padding: 8px 0;
  font-style: italic;
}

/* Dialog actions */
.dialog-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 24px 16px;
  border-top: 1px solid var(--border-color, #e0e0e0);
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .section-container {
    max-height: 150px;
  }

  .user-item {
    padding: 8px;
  }

  .user-name, .user-email {
    font-size: 0.9rem;
  }
}
