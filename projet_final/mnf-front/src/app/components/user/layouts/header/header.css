/* Header styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  padding: 10px;
  background-color: var(--surface-color);
  color: var(--text-color);
  box-shadow: 0 2px 5px var(--shadow-color);
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Sidenav container styles */
.sidenav-container {
  position: absolute; /* Absolute position */
  top: 64px; /* Position below the header */
  left: 0;
  height: calc(100vh - 64px); /* Full height minus header */
  width: 100%;
  z-index: 999; /* Just below the header */
  pointer-events: none; /* Allow clicks to pass through to elements below */
  overflow: hidden;
}

/* Sidenav styles */
.sidenav {
  width: 250px;
  height: 100%;
  background-color: var(--surface-color);
  color: var(--text-color);
  box-shadow: 2px 0 5px var(--shadow-color);
  pointer-events: auto; /* Restore pointer events for the sidenav */
  position: absolute;
  left: 0;
  top: 0;
  overflow-y: auto;
}

/* Sidenav content styles */
.mat-sidenav-content {
  pointer-events: none; /* Allow clicks to pass through */
}

/* Overlay for sidenav */
.sidenav-overlay {
  position: fixed;
  top: 64px; /* Position below the header */
  left: 0;
  width: 100%;
  height: calc(100vh - 64px);
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998; /* Below sidenav but above content */
  pointer-events: auto; /* Capture clicks */
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: bold;
  transition: color 0.3s ease;
}

.logo:hover {
  color: var(--secondary-color);
}

.spacer {
  flex: 1 1 auto;
}

.nav-links {
  display: flex;
  align-items: center;
}

.nav-links a {
  margin: 0 8px;
  color: var(--text-color);
  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-links a:hover {
  background-color: var(--primary-color);
  color: white;
}

.menu-button {
  display: none;
}

.learn-button {
  background-color: var(--primary-color);
  color: white;
  margin-right: 20px;
}

/* Language selector styles */
.language-selector {
  margin-right: 8px;
}

.profile-button {
  margin-right: 8px;
  color: #c94d61;
}

/* Profile menu styles */
::ng-deep .mat-mdc-menu-panel.mat-menu-panel {
  min-width: 180px;
}

::ng-deep .mat-mdc-menu-item .mat-icon {
  color: #c94d61;
  margin-right: 8px;
}

::ng-deep .mat-mdc-menu-item:hover {
  background-color: rgba(201, 77, 97, 0.1);
}

.flag-icon {
  width: 24px;
  height: 16px;
  border-radius: 2px;
  object-fit: cover;
}

/* Responsive styles */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .menu-button {
    display: block;
  }
}
