<!-- Header section positioned at top left -->
<header class="header">
  <div class="toolbar">
    <div class="logo-container">
      <a routerLink="/" class="logo">{{ 'app.title' | translate }}</a>
    </div>
    <span class="spacer"></span>
    <div class="nav-links">
      @for (link of navLinks; track link.path) {
        <a mat-button [routerLink]="link.path">{{ 'nav.' + link.label.toLowerCase() | translate }}</a>
      }
    </div>
    <a mat-raised-button class="learn-button">
      Apprendre
      <mat-icon>play_arrow</mat-icon>
    </a>

    @if (isAuthenticated) {
      <button mat-icon-button [matMenuTriggerFor]="profileMenu" class="profile-button" title="{{ 'nav.profile' | translate }}">
        <mat-icon>account_circle</mat-icon>
      </button>
      <mat-menu #profileMenu="matMenu">
        <button mat-menu-item routerLink="/profile">
          <mat-icon>person</mat-icon>
          <span>{{ 'profile.title' | translate }}</span>
        </button>
        <button mat-menu-item routerLink="/favorites">
          <mat-icon>star</mat-icon>
          <span>{{ 'favorites.title' | translate }}</span>
        </button>
        <button mat-menu-item (click)="logout()">
          <mat-icon>exit_to_app</mat-icon>
          <span>{{ 'profile.logout' | translate }}</span>
        </button>
      </mat-menu>
    }

    <button mat-icon-button [matMenuTriggerFor]="languageMenu" class="language-selector">
      <img src="assets/flags/{{currentLanguage.flag}}.svg" alt="{{currentLanguage.name}}" class="flag-icon">
    </button>
    <mat-menu #languageMenu="matMenu">
      @for (lang of languages; track lang.code) {
        <button mat-menu-item (click)="changeLanguage(lang.code)">
          <img src="assets/flags/{{lang.flag}}.svg" alt="{{lang.name}}" class="flag-icon">
          <span>{{ lang.name }}</span>
        </button>
      }
    </mat-menu>

    <app-theme-toggle (themeChanged)="onThemeChanged($event)"></app-theme-toggle>

    <button mat-icon-button class="menu-button" (click)="toggleSidenav()">
      <mat-icon>menu</mat-icon>
    </button>
  </div>
</header>

<!-- Sidenav as a completely separate section with absolute positioning -->
@if (sidenavOpened) {
<div class="sidenav-overlay" (click)="toggleSidenav()"></div>
<mat-sidenav-container class="sidenav-container">
  <mat-sidenav
    #sidenav
    [opened]="sidenavOpened"
    [mode]="'over'"
    class="sidenav"
  >
    <mat-toolbar i18n="@@menuTitle">Menu</mat-toolbar>
    <mat-nav-list>
      @for (link of navLinks; track link.path) {
      <a mat-list-item [routerLink]="link.path" (click)="toggleSidenav()">
        {{ 'nav.' + link.label.toLowerCase() | translate }}
      </a>
      }
    </mat-nav-list>
  </mat-sidenav>
  <mat-sidenav-content>
    <!-- Empty content, just to satisfy the Angular Material requirements -->
  </mat-sidenav-content>
</mat-sidenav-container>
}
