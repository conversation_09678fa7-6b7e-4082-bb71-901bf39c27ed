import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-random-questions-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule
  ],
  templateUrl: './random-questions-dialog.html',
})
export class RandomQuestionsDialogComponent {
  count: number = 3;

  constructor(
    public dialogRef: MatDialogRef<RandomQuestionsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { maxCount: number }
  ) {
    // Set the default count to the minimum of 3 or the maximum available
    this.count = Math.min(3, data.maxCount);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onConfirm(): void {
    this.dialogRef.close(this.count);
  }
}
