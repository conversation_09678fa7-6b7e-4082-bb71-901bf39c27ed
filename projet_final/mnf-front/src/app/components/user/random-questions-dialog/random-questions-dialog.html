<div class="random-questions-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>Add Random Questions</h2>
  </div>

  <mat-dialog-content>
    <div class="dialog-content">
      <div class="description-text">
        <p>How many random questions would you like to add to your quiz?</p>
        <p *ngIf="data.maxCount < 3" class="warning-message">
          <mat-icon>warning</mat-icon>
          <span>Only {{ data.maxCount }} question(s) available in this chapter.</span>
        </p>
      </div>

      <div class="input-container">
        <mat-form-field appearance="outline" class="question-count-field">
          <mat-label>Number of questions</mat-label>
          <input
            matInput
            type="number"
            [(ngModel)]="count"
            min="1"
            [max]="data.maxCount"
            class="count-input">
          <mat-hint>Maximum available: {{ data.maxCount }}</mat-hint>
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <div class="dialog-actions">
      <button
        mat-button
        class="cancel-button"
        (click)="onCancel()">
        Cancel
      </button>
      <button
        mat-raised-button
        color="primary"
        class="confirm-button"
        (click)="onConfirm()"
        [disabled]="count < 1 || count > data.maxCount">
        Add Questions
      </button>
    </div>
  </mat-dialog-actions>
</div>

<style>
  /* Main dialog container */
  .random-questions-dialog {
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    color: var(--text-color);
    border-radius: 12px;
    overflow: hidden;
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Dialog header */
  .dialog-header {
    background: linear-gradient(135deg, var(--primary-color), var(--surface-color));
    padding: 20px 24px;
    position: relative;
  }

  .dialog-header h2 {
    color: white;
    margin: 0;
    font-size: 22px;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Dialog content */
  .dialog-content {
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .description-text {
    font-size: 16px;
    line-height: 1.5;
    color: var(--text-color);
  }

  .description-text p {
    margin: 0 0 12px 0;
  }

  .description-text p:last-child {
    margin-bottom: 0;
  }

  /* Warning message */
  .warning-message {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--error-color);
    background-color: rgba(244, 67, 54, 0.08);
    padding: 12px;
    border-radius: 8px;
    margin-top: 12px !important;
    font-size: 14px;
  }

  .warning-message mat-icon {
    font-size: 20px;
    height: 20px;
    width: 20px;
    color: var(--error-color);
  }

  /* Input container */
  .input-container {
    margin-top: 8px;
  }

  .question-count-field {
    width: 100%;
  }

  .count-input {
    font-size: 16px;
    color: var(--text-color);
  }

  /* Dialog actions */
  .dialog-actions {
    display: flex;
    gap: 12px;
    padding: 8px 24px 24px;
    width: 100%;
    justify-content: flex-end;
  }

  .cancel-button {
    color: var(--text-color);
    opacity: 0.8;
    transition: opacity 0.2s ease;
  }

  .cancel-button:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.05);
  }

  .confirm-button {
    background-color: var(--primary-color);
    color: white;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(66, 115, 115, 0.3);
  }

  .confirm-button:hover:not([disabled]) {
    background-color: var(--primary-color);
    filter: brightness(1.1);
    box-shadow: 0 4px 12px rgba(66, 115, 115, 0.4);
    transform: translateY(-1px);
  }

  .confirm-button:active:not([disabled]) {
    transform: translateY(1px);
    box-shadow: 0 1px 4px rgba(66, 115, 115, 0.2);
  }

  .confirm-button[disabled] {
    background-color: rgba(0, 0, 0, 0.12);
    color: rgba(0, 0, 0, 0.38);
    box-shadow: none;
  }

  /* Override Material styles */
  ::ng-deep .mat-mdc-dialog-container {
    padding: 0 !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
    overflow: hidden !important;
  }

  ::ng-deep .mat-mdc-dialog-surface {
    border-radius: 12px !important;
    overflow: hidden !important;
  }

  ::ng-deep .mat-mdc-form-field-outline {
    color: var(--border-color) !important;
  }

  ::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline {
    color: var(--primary-color) !important;
  }

  ::ng-deep .mat-mdc-form-field-hint {
    color: var(--text-color) !important;
    opacity: 0.6;
  }

  ::ng-deep .mat-mdc-dialog-content {
    margin: 0 !important;
    padding: 0 !important;
    max-height: none !important;
  }

  ::ng-deep .mat-mdc-dialog-actions {
    padding: 0 !important;
    min-height: auto !important;
    margin-bottom: 0 !important;
    border-top: none !important;
  }

  /* Responsive styles */
  @media (max-width: 480px) {
    .dialog-header {
      padding: 16px 20px;
    }

    .dialog-header h2 {
      font-size: 20px;
    }

    .dialog-content {
      padding: 20px;
      gap: 16px;
    }

    .dialog-actions {
      padding: 8px 20px 20px;
      flex-direction: column-reverse;
      gap: 8px;
    }

    .cancel-button, .confirm-button {
      width: 100%;
    }
  }
</style>
