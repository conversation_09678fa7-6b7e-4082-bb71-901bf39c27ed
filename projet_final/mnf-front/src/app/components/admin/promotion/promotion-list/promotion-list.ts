import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Promotion } from '../../../../models/admin/Promotion';
import { PromotionService } from '../../../../services/admin/promotion.service';
import { NotificationService } from '../../../../services/notification.service';

@Component({
  selector: 'app-promotion-list',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './promotion-list.html',
  styleUrl: './promotion-list.css'
})
export class PromotionListComponent implements OnInit, OnChanges {
  promotions: Promotion[] = [];
  loading = true;
  error = '';

  // Search input
  @Input() searchTerm = '';

  // Pagination inputs from parent
  @Input() currentPage = 1;
  @Input() pageSize = 10;
  @Input() sortBy = 'id';

  // Pagination outputs to parent
  @Output() paginationChange = new EventEmitter<{totalItems: number, totalPages: number}>();

  // Search output
  @Output() searchChange = new EventEmitter<string>();

  // Calculated pagination values
  totalItems = 0;
  totalPages = 0;

  // Make Math available in the template
  Math = Math;

  constructor(
    private promotionService: PromotionService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadPromotions();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // React to changes in pagination inputs or search term
    if ((changes['currentPage'] && !changes['currentPage'].firstChange) ||
        (changes['pageSize'] && !changes['pageSize'].firstChange) ||
        (changes['searchTerm'] && !changes['searchTerm'].firstChange)) {
      this.loadPromotions();
    }
  }

  loadPromotions(): void {
    this.loading = true;
    // Convert from 1-based (UI) to 0-based (API) pagination
    // UI shows page numbers starting from 1, but API expects page numbers starting from 0
    const backendPage = this.currentPage - 1;

    // First get the total count from the /total endpoint
    this.promotionService.getPromotionsTotal()
      .then(total => {
        this.totalItems = total;
        this.totalPages = Math.ceil(total / this.pageSize);

        // Emit pagination info to parent component
        this.paginationChange.emit({
          totalItems: this.totalItems,
          totalPages: this.totalPages
        });

        // Then get the paginated data
        return this.promotionService.getPromotions(this.searchTerm, backendPage, this.pageSize, this.sortBy);
      })
      .then(result => {
        this.promotions = result.content;
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load promotions. Please try again.';
        this.loading = false;
        console.error('Error loading promotions:', error);
      });
  }

  onSearch(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.searchTerm = value;
    this.searchChange.emit(value);
    this.currentPage = 1; // Reset to first page when searching
    this.loadPromotions();
  }

  deletePromotion(id: number): void {
    if (confirm('Are you sure you want to delete this promotion?')) {
      this.promotionService.deletePromotion(id)
        .then(result => {
          if (result.success) {
            this.promotions = this.promotions.filter(p => p.id !== id);
            this.notificationService.success('Promotion deleted successfully');
          } else {
            this.error = result.errorMessage || 'Failed to delete promotion. Please try again.';
            this.notificationService.error(this.error);
          }
        })
        .catch(error => {
          this.error = 'Failed to delete promotion. Please try again.';
          this.notificationService.error(this.error);
          console.error('Error deleting promotion:', error);
        });
    }
  }
}
