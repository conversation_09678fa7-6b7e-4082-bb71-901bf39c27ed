import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ChapterService } from '../../../../services/admin/chapter.service';
import { Chapter } from '../../../../models/admin/Chapter';

@Component({
  selector: 'app-chapter-list',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './chapter-list.html',
  styleUrl: './chapter-list.css'
})
export class ChapterListComponent implements OnInit, OnChanges {
  chapters: Chapter[] = [];
  loading = true;
  error = '';

  // Pagination and search inputs from parent
  @Input() currentPage = 1;
  @Input() pageSize = 10;
  @Input() searchTerm = '';

  // Pagination outputs to parent
  @Output() paginationChange = new EventEmitter<{totalItems: number, totalPages: number}>();

  // Search output
  @Output() searchChange = new EventEmitter<string>();

  // Calculated pagination values
  totalPages = 0;

  // Make Math available in the template
  Math = Math;

  constructor(private chapterService: ChapterService) {}

  ngOnInit(): void {
    this.loadChapters();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // React to changes in pagination or search inputs
    if ((changes['currentPage'] && !changes['currentPage'].firstChange) ||
        (changes['pageSize'] && !changes['pageSize'].firstChange) ||
        (changes['searchTerm'] && !changes['searchTerm'].firstChange)) {
      // Need to reload data when pagination or search changes since we're using server-side pagination and filtering
      this.loadChapters();
    }
  }

  loadChapters(): void {
    this.loading = true;
    this.error = '';

    // Convert from 1-based (UI) to 0-based (API) pagination
    const apiPage = this.currentPage - 1;

    this.chapterService.getChaptersPaginated(this.searchTerm, apiPage, this.pageSize)
      .then(result => {
        this.chapters = result.content;
        // Use the totalElements and totalPages from the API response
        this.paginationChange.emit({
          totalItems: result.totalElements,
          totalPages: result.totalPages
        });
        this.loading = false;
      })
      .catch(err => {
        this.error = 'Failed to load chapters. Please try again.';
        this.loading = false;
        console.error('Error loading chapters:', err);
      });
  }

  // The paginatedChapters getter is no longer needed since we're getting paginated data directly from the API
  get paginatedChapters(): Chapter[] {
    return this.chapters;
  }

  onSearch(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.searchTerm = value;
    this.searchChange.emit(value);
    this.currentPage = 1; // Reset to first page when searching
    this.loadChapters();
  }

  deleteChapter(id: number): void {
    if (confirm('Are you sure you want to delete this chapter?')) {
      this.chapterService.deleteChapter(id)
        .then(success => {
          if (success) {
            this.chapters = this.chapters.filter(chapter => chapter.id !== id);
          } else {
            this.error = 'Failed to delete chapter. Please try again.';
          }
        })
        .catch(err => {
          this.error = 'Failed to delete chapter. Please try again.';
          console.error('Error deleting chapter:', err);
        });
    }
  }
}
