/* Question List Styles */
.question-list-container {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header styles */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.list-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #ffffff;
}

.add-button {
  background-color: #3b82f6;
  color: white;
  font-weight: 700;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  text-decoration: none;
  transition: background-color 0.2s;
}

.add-button:hover {
  background-color: #2563eb;
}

/* Error message */
.error-message {
  background-color: #991b1b;
  color: white;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

/* Loading indicator */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.loading-spinner {
  height: 3rem;
  width: 3rem;
  border-radius: 50%;
  border-top: 2px solid #3b82f6;
  border-bottom: 2px solid #3b82f6;
  border-left: 2px solid transparent;
  border-right: 2px solid transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-left: 0.75rem;
  color: #d1d5db;
}

/* Empty state */
.empty-state {
  background-color: #1f2937;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
}

.empty-state-text {
  font-size: 1.25rem;
  color: #d1d5db;
}

/* Table styles */
.table-container {
  background-color: #1f2937;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.table-header {
  background-color: #374151;
}

.table-header-cell {
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #d1d5db;
}

.table-body {
  background-color: #1f2937;
}

.table-row {
  border-bottom: 1px solid #4b5563;
  transition: background-color 0.15s;
}

.table-row:hover {
  background-color: #374151;
}

.table-cell {
  padding: 1rem 1.5rem;
  white-space: nowrap;
  font-size: 0.875rem;
  color: #d1d5db;
}

/* Action buttons */
.actions-container {
  display: flex;
  gap: 0.5rem;
}

.view-button {
  color: #60a5fa;
  text-decoration: none;
}

.view-button:hover {
  color: #93c5fd;
}

.edit-button {
  color: #fbbf24;
  text-decoration: none;
}

.edit-button:hover {
  color: #fcd34d;
}

.delete-button {
  color: #f87171;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  font-size: inherit;
}

.delete-button:hover {
  color: #fca5a5;
}

/* Pagination */
.pagination-container {
  background-color: #374151;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #4b5563;
}

.pagination-info {
  color: #d1d5db;
  font-size: 0.875rem;
}

.pagination-info-text {
  font-weight: 500;
}

.pagination-nav {
  display: flex;
  align-items: center;
}

.pagination-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem;
  border: 1px solid #4b5563;
  background-color: #1f2937;
  color: #d1d5db;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s;
}

.pagination-button:hover {
  background-color: #374151;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button-prev {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.pagination-button-next {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.pagination-page-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid #4b5563;
  background-color: #1f2937;
  color: #d1d5db;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s;
  margin: 0 -1px;
}

.pagination-page-button:hover {
  background-color: #374151;
}

.pagination-page-button-active {
  background-color: #3b82f6;
  color: white;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
