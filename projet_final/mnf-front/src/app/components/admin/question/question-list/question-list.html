<div class="question-list-container">
  <div class="list-header">
    <h2 class="list-title">Questions</h2>
    <div class="header-actions">
      <!-- Search input -->
      <div class="search-container">
        <input
          type="text"
          class="search-input"
          placeholder="Search questions..."
          [value]="searchTerm"
          (input)="onSearch($event)"
        />
      </div>
      <a routerLink="/admin/questions/create" class="add-button">
        Add Question
      </a>
    </div>
  </div>

  <!-- Error message -->
  @if (error) {
  <div class="error-message">{{ error }}</div>
  }

  <!-- Loading indicator -->
  @if (loading) {
  <div class="loading-container">
    <div class="loading-spinner"></div>
    <span class="loading-text" i18n="@@loadingQuestions"
      >Loading questions...</span
    >
  </div>
  }

  <!-- Questions list -->
  @if (!loading && questions.length > 0) {
  <div class="table-container">
    <table class="data-table">
      <thead class="table-header">
        <tr>
          <th scope="col" class="table-header-cell">ID</th>
          <th scope="col" class="table-header-cell">Question</th>
          <th scope="col" class="table-header-cell">Chapter ID</th>
          <th scope="col" class="table-header-cell">Actions</th>
        </tr>
      </thead>
      <tbody class="table-body">
        @for (question of questions; track question.id) {
        <tr class="table-row">
          <td class="table-cell">{{ question.id }}</td>
          <td class="table-cell">{{ question.statement }}</td>
          <td class="table-cell">{{ question.chapter_id || 'N/A' }}</td>
          <td class="table-cell">
            <div class="actions-container">
              @if (question.id !== undefined) {
              <a
                [routerLink]="['/admin/questions', question.id]"
                class="view-button"
                >View</a
              >
              }
              @if (question.id !== undefined) {
              <a
                [routerLink]="['/admin/questions/edit', question.id]"
                class="edit-button"
                >Edit</a
              >
              }
              @if (question.id !== undefined) {
              <button
                (click)="deleteQuestion(question.id)"
                class="delete-button"
              >
                Delete
              </button>
              }
            </div>
          </td>
        </tr>
        }
      </tbody>
    </table>

    <!-- Pagination is now handled by the parent component -->
  </div>
  }

  <!-- No questions message -->
  @if (!loading && questions.length === 0) {
  <div class="empty-state">
    <p class="empty-state-text">
      @if (searchTerm) {
        No questions found matching "{{ searchTerm }}". Try a different search term.
      } @else {
        No questions found. Create your first question to get started.
      }
    </p>
  </div>
  }
</div>
