import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { QuestionService } from '../../../../services/admin/question.service';
import { Question } from '../../../../models/admin/Question';
import { NotificationService } from '../../../../services/notification.service';

@Component({
  selector: 'app-question-list',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './question-list.html',
  styleUrl: './question-list.css'
})
export class QuestionListComponent implements OnInit, OnChanges {
  questions: Question[] = [];
  loading = true;
  error = '';

  // Search input
  @Input() searchTerm = '';

  // Pagination inputs from parent
  @Input() currentPage = 1;
  @Input() pageSize = 10;
  @Input() sortBy = 'id';

  // Pagination outputs to parent
  @Output() paginationChange = new EventEmitter<{totalItems: number, totalPages: number}>();

  // Search output
  @Output() searchChange = new EventEmitter<string>();

  // Calculated pagination values
  totalItems = 0;
  totalPages = 0;

  // Make Math available in the template
  Math = Math;

  constructor(
    private questionService: QuestionService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadQuestions();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // React to changes in pagination inputs or search term
    if ((changes['currentPage'] && !changes['currentPage'].firstChange) ||
        (changes['pageSize'] && !changes['pageSize'].firstChange) ||
        (changes['searchTerm'] && !changes['searchTerm'].firstChange)) {
      this.loadQuestions();
    }
  }

  loadQuestions(): void {
    this.loading = true;
    this.error = '';

    // First get the total count from the /total endpoint
    this.questionService.getQuestionsTotal()
      .then(total => {
        this.totalItems = total;
        this.totalPages = Math.ceil(total / this.pageSize);
        console.log(`Questions total count from /total endpoint: ${total}`);

        // Emit pagination info to parent component
        this.paginationChange.emit({
          totalItems: this.totalItems,
          totalPages: this.totalPages
        });

        // Convert from 1-based (UI) to 0-based (API) pagination
        const backendPage = this.currentPage - 1;

        // Then get the paginated data
        return this.questionService.getQuestions(this.searchTerm, backendPage, this.pageSize, this.sortBy);
      })
      .then(result => {
        this.questions = result.content;
        // Don't overwrite totalItems and totalPages with values from paginated response
        // to ensure we show the correct total count from the /total endpoint
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load questions. Please try again.';
        this.loading = false;
        console.error('Error loading questions:', error);
      });
  }

  onSearch(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.searchTerm = value;
    this.searchChange.emit(value);
    this.currentPage = 1; // Reset to first page when searching
    this.loadQuestions();
  }

  deleteQuestion(id: number): void {
    if (confirm('Are you sure you want to delete this question?')) {
      this.questionService.deleteQuestion(id)
        .then(success => {
          if (success) {
            this.questions = this.questions.filter(question => question.id !== id);
            this.notificationService.success('Question deleted successfully');
            // Reload questions to update pagination
            this.loadQuestions();
          } else {
            this.error = 'Failed to delete question. Please try again.';
            this.notificationService.error(this.error);
          }
        })
        .catch(error => {
          this.error = 'Failed to delete question. Please try again.';
          this.notificationService.error(this.error);
          console.error('Error deleting question:', error);
        });
    }
  }
}
