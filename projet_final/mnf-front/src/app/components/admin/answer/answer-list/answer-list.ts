import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AnswerService } from '../../../../services/admin/answer.service';
import { Answer } from '../../../../models/admin/Answer';
import { NotificationService } from '../../../../services/notification.service';

@Component({
  selector: 'app-answer-list',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './answer-list.html',
  styleUrl: './answer-list.css'
})
export class AnswerListComponent implements OnInit, OnChanges {
  answers: Answer[] = [];
  loading = true;
  error = '';

  // Search input
  @Input() searchTerm = '';

  // Pagination inputs from parent
  @Input() currentPage = 1;
  @Input() pageSize = 10;
  @Input() sortBy = 'id';

  // Pagination outputs to parent
  @Output() paginationChange = new EventEmitter<{totalItems: number, totalPages: number}>();

  // Search output
  @Output() searchChange = new EventEmitter<string>();

  // Calculated pagination values
  totalItems = 0;
  totalPages = 0;

  // Make Math available in the template
  Math = Math;

  constructor(
    private answerService: AnswerService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadAnswers();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // React to changes in pagination inputs or search term
    if ((changes['currentPage'] && !changes['currentPage'].firstChange) ||
        (changes['pageSize'] && !changes['pageSize'].firstChange) ||
        (changes['searchTerm'] && !changes['searchTerm'].firstChange)) {
      this.loadAnswers();
    }
  }

  loadAnswers(): void {
    this.loading = true;
    this.error = '';

    // First get the total count from the /total endpoint
    this.answerService.getAnswersTotal()
      .then(total => {
        this.totalItems = total;
        this.totalPages = Math.ceil(total / this.pageSize);

        // Emit pagination info to parent component
        this.paginationChange.emit({
          totalItems: this.totalItems,
          totalPages: this.totalPages
        });

        // Convert from 1-based (UI) to 0-based (API) pagination
        const backendPage = this.currentPage - 1;

        // Then get the paginated data
        return this.answerService.getAnswers(this.searchTerm, backendPage, this.pageSize, this.sortBy);
      })
      .then(result => {
        this.answers = result.content;
        // Don't overwrite totalItems and totalPages with values from paginated response
        // to ensure we show the correct total count from the /total endpoint

        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load answers. Please try again.';
        this.loading = false;
        console.error('Error loading answers:', error);
      });
  }

  onSearch(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.searchTerm = value;
    this.searchChange.emit(value);
    this.currentPage = 1; // Reset to first page when searching
    this.loadAnswers();
  }

  deleteAnswer(id: number): void {
    if (confirm('Are you sure you want to delete this answer?')) {
      this.answerService.deleteAnswer(id)
        .then(result => {
          if (result.success) {
            this.answers = this.answers.filter(answer => answer.id !== id);
            this.notificationService.success('Answer deleted successfully');
            // Reload answers to update pagination
            this.loadAnswers();
          } else {
            this.error = result.errorMessage || 'Failed to delete answer. Please try again.';
            this.notificationService.error(this.error);
          }
        })
        .catch(error => {
          this.error = 'Failed to delete answer. Please try again.';
          this.notificationService.error(this.error);
          console.error('Error deleting answer:', error);
        });
    }
  }
}
