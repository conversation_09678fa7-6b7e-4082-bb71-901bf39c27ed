<div class="answer-list-container">
  <div class="list-header">
    <h2 class="list-title">Answers</h2>
    <div class="header-actions">
      <!-- Search input -->
      <div class="search-container">
        <input
          type="text"
          class="search-input"
          placeholder="Search answers..."
          [value]="searchTerm"
          (input)="onSearch($event)"
        />
      </div>
      <a routerLink="/admin/answers/create" class="add-button">
        Add Answer
      </a>
    </div>
  </div>

  <!-- Error message -->
  @if (error) {
  <div class="error-message">{{ error }}</div>
  }

  <!-- Loading indicator -->
  @if (loading) {
  <div class="loading-container">
    <div class="loading-spinner"></div>
    <span class="loading-text">Loading answers...</span>
  </div>
  }

  <!-- Answers list -->
  @if (!loading && answers.length > 0) {
  <div class="table-container">
    <table class="data-table">
      <thead class="table-header">
        <tr>
          <th scope="col" class="table-header-cell">ID</th>
          <th scope="col" class="table-header-cell">Label</th>
          <th scope="col" class="table-header-cell">Text</th>
          <th scope="col" class="table-header-cell">Valid Answer</th>
          <th scope="col" class="table-header-cell">Question ID</th>
          <th scope="col" class="table-header-cell">Actions</th>
        </tr>
      </thead>
      <tbody class="table-body">
        @for (answer of answers; track answer.id) {
        <tr class="table-row">
          <td class="table-cell">{{ answer.id }}</td>
          <td class="table-cell">{{ answer.label }}</td>
          <td class="table-cell">{{ answer.text }}</td>
          <td class="table-cell">{{ answer.valid_answer ? 'Yes' : 'No' }}</td>
          <td class="table-cell">{{ answer.question_id || 'N/A' }}</td>
          <td class="table-cell">
            <div class="actions-container">
              @if (answer.id !== undefined) {
              <a
                [routerLink]="['/admin/answers', answer.id]"
                class="view-button"
                >View</a
              >
              }
              @if (answer.id !== undefined) {
              <a
                [routerLink]="['/admin/answers/edit', answer.id]"
                class="edit-button"
                >Edit</a
              >
              }
              @if (answer.id !== undefined) {
              <button
                (click)="deleteAnswer(answer.id)"
                class="delete-button"
              >
                Delete
              </button>
              }
            </div>
          </td>
        </tr>
        }
      </tbody>
    </table>

    <!-- Pagination is now handled by the parent component -->
  </div>
  }

  <!-- No answers message -->
  @if (!loading && answers.length === 0) {
  <div class="empty-state">
    <p class="empty-state-text">
      @if (searchTerm) {
        No answers found matching "{{ searchTerm }}". Try a different search term.
      } @else {
        No answers found. Create your first answer to get started.
      }
    </p>
  </div>
  }
</div>
