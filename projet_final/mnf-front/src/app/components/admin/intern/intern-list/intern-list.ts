import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
} from "@angular/core";
import { RouterLink } from "@angular/router";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { Intern } from "../../../../models/admin/Intern";
import { InternService } from "../../../../services/admin/intern.service";
import { PromotionService } from "../../../../services/admin/promotion.service";
import { Promotion } from "../../../../models/admin/Promotion";
import { NotificationService } from "../../../../services/notification.service";

@Component({
  selector: "app-intern-list",
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: "./intern-list.html",
  styleUrl: "./intern-list.css",
})
export class InternListComponent implements OnInit, OnChanges {
  interns: Intern[] = [];
  promotions: Map<number, Promotion> = new Map();
  loading = true;
  error = "";

  // Search input
  @Input() searchTerm = '';

  // Pagination inputs from parent
  @Input() currentPage = 1;
  @Input() pageSize = 10;
  @Input() sortBy = 'id';

  // Pagination outputs to parent
  @Output() paginationChange = new EventEmitter<{
    totalItems: number;
    totalPages: number;
  }>();

  // Search output
  @Output() searchChange = new EventEmitter<string>();

  // Calculated pagination values
  totalItems = 0;
  totalPages = 0;

  // Make Math available in the template
  Math = Math;

  constructor(
    private internService: InternService,
    private promotionService: PromotionService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadInterns();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // React to changes in pagination inputs or search term
    if ((changes['currentPage'] && !changes['currentPage'].firstChange) ||
        (changes['pageSize'] && !changes['pageSize'].firstChange) ||
        (changes['searchTerm'] && !changes['searchTerm'].firstChange)) {
      this.loadInterns();
    }
  }

  loadInterns(): void {
    this.loading = true;
    this.error = '';

    // Convert from 1-based (UI) to 0-based (API) pagination
    const backendPage = this.currentPage - 1;

    // First get the total count from the /total endpoint
    this.internService.getInternsTotal()
      .then(total => {
        this.totalItems = total;
        this.totalPages = Math.ceil(total / this.pageSize);

        // Emit pagination info to parent component
        this.paginationChange.emit({
          totalItems: this.totalItems,
          totalPages: this.totalPages
        });

        // Then get the paginated data
        return this.internService.getInternsPaginated(this.searchTerm, backendPage, this.pageSize, this.sortBy);
      })
      .then(result => {
        this.interns = result.content;
        this.loading = false;
        this.loadPromotions();
      })
      .catch(error => {
        this.error = 'Failed to load interns. Please try again.';
        this.loading = false;
        console.error('Error loading interns:', error);
      });
  }

  onSearch(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.searchTerm = value;
    this.searchChange.emit(value);
    this.currentPage = 1; // Reset to first page when searching
    this.loadInterns();
  }


  loadPromotions(): void {
    // Check if this.interns is an array before proceeding
    if (!Array.isArray(this.interns)) {
      console.error("Error: this.interns is not an array", this.interns);
      this.interns = []; // Initialize as empty array to prevent further errors
      this.loading = false;
      return;
    }

    // First, check if interns already have promotion objects
    const internsWithPromotion = this.interns.filter(
      (intern) => intern.promotion && intern.promotion.id
    );

    if (internsWithPromotion.length > 0) {
      // Use the promotion objects that are already in the interns
      internsWithPromotion.forEach((intern) => {
        if (intern.promotion && intern.promotion.id) {
          this.promotions.set(intern.promotion.id, intern.promotion);
        }
      });
      this.loading = false;
      return;
    }

    // If no promotion objects, get unique promotion IDs
    const promotionIds = [
      ...new Set(
        this.interns
          .filter((intern) => intern.promotion_id !== null)
          .map((intern) => intern.promotion_id!)
      ),
    ];

    if (promotionIds.length === 0) {
      this.loading = false;
      return;
    }

    // Load promotions
    Promise.all(
      promotionIds.map((id) => this.promotionService.getPromotionById(id))
    )
      .then((promotions) => {
        promotions.forEach((promotion) => {
          if (promotion && promotion.id) {
            this.promotions.set(promotion.id, promotion);
          }
        });
        this.loading = false;
      })
      .catch((error) => {
        this.error =
          "Failed to load promotions. Some promotion names may not be displayed.";
        this.loading = false;
        console.error("Error loading promotions:", error);
      });
  }

  getPromotionName(promotionId: number | null): string {
    if (promotionId === null) return "None";
    const promotion = this.promotions.get(+promotionId);
    return promotion ? promotion.name : `Promotion ${promotionId}`;
  }

  // Get promotion name directly from intern object
  getInternPromotionName(intern: Intern): string {
    // If intern has a promotion object, use it
    if (intern.promotion && intern.promotion.name) {
      return intern.promotion.name;
    }

    // Otherwise, use the promotion_id to look up the promotion
    return this.getPromotionName(intern.promotion_id);
  }

  deleteIntern(id: number): void {
    if (confirm("Are you sure you want to delete this intern?")) {
      this.internService
        .deleteIntern(id)
        .then((success) => {
          if (success) {
            this.interns = this.interns.filter((i) => i.id !== id);
            this.notificationService.success('Intern deleted successfully');
          } else {
            this.error = "Failed to delete intern. Please try again.";
            this.notificationService.error(this.error);
          }
        })
        .catch((error) => {
          this.error = "Failed to delete intern. Please try again.";
          this.notificationService.error(this.error);
          console.error("Error deleting intern:", error);
        });
    }
  }
}
