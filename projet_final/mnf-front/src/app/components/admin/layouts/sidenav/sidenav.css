/* Sidenav container styles */
.sidenav-container {
  position: fixed;
  top: 64px; /* Height of the toolbar */
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

/* Sidenav styles */
.sidenav {
  width: 250px;
  background-color: #1e1e1e;
  color: white;
}

/* Dark mode specific styles */
:host-context([data-theme="dark"]) {
  .sidenav {
    background-color: #1e1e1e;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .sidenav-container {
    top: 56px; /* Height of the toolbar on mobile */
  }
}
