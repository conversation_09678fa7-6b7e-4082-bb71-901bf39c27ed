/* Footer styles */
.footer {
  background-color: var(--surface-color);
  color: var(--text-color);
  border-top: 1px solid var(--border-color);
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease;
}

.footer-content {
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer-links {
  margin-bottom: 8px;
}

.footer-links a {
  color: var(--text-color);
  text-decoration: none;
  margin: 0 8px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.footer-links a:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Responsive styles */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    align-items: center;
  }

  .footer-links {
    margin-bottom: 16px;
  }
}
