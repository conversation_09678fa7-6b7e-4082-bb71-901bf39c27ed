/* Header styles */
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: var(--surface-color);
  color: var(--text-color);
  box-shadow: 0 2px 5px var(--shadow-color);
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Sidenav container styles */
.sidenav-container {
  position: fixed; /* Fixed position to overlay the content */
  top: 64px; /* Position below the header */
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999; /* Just below the header */
  pointer-events: none; /* Allow clicks to pass through to elements below */
}

/* Sidenav styles */
.sidenav {
  width: 250px;
  background-color: var(--surface-color);
  color: var(--text-color);
  box-shadow: 2px 0 5px var(--shadow-color);
  pointer-events: auto; /* Restore pointer events for the sidenav */
}

/* Sidenav content styles */
.mat-sidenav-content {
  pointer-events: none; /* Allow clicks to pass through */
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: bold;
  transition: color 0.3s ease;
}

.logo:hover {
  color: var(--secondary-color);
}

.spacer {
  flex: 1 1 auto;
}

.nav-links {
  display: flex;
  align-items: center;
}

.nav-links a {
  margin: 0 8px;
  color: var(--text-color);
  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-links a:hover {
  background-color: var(--primary-color);
  color: white;
}

.menu-button {
  display: none;
}

/* Language selector styles */
.language-selector {
  margin-right: 8px;
}

.flag-icon {
  width: 24px;
  height: 16px;
  border-radius: 2px;
  object-fit: cover;
}

/* Responsive styles */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .menu-button {
    display: block;
  }
}
