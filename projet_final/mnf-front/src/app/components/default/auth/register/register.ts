import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
  ReactiveFormsModule,
  FormGroup,
  FormBuilder,
  Validators,
  AbstractControl,
  ValidationErrors,
  ValidatorFn,
} from "@angular/forms";
import { Router, ActivatedRoute } from "@angular/router";
import { MaterialModules } from "../../../../material/material";
import { trigger, transition, style, animate } from "@angular/animations";
import { TranslatePipe } from "../../../../pipes/translate.pipe";
import { AuthService } from "../../../../services/auth.service";

@Component({
  selector: "app-register",
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MaterialModules, TranslatePipe],
  templateUrl: "./register.html",
  styleUrl: "./register.css",
  animations: [
    trigger("formAnimation", [
      transition(":enter", [
        style({ opacity: 0, transform: "translateY(10px)" }),
        animate(
          "300ms ease-out",
          style({ opacity: 1, transform: "translateY(0)" })
        ),
      ]),
      transition(":leave", [
        animate(
          "300ms ease-in",
          style({ opacity: 0, transform: "translateY(10px)" })
        ),
      ]),
    ]),
  ],
})
export class RegisterPage implements OnInit {
  registerForm!: FormGroup;
  hidePassword = true;
  hideConfirmPassword = true;
  loading = false;
  error = "";
  returnUrl = "/";

  // Custom validator for name fields (only letters and spaces)
  static nameValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (!value) {
        return null;
      }
      const valid = /^[A-Za-zÀ-ÖØ-öø-ÿ\s-]+$/.test(value);
      return !valid ? { invalidName: true } : null;
    };
  }

  // Custom validator for password complexity
  static passwordComplexityValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (!value) {
        return null;
      }

      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumeric = /[0-9]/.test(value);
      const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(value);

      const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;

      return !passwordValid ? {
        passwordComplexity: {
          hasUpperCase,
          hasLowerCase,
          hasNumeric,
          hasSpecialChar
        }
      } : null;
    };
  }

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.registerForm = this.fb.group(
      {
        firstName: ["", [
          Validators.required,
          RegisterPage.nameValidator()
        ]],
        lastName: ["", [
          Validators.required,
          RegisterPage.nameValidator()
        ]],
        email: ["", [
          Validators.required,
          Validators.email,
          Validators.pattern(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)
        ]],
        password: ["", [
          Validators.required,
          Validators.minLength(8),
          RegisterPage.passwordComplexityValidator()
        ]],
        confirmPassword: ["", [Validators.required]],
      },
      {
        validators: this.passwordMatchValidator,
      }
    );

    // Get return URL from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams["returnUrl"] || "/";

    if (this.authService.isAuthenticated()) {
      this.router.navigateByUrl(this.returnUrl);
    }
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get("password")?.value;
    const confirmPassword = form.get("confirmPassword")?.value;

    if (password !== confirmPassword) {
      form.get("confirmPassword")?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  onSubmit(): void {
    if (this.registerForm.valid) {
      this.loading = true;
      this.error = "";

      const { firstName, lastName, email, password, confirmPassword } =
        this.registerForm.value;

      this.authService
        .register(firstName, lastName, email, password, confirmPassword)
        .then(() => {
          // Auto login après inscription réussie
          return this.authService.login(email, password);
        })
        .then(() => {
          this.router.navigateByUrl(this.returnUrl);
        })
        .catch((error) => {
          this.error = "Registration or login failed. Please try again.";
          console.error("Error after registration:", error);
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }

  getErrorMessage(controlName: string): string {
    const control = this.registerForm.get(controlName);
    if (control?.hasError("required")) {
      return "common.required";
    }
    if (control?.hasError("email") || control?.hasError("pattern")) {
      return "common.invalidEmail";
    }
    if (control?.hasError("minlength")) {
      // Get the required length from the validator
      const requiredLength = control.errors?.['minlength']?.requiredLength || 8;
      // Return the key with the length parameter
      return `common.passwordMinLength|{"length": ${requiredLength}}`;
    }
    if (control?.hasError("passwordMismatch")) {
      return "common.passwordMismatch";
    }
    if (control?.hasError("invalidName")) {
      return "common.invalidName";
    }
    if (control?.hasError("passwordComplexity")) {
      return "common.passwordComplexity";
    }
    return "";
  }
}
