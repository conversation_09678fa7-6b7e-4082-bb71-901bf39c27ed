<form
  [formGroup]="registerForm"
  (ngSubmit)="onSubmit()"
  class="form-container"
  @formAnimation
>
  <!-- Error message -->
  @if (error) {
  <div class="alert alert-error" role="alert">
    <span>{{ error }}</span>
  </div>
  }
  <div class="grid-container">
    <div class="form-group">
      <mat-form-field appearance="fill" class="w-full custom-form-field">
        <mat-label>{{ 'auth.firstName' | translate }}</mat-label>
        <mat-icon matPrefix class="mr-2">person</mat-icon>
        <input matInput formControlName="firstName" required />
        @if (registerForm.get('firstName')?.invalid) {
        <mat-error> {{ getErrorMessage('firstName') | translate }} </mat-error>
        }
      </mat-form-field>
    </div>

    <div class="form-group">
      <mat-form-field appearance="fill" class="w-full custom-form-field">
        <mat-label>{{ 'auth.lastName' | translate }}</mat-label>
        <mat-icon matPrefix class="mr-2">person</mat-icon>
        <input matInput formControlName="lastName" required />
        @if (registerForm.get('lastName')?.invalid) {
        <mat-error> {{ getErrorMessage('lastName') | translate }} </mat-error>
        }
      </mat-form-field>
    </div>
  </div>

  <div class="form-group">
    <mat-form-field appearance="fill" class="custom-form-field">
      <mat-label>{{ 'auth.email' | translate }}</mat-label>
      <mat-icon matPrefix class="mr-2">email</mat-icon>
      <input
        matInput
        type="email"
        formControlName="email"
        placeholder="<EMAIL>"
        required
      />
      @if (registerForm.get('email')?.invalid) {
      <mat-error> {{ getErrorMessage('email') | translate }} </mat-error>
      }
    </mat-form-field>
  </div>

  <div class="form-group">
    <mat-form-field appearance="fill" class="w-full custom-form-field">
      <mat-label>{{ 'auth.password' | translate }}</mat-label>
      <mat-icon matPrefix class="mr-2">lock</mat-icon>
      <input
        matInput
        [type]="hidePassword ? 'password' : 'text'"
        formControlName="password"
        required
      />
      <button
        mat-icon-button
        matSuffix
        (click)="hidePassword = !hidePassword"
        type="button"
      >
        <mat-icon
          >{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon
        >
      </button>
      @if (registerForm.get('password')?.invalid) {
      <mat-error> {{ getErrorMessage('password') | translate }} </mat-error>
      }
    </mat-form-field>
  </div>

  <div class="form-group">
    <mat-form-field appearance="fill" class="w-full custom-form-field">
      <mat-label>{{ 'auth.confirmPassword' | translate }}</mat-label>
      <mat-icon matPrefix class="mr-2">lock</mat-icon>
      <input
        matInput
        [type]="hideConfirmPassword ? 'password' : 'text'"
        formControlName="confirmPassword"
        required
      />
      <button
        mat-icon-button
        matSuffix
        (click)="hideConfirmPassword = !hideConfirmPassword"
        type="button"
      >
        <mat-icon
          >{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon
        >
      </button>
      @if (registerForm.get('confirmPassword')?.invalid) {
      <mat-error>
        {{ getErrorMessage('confirmPassword') | translate }}
      </mat-error>
      }
    </mat-form-field>
  </div>

  <div class="actions-container">
    <button
      type="submit"
      [disabled]="registerForm.invalid || loading"
      class="submit-button register-button"
    >
      @if (loading) {
      <mat-spinner diameter="24" class="mr-2"></mat-spinner>
      {{ 'common.loading' | translate }} } @else {
      <mat-icon class="mr-2">how_to_reg</mat-icon>
      {{ 'auth.register' | translate }} }
    </button>
  </div>
</form>
