<form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="space-y-4" @formAnimation>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
      <mat-form-field appearance="fill" class="w-full custom-form-field">
        <mat-label>{{ 'auth.firstName' | translate }}</mat-label>
        <mat-icon matPrefix class="mr-2 text-gray-500">person</mat-icon>
        <input matInput formControlName="firstName" required>
        @if (registerForm.get('firstName')?.invalid) {
          <mat-error>
            {{ getErrorMessage('firstName') | translate }}
          </mat-error>
        }
      </mat-form-field>
    </div>

    <div>
      <mat-form-field appearance="fill" class="w-full custom-form-field">
        <mat-label>{{ 'auth.lastName' | translate }}</mat-label>
        <mat-icon matPrefix class="mr-2 text-gray-500">person</mat-icon>
        <input matInput formControlName="lastName" required>
        @if (registerForm.get('lastName')?.invalid) {
          <mat-error>
            {{ getErrorMessage('lastName') | translate }}
          </mat-error>
        }
      </mat-form-field>
    </div>
  </div>

  <div>
    <mat-form-field appearance="fill" class="w-full custom-form-field">
      <mat-label>{{ 'auth.email' | translate }}</mat-label>
      <mat-icon matPrefix class="mr-2 text-gray-500">email</mat-icon>
      <input matInput type="email" formControlName="email" placeholder="<EMAIL>" required>
      @if (registerForm.get('email')?.invalid) {
        <mat-error>
          {{ getErrorMessage('email') | translate }}
        </mat-error>
      }
    </mat-form-field>
  </div>

  <div>
    <mat-form-field appearance="fill" class="w-full custom-form-field">
      <mat-label>{{ 'auth.password' | translate }}</mat-label>
      <mat-icon matPrefix class="mr-2 text-gray-500">lock</mat-icon>
      <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" required>
      <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button" class="text-gray-500">
        <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
      </button>
      @if (registerForm.get('password')?.invalid) {
        <mat-error>
          {{ getErrorMessage('password') | translate }}
        </mat-error>
      }
    </mat-form-field>
  </div>

  <div>
    <mat-form-field appearance="fill" class="w-full custom-form-field">
      <mat-label>{{ 'auth.confirmPassword' | translate }}</mat-label>
      <mat-icon matPrefix class="mr-2 text-gray-500">lock</mat-icon>
      <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword" required>
      <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button" class="text-gray-500">
        <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
      </button>
      @if (registerForm.get('confirmPassword')?.invalid) {
        <mat-error>
          {{ getErrorMessage('confirmPassword') | translate }}
        </mat-error>
      }
    </mat-form-field>
  </div>

  <div class="mt-6">
    <button mat-raised-button color="primary" type="submit" [disabled]="registerForm.invalid" class="w-full py-3 rounded-lg text-lg font-medium shadow-md hover:shadow-lg transition duration-300">
      <span class="flex items-center justify-center">
        <mat-icon class="mr-2">how_to_reg</mat-icon>
        {{ 'auth.register' | translate }}
      </span>
    </button>
  </div>
</form>
