/* Custom styles for register component */
:host {
  display: block;
}

.form-container {
  width: 100%;
}

/* Grid container for first name and last name */
.grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: 1fr;
  }
}

/* Custom styling for form fields */
::ng-deep .custom-form-field {
  width: 100%;
}

/* Style for filled appearance */
::ng-deep .custom-form-field.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper {
  background-color: white;
  border: 1px solid #CD7A88;
  border-radius: 0.25rem;
  padding: 0.375rem 0.5rem;
  transition: background-color 0.3s ease;
}

::ng-deep .custom-form-field.mat-form-field-appearance-fill:hover .mat-mdc-text-field-wrapper {
  border-color: #CD8A88;
}

/* Remove the underline */
::ng-deep .custom-form-field.mat-form-field-appearance-fill .mdc-line-ripple::before,
::ng-deep .custom-form-field.mat-form-field-appearance-fill .mdc-line-ripple::after {
  border-bottom-width: 0;
}

/* Style for the label */
::ng-deep .custom-form-field .mat-mdc-floating-label {
  font-size: 14px;
  color: black;
}

/* Style for the input */
::ng-deep .custom-form-field input.mat-mdc-input-element {
  padding: 0.375rem 0;
  font-size: 14px;
  color: black;
}

/* Style for the icons */
::ng-deep .custom-form-field .mat-icon {
  color: #CD7A88;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  height: 18px;
  width: 18px;
}

/* Style for the submit button */
::ng-deep .submit-button {
  background-color: #CD7A88 !important;
  color: white !important;
  font-weight: 600;
  padding: 0.5rem 1rem !important;
  border-radius: 0.25rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  width: auto !important;
  height: auto !important;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  min-width: 150px;
}

::ng-deep .submit-button:hover {
  background-color: #CD8A88 !important;
}

::ng-deep .submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Style for action buttons container */
.actions-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

/* Add some responsive adjustments */
@media (max-width: 640px) {
  /* No specific adjustments needed */
}
