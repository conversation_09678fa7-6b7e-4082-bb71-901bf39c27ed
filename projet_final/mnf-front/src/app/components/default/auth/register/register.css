/* Custom styles for register component */
:host {
  display: block;
}

/* Custom styling for form fields */
::ng-deep .custom-form-field {
  width: 100%;
}

/* Style for filled appearance */
::ng-deep .custom-form-field.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  transition: background-color 0.3s ease;
}

::ng-deep .custom-form-field.mat-form-field-appearance-fill:hover .mat-mdc-text-field-wrapper {
  background-color: rgba(0, 0, 0, 0.04);
}

/* Remove the underline */
::ng-deep .custom-form-field.mat-form-field-appearance-fill .mdc-line-ripple::before,
::ng-deep .custom-form-field.mat-form-field-appearance-fill .mdc-line-ripple::after {
  border-bottom-width: 0;
}

/* Style for the label */
::ng-deep .custom-form-field .mat-mdc-floating-label {
  font-size: 14px;
}

/* Style for the input */
::ng-deep .custom-form-field input.mat-mdc-input-element {
  padding: 0.5rem 0;
  font-size: 16px;
}

/* Style for the icons */
::ng-deep .custom-form-field .mat-icon {
  color: rgba(0, 0, 0, 0.54);
}

/* Add some responsive adjustments */
@media (max-width: 640px) {
  .max-w-md {
    max-width: 95%;
    margin: 0 auto;
  }
}
