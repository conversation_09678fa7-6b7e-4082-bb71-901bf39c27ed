/* Custom styles for register component */
:host {
  display: block;
}

.form-container {
  width: 100%;
}

/* Grid container for first name and last name */
.grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: 1fr;
  }
}

/* Custom styling for form fields */
::ng-deep .custom-form-field {
  width: 100%;
  margin: 10px;
}

/* Style for filled appearance */
::ng-deep
  .custom-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper {
  background-color: white;
  border: 1px solid #cd7a88;
  border-radius: 0.25rem;
  transition: background-color 0.3s ease;
}

::ng-deep
  .custom-form-field.mat-form-field-appearance-fill:hover
  .mat-mdc-text-field-wrapper {
  border-color: #cd8a88;
}

/* Remove the underline */
::ng-deep
  .custom-form-field.mat-form-field-appearance-fill
  .mdc-line-ripple::before,
::ng-deep
  .custom-form-field.mat-form-field-appearance-fill
  .mdc-line-ripple::after {
  border-bottom-width: 0;
}

/* Style for the label */
::ng-deep .custom-form-field .mat-mdc-floating-label {
  font-size: 14px;
  color: var(--secondary-dark-color) !important;
  padding: 5px;
}

/* Style for the input */
::ng-deep .custom-form-field input.mat-mdc-input-element {
  padding-top: 10px;
  font-size: 14px;
  color: black;
}

/* Style for the icons */
::ng-deep .custom-form-field .mat-icon {
  color: #cd7a88;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  height: 18px;
  width: 18px;
}

/* Style for the submit button */
.submit-button {
  background-color: #cd7a88;
  color: white !important;
  font-weight: 600;
  padding: 10px;
  border-radius: 0.25rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin: 0 auto;
  min-width: 150px;
}

.submit-button:hover {
  background-color: var(--secondary-dark-color);
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

input.mat-mdc-input-element::placeholder {
  font-size: 14px !important;
}

/* Style for action buttons container */
.actions-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

/* Add some responsive adjustments */
@media (max-width: 640px) {
  /* No specific adjustments needed */
}
