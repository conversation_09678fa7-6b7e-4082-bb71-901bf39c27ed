<form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="space-y-6" @formAnimation>
  <!-- Error message -->
  @if (error) {
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
      <span class="block sm:inline">{{ error }}</span>
    </div>
  }

  <div class="space-y-2">
    <mat-form-field appearance="fill" class="w-full custom-form-field">
      <mat-label>{{ 'auth.email' | translate }}</mat-label>
      <mat-icon matPrefix class="mr-2 text-gray-500">email</mat-icon>
      <input matInput type="email" formControlName="email" placeholder="<EMAIL>" required>
      @if (loginForm.get('email')?.invalid) {
        <mat-error>
          {{ getErrorMessage('email') | translate }}
        </mat-error>
      }
    </mat-form-field>
  </div>

  <div class="space-y-2">
    <mat-form-field appearance="fill" class="w-full custom-form-field">
      <mat-label>{{ 'auth.password' | translate }}</mat-label>
      <mat-icon matPrefix class="mr-2 text-gray-500">lock</mat-icon>
      <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" required>
      <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button" class="text-gray-500">
        <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
      </button>
      @if (loginForm.get('password')?.invalid) {
        <mat-error>
          {{ getErrorMessage('password') | translate }}
        </mat-error>
      }
    </mat-form-field>
  </div>

  <div class="flex items-center justify-between">
    <mat-checkbox color="primary" class="text-gray-700">{{ 'auth.rememberMe' | translate }}</mat-checkbox>
    <a routerLink="/forgot-password" class="text-blue-600 hover:text-blue-800 text-sm transition duration-300">{{ 'auth.forgotPassword' | translate }}</a>
  </div>

  <button mat-raised-button color="primary" type="submit" [disabled]="loginForm.invalid || loading" class="w-full py-3 rounded-lg text-lg font-medium shadow-md hover:shadow-lg transition duration-300">
    <span class="flex items-center justify-center">
      @if (loading) {
        <mat-spinner diameter="24" class="mr-2"></mat-spinner>
        {{ 'common.loading' | translate }}
      } @else {
        <mat-icon class="mr-2">login</mat-icon>
        {{ 'auth.login' | translate }}
      }
    </span>
  </button>
</form>
