import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { QuestionSeries, QuestionSeriesItem } from '../../models/user/QuestionSeries';
import { BehaviorSubject, Observable } from 'rxjs';
import { AuthService } from '../auth.service';

@Injectable({
  providedIn: 'root'
})
export class QuestionSeriesService {
  private readonly apiUrl = '/api/quizzes';

  private seriesSubject = new BehaviorSubject<QuestionSeries[]>([]);
  public series$ = this.seriesSubject.asObservable();

  private currentSeriesSubject = new BehaviorSubject<QuestionSeries | null>(null);
  public currentSeries$ = this.currentSeriesSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private authService: AuthService
  ) {}

  /**
   * Get all question series for the current user
   * @returns Promise with the question series
   */
  public getQuestionSeries(): Promise<QuestionSeries[]> {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      return Promise.resolve([]);
    }

    return this.apiService.get<QuestionSeries[]>(`${this.apiUrl}?userId=${currentUser.id}`)
      .then(response => {
        const series = response.data;
        this.seriesSubject.next(series);
        return series;
      })
      .catch(error => {
        console.error('Error fetching question series:', error);
        return [];
      });
  }

  /**
   * Get a question series by ID
   * @param id The question series ID
   * @returns Promise with the question series
   */
  public getQuestionSeriesById(id: number): Promise<QuestionSeries | null> {
    return this.apiService.get<QuestionSeries>(`${this.apiUrl}/${id}`)
      .then(response => {
        const series = response.data;
        this.currentSeriesSubject.next(series);
        return series;
      })
      .catch(error => {
        console.error(`Error fetching question series ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new question series
   * @param series The question series data
   * @returns Promise with the created question series
   */
  public createQuestionSeries(series: Omit<QuestionSeries, 'id'>): Promise<QuestionSeries | null> {
    console.log('Creating question series with data:', series);
    console.log('Questions array:', series.questions);

    // Ensure questions is an array
    if (!Array.isArray(series.questions)) {
      console.log('Questions is not an array, setting to empty array');
      series.questions = [];
    }

    // Check if questions array is empty
    if (series.questions.length === 0) {
      console.error('Cannot create a series with an empty questions array');
      return Promise.reject(new Error('Series must contain at least one question'));
    }

    return this.apiService.post<QuestionSeries>(this.apiUrl, series)
      .then(response => {
        const newSeries = response.data;
        console.log('Created question series:', newSeries);

        // Update the series subject with the new series
        const currentSeries = this.seriesSubject.value;
        this.seriesSubject.next([...currentSeries, newSeries]);

        // Set as current series
        this.currentSeriesSubject.next(newSeries);

        return newSeries;
      })
      .catch(error => {
        console.error('Error creating question series:', error);
        return null;
      });
  }

  /**
   * Update a question series
   * @param id The question series ID
   * @param series The question series data
   * @returns Promise with the updated question series
   */
  public updateQuestionSeries(id: number, series: Partial<QuestionSeries>): Promise<QuestionSeries | null> {
    console.log(`updateQuestionSeries called with id: ${id} and series:`, series);

    return this.apiService.put<QuestionSeries>(`${this.apiUrl}/${id}`, series)
      .then(response => {
        console.log('Update response:', response.data);
        const updatedSeries = response.data;

        // Update the series subject with the updated series
        const currentSeries = this.seriesSubject.value;
        console.log('Current series list:', currentSeries);

        const updatedSeriesList = currentSeries.map(s =>
          s.id === id ? updatedSeries : s
        );
        console.log('Updated series list:', updatedSeriesList);

        this.seriesSubject.next(updatedSeriesList);
        console.log('Series subject updated');

        // Update current series if it's the one being edited
        if (this.currentSeriesSubject.value?.id === id) {
          console.log('Updating current series subject');
          this.currentSeriesSubject.next(updatedSeries);
        }

        return updatedSeries;
      })
      .catch(error => {
        console.error(`Error updating question series ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete a question series
   * @param id The question series ID
   * @returns Promise with success status
   */
  public deleteQuestionSeries(id: number): Promise<boolean> {
    return this.apiService.delete(`${this.apiUrl}/${id}`)
      .then(() => {
        // Update the series subject by removing the deleted series
        const currentSeries = this.seriesSubject.value;
        this.seriesSubject.next(currentSeries.filter(s => s.id !== id));

        // Clear current series if it's the one being deleted
        if (this.currentSeriesSubject.value?.id === id) {
          this.currentSeriesSubject.next(null);
        }

        return true;
      })
      .catch(error => {
        console.error(`Error deleting question series ${id}:`, error);
        return false;
      });
  }

  /**
   * Add a question to the current series
   * @param questionItem The question to add
   */
  public addQuestionToCurrentSeries(questionItem: QuestionSeriesItem): void {
    console.log('addQuestionToCurrentSeries called with questionItem:', questionItem);

    const currentSeries = this.currentSeriesSubject.value;
    console.log('Current series:', currentSeries);

    if (!currentSeries) {
      console.log('No current series, cannot add question');
      return;
    }

    // Ensure questions is an array before checking
    if (!currentSeries.questions) {
      console.warn('Series questions was undefined in addQuestionToCurrentSeries, initializing to empty array');
      currentSeries.questions = [];
    }

    // Check if question is already in the series
    console.log('Current series questions:', currentSeries.questions);
    if (currentSeries.questions.includes(questionItem.questionId)) {
      console.log('Question already in series, not adding again');
      return;
    }

    // Add question to the series
    const updatedSeries = {
      ...currentSeries,
      questions: [...currentSeries.questions, questionItem.questionId]
    };
    console.log('Updated series:', updatedSeries);

    // Update the current series
    this.currentSeriesSubject.next(updatedSeries);
    console.log('Current series updated');

    // Save the updated series
    console.log('Saving updated series');
    this.updateQuestionSeries(currentSeries.id!, updatedSeries)
      .then(result => {
        console.log('Series update result:', result);
      })
      .catch(error => {
        console.error('Error updating series:', error);
      });
  }

  /**
   * Remove a question from the current series
   * @param questionId The ID of the question to remove
   */
  public removeQuestionFromCurrentSeries(questionId: number): void {
    const currentSeries = this.currentSeriesSubject.value;
    if (!currentSeries) return;

    // Ensure questions is an array before filtering
    if (!currentSeries.questions) {
      console.warn('Series questions was undefined in removeQuestionFromCurrentSeries, initializing to empty array');
      currentSeries.questions = [];
    }

    // Remove question from the series
    const updatedSeries = {
      ...currentSeries,
      questions: currentSeries.questions.filter(id => id !== questionId)
    };

    // Update the current series
    this.currentSeriesSubject.next(updatedSeries);

    // Save the updated series
    this.updateQuestionSeries(currentSeries.id!, updatedSeries);
  }

  /**
   * Set the current question series
   * @param series The question series to set as current
   */
  public setCurrentSeries(series: QuestionSeries | null): void {
    this.currentSeriesSubject.next(series);
  }

  /**
   * Create a new empty question series
   * @param name The name of the series
   * @param description The description of the series
   * @param questions Optional array of question IDs to include in the series
   * @returns Promise with the created question series
   */
  public createEmptySeries(name: string, description?: string, questions?: number[]): Promise<QuestionSeries | null> {
    console.log('Creating empty series with name:', name, 'and description:', description);

    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('No current user, cannot create series');
      return Promise.resolve(null);
    }

    const newSeries: Omit<QuestionSeries, 'id'> = {
      name,
      description,
      userId: currentUser.id,
      questions: questions || []
    };

    console.log('New series object:', newSeries);

    // If no questions are provided, reject the promise
    if (!questions || questions.length === 0) {
      console.error('Cannot create a series with an empty questions array');
      return Promise.reject(new Error('Series must contain at least one question'));
    }

    return this.createQuestionSeries(newSeries);
  }
}
