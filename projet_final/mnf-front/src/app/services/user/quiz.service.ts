import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { Quiz, QuizQuestion } from "../../models/user/Quiz";
import { BehaviorSubject, Observable } from "rxjs";
import { AuthService } from "../auth.service";

@Injectable({
  providedIn: "root",
})
export class QuizService {
  private readonly apiUrl = "/api/quizzes";

  // BehaviorSubject to store the current quiz
  private currentQuizSubject: BehaviorSubject<Quiz | null> =
    new BehaviorSubject<Quiz | null>(null);
  public currentQuiz$: Observable<Quiz | null> =
    this.currentQuizSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private authService: AuthService
  ) {}

  /**
   * Get all quizzes for the current user
   * @returns Promise with the quizzes
   */
  public getQuizzes(): Promise<Quiz[]> {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error("User not authenticated");
      return Promise.resolve([]);
    }

    return this.apiService
      .get<Quiz[]>(`${this.apiUrl}/user/${currentUser.id}`)
      .then((response) => {
        if (response && response.data) {
          return response.data;
        }
        return [];
      })
      .catch((error) => {
        console.error("Error fetching user quizzes:", error);
        return [];
      });
  }

  /**
   * Get the count of quizzes
   * @returns Promise with the count
   */
  public getQuizCount(): Promise<number> {
    return this.apiService
      .get<number>(`${this.apiUrl}/count`)
      .then((response) => {
        if (response && response.data) {
          return response.data;
        }
        return 0;
      })
      .catch((error) => {
        console.error("Error fetching quiz count:", error);
        return 0;
      });
  }

  /**
   * Get quizzes by user ID
   * @param userId The user ID
   * @returns Promise with the quizzes
   */
  public getQuizzesByUser(userId: number): Promise<Quiz[]> {
    return this.apiService
      .get<Quiz[]>(`${this.apiUrl}/user/${userId}`)
      .then((response) => {
        if (response && response.data) {
          return response.data;
        }
        return [];
      })
      .catch((error) => {
        console.error(`Error fetching quizzes for user ${userId}:`, error);
        return [];
      });
  }

  /**
   * Search quizzes by name
   * @param name The name to search for
   * @returns Promise with the quizzes
   */
  public searchQuizzes(name: string): Promise<Quiz[]> {
    return this.apiService
      .get<Quiz[]>(`${this.apiUrl}/search`, { params: { name } })
      .then((response) => {
        if (response && response.data) {
          return response.data;
        }
        return [];
      })
      .catch((error) => {
        console.error(`Error searching quizzes with name ${name}:`, error);
        return [];
      });
  }

  /**
   * Get a quiz by ID
   * @param quizId The quiz ID
   * @returns Promise with the quiz
   */
  public getQuizById(quizId: number): Promise<Quiz | null> {
    return this.apiService
      .get<Quiz>(`${this.apiUrl}/${quizId}`)
      .then((response) => {
        if (response && response.data) {
          return response.data;
        }
        return null;
      })
      .catch((error) => {
        console.error(`Error fetching quiz ${quizId}:`, error);
        return null;
      });
  }

  /**
   * Get quiz questions for a quiz
   * @param quizId The quiz ID
   * @returns Promise with the quiz questions
   */
  public getQuizQuestions(quizId: number): Promise<QuizQuestion[]> {
    return this.apiService
      .get<QuizQuestion[]>(`${this.apiUrl}/${quizId}/questions`)
      .then((response) => {
        if (response && response.data) {
          return response.data;
        }
        return [];
      })
      .catch((error) => {
        console.error(`Error fetching questions for quiz ${quizId}:`, error);
        return [];
      });
  }

  /**
   * Create a new quiz
   * @param quiz The quiz data
   * @returns Promise with the created quiz
   */
  public createQuiz(quiz: Omit<Quiz, "id">): Promise<Quiz | null> {
    // Ensure userId is set
    if (!quiz.userId && this.authService.currentUserValue) {
      quiz = { ...quiz, userId: this.authService.currentUserValue.id };
    }

    console.log("Quiz object in QuizService.createQuiz:", quiz);

    // Create a copy of the quiz object to ensure we're not modifying the original
    const quizToSend = { ...quiz };

    // Handle backward compatibility with questions array
    if (
      quizToSend.questions &&
      Array.isArray(quizToSend.questions) &&
      quizToSend.questions.length > 0
    ) {
      console.log("Questions array in quiz:", quizToSend.questions);
      console.log("Questions array length:", quizToSend.questions.length);

      // If quizQuestions is not provided or empty, create it from the questions array
      if (!quizToSend.quizQuestions || quizToSend.quizQuestions.length === 0) {
        console.log("Creating quizQuestions array from questions array");
        quizToSend.quizQuestions = quizToSend.questions.map(
          (questionId, index) => ({
            position: index + 1,
            question: {
              id: questionId,
              title: "", // These will be filled by the backend
              statement: "",
            },
          })
        );
      }
    }

    // Ensure quizQuestions is an array and not empty
    if (
      !quizToSend.quizQuestions ||
      !Array.isArray(quizToSend.quizQuestions) ||
      quizToSend.quizQuestions.length === 0
    ) {
      console.error("Cannot create a quiz with empty quizQuestions");
      return Promise.reject(
        new Error("Quiz must contain at least one question")
      );
    }

    // Ensure all quizQuestions have a valid question object with an id
    const invalidQuestions = quizToSend.quizQuestions.filter(
      (qq) => !qq.question || !qq.question.id
    );
    if (invalidQuestions.length > 0) {
      console.error(
        "Invalid quizQuestions found with missing question or question.id:",
        invalidQuestions
      );

      // Try to fix invalid questions if they have questionId or question_id
      const fixedQuizQuestions = quizToSend.quizQuestions.map((qq) => {
        if (!qq.question && (qq.questionId || qq.question_id)) {
          const questionId = qq.questionId || qq.question_id;
          return {
            ...qq,
            question: {
              id: Number(questionId),
              title: "", // These will be filled by the backend
              statement: "",
            },
          };
        }
        return qq;
      });

      // Check if we fixed all invalid questions
      const stillInvalidQuestions = fixedQuizQuestions.filter(
        (qq) => !qq.question || !qq.question.id
      );
      if (stillInvalidQuestions.length > 0) {
        return Promise.reject(
          new Error("Quiz questions must have valid question objects with IDs")
        );
      }

      quizToSend.quizQuestions = fixedQuizQuestions;
    }

    // Log the final quizQuestions array after all transformations
    console.log("Final quizQuestions array after transformations:");
    quizToSend.quizQuestions.forEach((qq, index) => {
      console.log(
        `Question ${index + 1}: position=${qq.position}, question.id=${
          qq.question?.id
        }`
      );
    });

    // Create the payload in the format expected by the backend DTO
    const payloadToSend = {
      name: quizToSend.name,
      description: quizToSend.description,
      userId: quizToSend.userId,
      quizQuestions: quizToSend.quizQuestions.map((qq) => ({
        position: qq.position,
        question: {
          id: qq.id,
        },
      })),
    };

    console.log("Modified payload to send:", payloadToSend);
    console.log(
      "quizQuestions array in final payload:",
      payloadToSend.quizQuestions
    );
    console.log(
      "quizQuestions array length in final payload:",
      payloadToSend.quizQuestions.length
    );

    return this.apiService
      .post<Quiz>(this.apiUrl, payloadToSend)
      .then((response) => {
        console.log("Quiz creation response:", response.data);
        if (response && response.data) {
          // Update current quiz if successful
          this.setCurrentQuiz(response.data);
          return response.data;
        }
        return null;
      })
      .catch((error) => {
        console.error("Error creating quiz:", error);
        throw error; // Rethrow the error to be handled by the caller
      });
  }

  /**
   * Update a quiz
   * @param quizId The quiz ID
   * @param quiz The quiz data
   * @returns Promise with the updated quiz
   */
  public updateQuiz(quizId: number, quiz: Partial<Quiz>): Promise<Quiz | null> {
    return this.apiService
      .put<Quiz>(`${this.apiUrl}/${quizId}`, quiz)
      .then((response) => {
        if (response && response.data) {
          // Update current quiz if it's the one being updated
          const currentQuiz = this.currentQuizSubject.getValue();
          if (currentQuiz && currentQuiz.id === quizId) {
            this.setCurrentQuiz(response.data);
          }
          return response.data;
        }
        return null;
      })
      .catch((error) => {
        console.error(`Error updating quiz ${quizId}:`, error);
        return null;
      });
  }

  /**
   * Delete a quiz
   * @param quizId The quiz ID
   * @returns Promise with success status
   */
  public deleteQuiz(quizId: number): Promise<boolean> {
    return this.apiService
      .delete(`${this.apiUrl}/${quizId}`)
      .then(() => {
        // Clear current quiz if it's the one being deleted
        const currentQuiz = this.currentQuizSubject.getValue();
        if (currentQuiz && currentQuiz.id === quizId) {
          this.setCurrentQuiz(null);
        }
        return true;
      })
      .catch((error) => {
        console.error(`Error deleting quiz ${quizId}:`, error);
        return false;
      });
  }

  /**
   * Set the current quiz
   * @param quiz The quiz to set as current
   */
  public setCurrentQuiz(quiz: Quiz | null): void {
    this.currentQuizSubject.next(quiz);
  }

  /**
   * Create an empty quiz with default values
   * @param name The quiz name
   * @param description The quiz description
   * @returns Promise with the created quiz
   */
  public createEmptyQuiz(
    name: string,
    description?: string
  ): Promise<Quiz | null> {
    if (!this.authService.currentUserValue) {
      console.error("User not authenticated");
      return Promise.resolve(null);
    }

    const quiz: Omit<Quiz, "id"> = {
      name,
      description: description || "",
      userId: this.authService.currentUserValue.id,
      questions: [], // Keep empty questions array for backward compatibility
      quizQuestions: [], // Empty quizQuestions array to match the new interface
    };

    // Since createQuiz now requires at least one question, we need to handle this case
    console.log(
      "Warning: Creating an empty quiz without questions. This will be rejected by createQuiz."
    );
    return Promise.reject(new Error("Quiz must contain at least one question"));
  }

  /**
   * Share a quiz with favorite users
   * @param quizId The ID of the quiz to share
   * @param favoriteUserIds Array of favorite user IDs to share with
   * @returns Promise with success status
   */
  public shareQuiz(
    quizId: number,
    favoriteUserIds: number[]
  ): Promise<boolean> {
    if (!this.authService.currentUserValue) {
      console.error("User not authenticated");
      return Promise.resolve(false);
    }

    const senderId = this.authService.currentUserValue.id;

    // Create an array of share requests, one for each favorite
    const sharePromises = favoriteUserIds.map((userId) => {
      return this.apiService.post(`${this.apiUrl}/${quizId}/share`, {
        sender_id: senderId,
        quiz_id: quizId,
        user_id: userId,
      });
    });

    // Execute all share requests and return true if all succeed
    return Promise.all(sharePromises)
      .then(() => {
        console.log(
          `Quiz ${quizId} shared successfully with ${favoriteUserIds.length} users`
        );
        return true;
      })
      .catch((error) => {
        console.error(`Error sharing quiz ${quizId}:`, error);
        return false;
      });
  }
}
