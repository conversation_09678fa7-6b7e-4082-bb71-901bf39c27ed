import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Favorite {
  id: number;
  userId: number;
  favoriteUserId: number;
  favoriteUserName?: string;
  favoriteUserEmail?: string;
  createdAt?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FavoritesService {
  private favoritesSubject: BehaviorSubject<Favorite[]>;
  public favorites$: Observable<Favorite[]>;

  // Mock data for development
  private mockFavorites: Favorite[] = [
    { id: 1, userId: 1, favoriteUserId: 2, favoriteUserName: '<PERSON>', favoriteUserEmail: '<EMAIL>', createdAt: new Date().toISOString() },
    { id: 2, userId: 1, favoriteUserId: 3, favoriteUserName: '<PERSON>', favoriteUserEmail: '<EMAIL>', createdAt: new Date().toISOString() }
  ];

  constructor(private apiService: ApiService) {
    this.favoritesSubject = new BehaviorSubject<Favorite[]>([]);
    this.favorites$ = this.favoritesSubject.asObservable();

    // Initialize with mock data
    this.favoritesSubject.next(this.mockFavorites);
  }

  /**
   * Get all favorites for the current user
   * @returns Promise with the favorites
   */
  getFavorites(userId: number): Promise<Favorite[]> {
    // In a real app, this would call your API
    // return this.apiService.get<Favorite[]>(`/api/favorites?userId=${userId}`)
    //   .then(response => response.data);

    // For now, return mock data
    const userFavorites = this.mockFavorites.filter(fav => fav.userId === userId);
    return Promise.resolve(userFavorites);
  }

  /**
   * Add a user to favorites
   * @param userId The current user's ID
   * @param favoriteUserId The ID of the user to add to favorites
   * @param favoriteUserName The name of the user to add to favorites
   * @param favoriteUserEmail The email of the user to add to favorites
   * @returns Promise with the created favorite
   */
  addFavorite(userId: number, favoriteUserId: number, favoriteUserName?: string, favoriteUserEmail?: string): Promise<Favorite> {
    // In a real app, this would call your API
    // return this.apiService.post<Favorite>('/api/favorites', { userId, favoriteUserId })
    //   .then(response => response.data);

    // For now, simulate adding a favorite
    const newFavorite: Favorite = {
      id: this.mockFavorites.length + 1,
      userId,
      favoriteUserId,
      favoriteUserName,
      favoriteUserEmail,
      createdAt: new Date().toISOString()
    };

    this.mockFavorites.push(newFavorite);
    this.favoritesSubject.next([...this.mockFavorites]);
    return Promise.resolve(newFavorite);
  }

  /**
   * Remove a user from favorites
   * @param favoriteId The ID of the favorite to remove
   * @returns Promise with a boolean indicating success
   */
  removeFavorite(favoriteId: number): Promise<boolean> {
    // In a real app, this would call your API
    // return this.apiService.delete(`/api/favorites/${favoriteId}`)
    //   .then(() => true)
    //   .catch(() => false);

    // For now, simulate removing a favorite
    const index = this.mockFavorites.findIndex(fav => fav.id === favoriteId);
    if (index === -1) {
      return Promise.resolve(false);
    }

    this.mockFavorites.splice(index, 1);
    this.favoritesSubject.next([...this.mockFavorites]);
    return Promise.resolve(true);
  }

  /**
   * Check if a user is in favorites
   * @param userId The current user's ID
   * @param favoriteUserId The ID of the user to check
   * @returns Promise with a boolean indicating if the user is in favorites
   */
  isFavorite(userId: number, favoriteUserId: number): Promise<boolean> {
    // In a real app, this would call your API
    // return this.apiService.get<{ isFavorite: boolean }>(`/api/favorites/check?userId=${userId}&favoriteUserId=${favoriteUserId}`)
    //   .then(response => response.data.isFavorite);

    // For now, check mock data
    const favorite = this.mockFavorites.find(fav => fav.userId === userId && fav.favoriteUserId === favoriteUserId);
    return Promise.resolve(!!favorite);
  }
}
