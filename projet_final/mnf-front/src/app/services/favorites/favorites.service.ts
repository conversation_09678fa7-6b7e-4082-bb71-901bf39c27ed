import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Favorite {
  id: number;
  userId: number;
  favoriteUserId: number;
  favoriteUserName?: string;
  favoriteUserEmail?: string;
  createdAt?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FavoritesService {
  private favoritesSubject: BehaviorSubject<Favorite[]>;
  public favorites$: Observable<Favorite[]>;


  constructor(private apiService: ApiService) {
    this.favoritesSubject = new BehaviorSubject<Favorite[]>([]);
    this.favorites$ = this.favoritesSubject.asObservable();
  }

  /**
   * Get all favorites for the current user
   * @returns Promise with the favorites
   */
  getFavorites(userId: number): Promise<Favorite[]> {
    return this.apiService.get<Favorite[]>(`/api/user/favorites`)
      .then(response => {
        const favorites = response.data;
        this.favoritesSubject.next(favorites);
        return favorites;
      });
  }

  /**
   * Add a user to favorites
   * @param userId The current user's ID
   * @param favoriteUserId The ID of the user to add to favorites
   * @param favoriteUserName The name of the user to add to favorites
   * @param username The email of the user to add to favorites
   * @returns Promise with the created favorite
   */
  addFavorite(userId: number, favoriteUserId: number, favoriteUserName?: string, username?: string): Promise<Favorite> {
    return this.apiService.post<Favorite>(`/api/user/favorites`, {
      userId,
      favoriteUserId,
      favoriteUserName,
      username
    })
      .then(response => {
        const newFavorite = response.data;

        // Update the favorites subject with the new favorite
        const currentFavorites = this.favoritesSubject.value;
        this.favoritesSubject.next([...currentFavorites, newFavorite]);

        return newFavorite;
      });
  }

  /**
   * Remove a user from favorites
   * @param favoriteIdOrEmail The ID or email of the favorite to remove
   * @returns Promise with a boolean indicating success
   */
  removeFavorite(favoriteIdOrEmail: number | string): Promise<boolean> {
    return this.apiService.delete(`/api/user/favorites/${favoriteIdOrEmail}`)
      .then(() => {
        // Update the favorites subject by removing the favorite
        const currentFavorites = this.favoritesSubject.value;
        const updatedFavorites = typeof favoriteIdOrEmail === 'number'
          ? currentFavorites.filter(fav => fav.id !== favoriteIdOrEmail)
          : currentFavorites.filter(fav => fav.favoriteUserEmail !== favoriteIdOrEmail);
        this.favoritesSubject.next(updatedFavorites);

        return true;
      })
      .catch(error => {
        console.error('Error removing favorite:', error);
        return false;
      });
  }

  /**
   * Check if a user is in favorites
   * @param userId The current user's ID
   * @param favoriteUserId The ID of the user to check
   * @returns Promise with a boolean indicating if the user is in favorites
   */
  isFavorite(userId: number, favoriteUserId: number): Promise<boolean> {
    return this.apiService.get<{ isFavorite: boolean }>(`/api/user/favorites/check?userId=${userId}&favoriteUserId=${favoriteUserId}`)
      .then(response => response.data.isFavorite)
      .catch(error => {
        console.error('Error checking favorite status:', error);
        return false;
      });
  }

  /**
   * Add a user to favorites by email
   * @param favoriteUsername The email of the user to add to favorites
   * @returns Promise with the created favorite
   */
  addFavoriteByEmail(favoriteUsername: string): Promise<Favorite> {
    return this.apiService.post<Favorite>(`/api/user/favorites`, { favoriteUsername})
      .then(response => {
        const newFavorite = response.data;

        // Update the favorites subject with the new favorite
        const currentFavorites = this.favoritesSubject.value;
        this.favoritesSubject.next([...currentFavorites, newFavorite]);

        return newFavorite;
      });
  }

  /**
   * Add a user to favorites by username (deprecated, use addFavoriteByEmail instead)
   * @param favoriteUsername The username of the user to add to favorites
   * @returns Promise with the created favorite
   */
  addFavoriteByUsername(favoriteUsername: string): Promise<Favorite> {
    console.warn('addFavoriteByUsername is deprecated, use addFavoriteByEmail instead');
    // For backward compatibility, we'll keep this method but it should be updated in the calling code
    return this.apiService.post<Favorite>(`/api/user/favorites`, { favoriteUsername })
      .then(response => {
        const newFavorite = response.data;

        // Update the favorites subject with the new favorite
        const currentFavorites = this.favoritesSubject.value;
        this.favoritesSubject.next([...currentFavorites, newFavorite]);

        return newFavorite;
      });
  }

  /**
   * Remove a user from favorites by email
   * @param favoriteEmail The email of the user to remove from favorites
   * @returns Promise with a boolean indicating success
   */
  removeFavoriteByEmail(favoriteEmail: string): Promise<boolean> {
    return this.apiService.delete(`/api/user/favorites/${favoriteEmail}`)
      .then(() => {
        // Update the favorites subject by removing the favorite with the matching email
        const currentFavorites = this.favoritesSubject.value;
        const updatedFavorites = currentFavorites.filter(fav =>
          fav.favoriteUserEmail?.toLowerCase() !== favoriteEmail.toLowerCase()
        );
        this.favoritesSubject.next(updatedFavorites);

        return true;
      })
      .catch(error => {
        console.error('Error removing favorite by email:', error);
        return false;
      });
  }

  /**
   * Remove a user from favorites by username (deprecated, use removeFavoriteByEmail instead)
   * @param favoriteUsername The username of the user to remove from favorites
   * @returns Promise with a boolean indicating success
   */
  removeFavoriteByUsername(favoriteUsername: string): Promise<boolean> {
    console.warn('removeFavoriteByUsername is deprecated, use removeFavoriteByEmail instead');
    // For backward compatibility, we'll keep this method but it should be updated in the calling code
    return this.apiService.delete(`/api/user/favorites/${favoriteUsername}`)
      .then(() => {
        // Update the favorites subject by removing the favorite with the matching username
        const currentFavorites = this.favoritesSubject.value;
        const updatedFavorites = currentFavorites.filter(fav =>
          fav.favoriteUserName?.toLowerCase() !== favoriteUsername.toLowerCase()
        );
        this.favoritesSubject.next(updatedFavorites);

        return true;
      })
      .catch(error => {
        console.error('Error removing favorite by username:', error);
        return false;
      });
  }

  /**
   * Get favorites for the current user
   * @returns Promise with the favorites
   */
  getCurrentUserFavorites(): Promise<Favorite[]> {
    return this.apiService.get<Favorite[]>('/api/user/favorites')
      .then(response => {
        const favorites = response.data;
        this.favoritesSubject.next(favorites);
        return favorites;
      });
  }
}
