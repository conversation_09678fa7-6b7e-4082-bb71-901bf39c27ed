import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Promotion } from '../../models/admin/Promotion';

@Injectable({
  providedIn: 'root'
})
export class PromotionService {
  private readonly apiUrl = '/api/admin/promotions';

  constructor(private apiService: ApiService) {}

  /**
   * Get all promotions
   * @returns Promise with the promotions
   */
  public getPromotions(): Promise<Promotion[]> {
    return this.apiService.get<Promotion[]>(this.apiUrl)
      .then(response => response.data)
      .catch(error => {
        console.error('Error fetching promotions:', error);
        return [];
      });
  }

  /**
   * Get a promotion by ID
   * @param id The promotion ID
   * @returns Promise with the promotion
   */
  public getPromotionById(id: number): Promise<Promotion | null> {
    return this.apiService.get<Promotion>(`${this.apiUrl}/${id}`)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error fetching promotion ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new promotion
   * @param promotion The promotion data
   * @returns Promise with the created promotion
   */
  public createPromotion(promotion: Omit<Promotion, 'id'>): Promise<Promotion | null> {
    return this.apiService.post<Promotion>(this.apiUrl, promotion)
      .then(response => response.data)
      .catch(error => {
        console.error('Error creating promotion:', error);
        return null;
      });
  }

  /**
   * Update a promotion
   * @param id The promotion ID
   * @param promotion The promotion data
   * @returns Promise with the updated promotion
   */
  public updatePromotion(id: number, promotion: Partial<Promotion>): Promise<Promotion | null> {
    return this.apiService.put<Promotion>(`${this.apiUrl}/${id}`, promotion)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error updating promotion ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete a promotion
   * @param id The promotion ID
   * @returns Promise with success status
   */
  public deletePromotion(id: number): Promise<boolean> {
    return this.apiService.delete(`${this.apiUrl}/${id}`)
      .then(() => true)
      .catch(error => {
        console.error(`Error deleting promotion ${id}:`, error);
        return false;
      });
  }
}
