import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { Promotion } from "../../models/admin/Promotion";

@Injectable({
  providedIn: "root",
})
export class PromotionService {
  private readonly apiUrl = "/api/promotions";

  constructor(private apiService: ApiService) {}

  /**
   * Maps API response format to Promotion model format
   * @param apiPromotion The promotion data from API
   * @returns Promotion object with mapped fields
   */
  private mapApiPromotionToModel(apiPromotion: any): Promotion {
    let name = apiPromotion.nom || apiPromotion.name; // Handle both "nom" and "name" fields

    // Replace "AAAAAAAAAAAAAAAAAAAAA" with a more meaningful default
    if ((name || "").includes("AAAAAAAA")) {
      name = "Unknown Promotion";
    }

    return {
      id: apiPromotion.id,
      name: name,
    };
  }

  /**
   * Maps Promotion model format to API request format
   * @param modelPromotion The promotion data from model
   * @returns Object with mapped fields for API
   */
  private mapModelPromotionToApi(modelPromotion: Partial<Promotion>): any {
    const apiPromotion: any = {
      nom: modelPromotion.name, // Use name for the API's nom field
    };

    // Include id if it exists (for updates)
    if (modelPromotion.id !== undefined) {
      apiPromotion.id = modelPromotion.id;
    }

    return apiPromotion;
  }

  /**
   * Get the total count of promotions
   * @param search Optional search term for filtering promotions
   * @returns Promise with the count
   */
  public getPromotionsTotal(): Promise<number> {
    return this.apiService
      .get<number>(`${this.apiUrl}/total`)
      .then((response) => {
        if (response && response.data !== undefined) {
          return response.data;
        }
        return 0;
      })
      .catch((error) => {
        console.error("Error fetching promotions count:", error);
        return 0;
      });
  }

  /**
   * Get all promotions with pagination and search
   * @param search Optional search term for filtering promotions
   * @param currentPage Current page number (0-based)
   * @param pageSize Number of items per page
   * @param sortBy Field to sort by (default: 'id')
   * @returns Promise with the promotions
   */
  public getPromotions(
    search?: string,
    currentPage: number = 0,
    pageSize: number = 10,
    sortBy: string = "id"
  ): Promise<{
    content: Promotion[];
    totalElements: number;
    totalPages: number;
  }> {
    // Build query parameters
    const params: Record<string, string> = {
      currentPage: currentPage.toString(),
      pageSize: pageSize.toString(),
      sortBy: sortBy,
    };

    // Add search parameter if provided
    if (search) {
      params["search"] = search;
    }

    return this.apiService
      .get<any>(this.apiUrl, { params })
      .then((response) => {
        if (response && response.data) {
          let promotions: Promotion[] = [];
          let totalElements = 0;
          let totalPages = 0;

          // Handle paginated response format
          if (response.data.content && Array.isArray(response.data.content)) {
            promotions = response.data.content.map((promotion: any) =>
              this.mapApiPromotionToModel(promotion)
            );
            totalElements = response.data.totalElements || promotions.length;
            totalPages =
              response.data.totalPages || Math.ceil(totalElements / pageSize);
          }
          // Handle direct array in data
          else if (Array.isArray(response.data)) {
            promotions = response.data.map((promotion: any) =>
              this.mapApiPromotionToModel(promotion)
            );
            totalElements = promotions.length;
            totalPages = Math.ceil(totalElements / pageSize);
          }
          // Handle promotions array in data.promotions
          else if (
            response.data.promotions &&
            Array.isArray(response.data.promotions)
          ) {
            promotions = response.data.promotions.map((promotion: any) =>
              this.mapApiPromotionToModel(promotion)
            );
            totalElements = promotions.length;
            totalPages = Math.ceil(totalElements / pageSize);
          }
          // Handle promotions array in data.promotionDtos
          else if (
            response.data.promotionDtos &&
            Array.isArray(response.data.promotionDtos)
          ) {
            promotions = response.data.promotionDtos.map((promotion: any) =>
              this.mapApiPromotionToModel(promotion)
            );
            totalElements = promotions.length;
            totalPages = Math.ceil(totalElements / pageSize);
          }

          return {
            content: promotions,
            totalElements,
            totalPages,
          };
        }
        console.error(
          "Error: API response is not in expected format",
          response
        );
        return {
          content: [],
          totalElements: 0,
          totalPages: 0,
        };
      })
      .catch((error) => {
        console.error("Error fetching promotions:", error);
        return {
          content: [],
          totalElements: 0,
          totalPages: 0,
        };
      });
  }

  /**
   * Get a promotion by ID
   * @param id The promotion ID
   * @returns Promise with the promotion
   */
  public getPromotionById(id: number): Promise<Promotion | null> {
    return this.apiService
      .get<any>(`${this.apiUrl}/${id}`)
      .then((response) => {
        if (response && response.data) {
          // Handle direct object in data
          if (
            !Array.isArray(response.data) &&
            typeof response.data === "object"
          ) {
            // Check if it's a promotion object directly
            if (response.data.id !== undefined) {
              return this.mapApiPromotionToModel(response.data);
            }
            // Check if it's in promotionDto
            else if (response.data.promotionDto) {
              return this.mapApiPromotionToModel(response.data.promotionDto);
            }
            // Check if it's in promotionDtos array
            else if (
              response.data.promotionDtos &&
              Array.isArray(response.data.promotionDtos) &&
              response.data.promotionDtos.length > 0
            ) {
              return this.mapApiPromotionToModel(
                response.data.promotionDtos[0]
              );
            }
          }
          // Handle array in data (take the first item)
          else if (Array.isArray(response.data) && response.data.length > 0) {
            return this.mapApiPromotionToModel(response.data[0]);
          }
        }
        console.error(
          `Error fetching promotion ${id}: Invalid response format`,
          response
        );
        return null;
      })
      .catch((error) => {
        console.error(`Error fetching promotion ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new promotion
   * @param promotion The promotion data
   * @returns Promise with the created promotion
   */
  public createPromotion(
    promotion: Omit<Promotion, "id">
  ): Promise<Promotion | null> {
    // Convert from model format to API format
    const apiPromotion = this.mapModelPromotionToApi(promotion);

    return this.apiService
      .post<any>(`${this.apiUrl}/promotion`, apiPromotion)
      .then((response) => {
        if (response && response.data) {
          // Handle direct object in data
          if (
            !Array.isArray(response.data) &&
            typeof response.data === "object"
          ) {
            // Check if it's a promotion object directly
            if (response.data.id !== undefined) {
              return this.mapApiPromotionToModel(response.data);
            }
            // Check if it's in promotionDto
            else if (response.data.promotionDto) {
              return this.mapApiPromotionToModel(response.data.promotionDto);
            }
            // Check if it's in promotionDtos array
            else if (
              response.data.promotionDtos &&
              Array.isArray(response.data.promotionDtos) &&
              response.data.promotionDtos.length > 0
            ) {
              return this.mapApiPromotionToModel(
                response.data.promotionDtos[0]
              );
            }
          }
          // Handle array in data (take the first item)
          else if (Array.isArray(response.data) && response.data.length > 0) {
            return this.mapApiPromotionToModel(response.data[0]);
          }
        }
        console.error(
          "Error creating promotion: Invalid response format",
          response
        );
        return null;
      })
      .catch((error) => {
        console.error("Error creating promotion:", error);
        return null;
      });
  }

  /**
   * Update a promotion
   * @param id The promotion ID
   * @param promotion The promotion data
   * @returns Promise with the updated promotion
   */
  public updatePromotion(
    id: number,
    promotion: Partial<Promotion>
  ): Promise<Promotion | null> {
    // Convert from model format to API format
    const apiPromotion = this.mapModelPromotionToApi(promotion);

    return this.apiService
      .patch<any>(`${this.apiUrl}/${id}`, apiPromotion)
      .then((response) => {
        if (response && response.data) {
          // Handle direct object in data
          if (
            !Array.isArray(response.data) &&
            typeof response.data === "object"
          ) {
            // Check if it's a promotion object directly
            if (response.data.id !== undefined) {
              return this.mapApiPromotionToModel(response.data);
            }
            // Check if it's in promotionDto
            else if (response.data.promotionDto) {
              return this.mapApiPromotionToModel(response.data.promotionDto);
            }
            // Check if it's in promotionDtos array
            else if (
              response.data.promotionDtos &&
              Array.isArray(response.data.promotionDtos) &&
              response.data.promotionDtos.length > 0
            ) {
              return this.mapApiPromotionToModel(
                response.data.promotionDtos[0]
              );
            }
          }
          // Handle array in data (take the first item)
          else if (Array.isArray(response.data) && response.data.length > 0) {
            return this.mapApiPromotionToModel(response.data[0]);
          }
        }
        console.error(
          `Error updating promotion ${id}: Invalid response format`,
          response
        );
        return null;
      })
      .catch((error) => {
        console.error(`Error updating promotion ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete a promotion
   * @param id The promotion ID
   * @returns Promise with success status and error message if applicable
   */
  public deletePromotion(
    id: number
  ): Promise<{ success: boolean; errorMessage?: string }> {
    return this.apiService
      .delete(`${this.apiUrl}/${id}`)
      .then(() => ({ success: true }))
      .catch((error) => {
        console.error(`Error deleting promotion ${id}:`, error);

        // Check for foreign key constraint violation
        if (
          error &&
          error.response &&
          error.response.data &&
          (error.response.data.message?.includes("foreign key constraint") ||
            error.response.data.error?.includes("foreign key constraint") ||
            error.response.data.includes("foreign key constraint"))
        ) {
          return {
            success: false,
            errorMessage:
              "Cannot delete this promotion because it has interns assigned to it. Please reassign or remove the interns first.",
          };
        }

        return {
          success: false,
          errorMessage: "Failed to delete promotion. Please try again.",
        };
      });
  }
}
