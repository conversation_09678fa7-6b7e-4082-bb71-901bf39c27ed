import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Question } from '../../models/admin/Question';

@Injectable({
  providedIn: 'root'
})
export class QuestionService {
  private readonly apiUrl = '/api/questions';

  constructor(private apiService: ApiService) {
  }

  /**
   * Maps API response format to Question model format
   * @param apiQuestion The question data from API
   * @returns Question object with mapped fields
   */
  private mapApiQuestionToModel(apiQuestion: any): Question {
    return {
      id: apiQuestion.id,
      title: apiQuestion.title || apiQuestion.titre || '',
      statement: apiQuestion.text || apiQuestion.contenu || apiQuestion.statement || '',
      chapter_id: apiQuestion.chapter_id || apiQuestion.chapitre_id || (apiQuestion.chapitre?.id) || 0,
      chapter: apiQuestion.chapitreTitle ?
        { id: 0, name: apiQuestion.chapitreTitle, title: apiQuestion.chapitreTitle, path: '' } :
        apiQuestion.chapitre ?
        { id: apiQuestion.chapitre.id, name: apiQuestion.chapitre.nom || apiQuestion.chapitre.titre, title: apiQuestion.chapitre.titre || apiQuestion.chapitre.nom, path: apiQuestion.chapitre.path || '' } :
        undefined
    };
  }

  /**
   * Get the total count of questions
   * @returns Promise with the count
   */
  public getQuestionsTotal(): Promise<number> {
    return this.apiService
      .get<number>(`${this.apiUrl}/total`)
      .then((response) => {
        if (response && response.data !== undefined) {
          return response.data;
        }
        return 0;
      })
      .catch((error) => {
        console.error("Error fetching questions count:", error);
        return 0;
      });
  }

  /**
   * Get all questions with pagination and search
   * @param search Optional search term for filtering questions
   * @param currentPage Current page number (0-based)
   * @param pageSize Number of items per page
   * @param sortBy Field to sort by (default: 'id')
   * @returns Promise with the questions
   */
  public getQuestions(
    search?: string,
    currentPage: number = 0,
    pageSize: number = 10,
    sortBy: string = 'id'
  ): Promise<{ content: Question[], totalElements: number, totalPages: number }> {
    // Build query parameters
    const params: Record<string, string> = {
      currentPage: currentPage.toString(),
      pageSize: pageSize.toString(),
      sortBy: sortBy
    };

    // Add search parameter if provided
    if (search) {
      params['search'] = search;
    }

    return this.apiService
      .get<any>(this.apiUrl, { params })
      .then((response) => {
        if (response && response.data) {
          let questions: Question[] = [];
          let totalElements = 0;
          let totalPages = 0;

          // Handle QuestionAffichageDto response format
          if (response.data.QuestionAffichageDto && Array.isArray(response.data.QuestionAffichageDto)) {
            console.log('Handling QuestionAffichageDto response format', response.data.QuestionAffichageDto);
            questions = response.data.QuestionAffichageDto.map((question: any, index: number) =>
              this.mapApiQuestionToModel({
                id: index + 1, // Generate temporary IDs
                titre: question.titre,
                contenu: question.contenu,
                chapitreTitle: question.chapitreTitle
              })
            );
            totalElements = questions.length;
            totalPages = Math.ceil(totalElements / pageSize);
          }
          // Handle paginated response format
          else if (response.data.content && Array.isArray(response.data.content)) {
            questions = response.data.content.map((question: any) =>
              this.mapApiQuestionToModel(question)
            );
            totalElements = response.data.totalElements || questions.length;
            totalPages = response.data.totalPages || Math.ceil(totalElements / pageSize);
          }
          // Handle direct array in data
          else if (Array.isArray(response.data)) {
            questions = response.data.map((question: any) =>
              this.mapApiQuestionToModel(question)
            );
            totalElements = questions.length;
            totalPages = Math.ceil(totalElements / pageSize);
          }

          return {
            content: questions,
            totalElements,
            totalPages
          };
        }
        console.error(
          "Error: API response is not in expected format",
          response
        );
        return {
          content: [],
          totalElements: 0,
          totalPages: 0
        };
      })
      .catch((error) => {
        console.error("Error fetching questions:", error);
        return {
          content: [],
          totalElements: 0,
          totalPages: 0
        };
      });
  }

  /**
   * Get a question by ID
   * @param id The question ID
   * @returns Promise with the question
   */
  public getQuestionById(id: number): Promise<Question | null> {
    return this.apiService.get<any>(`${this.apiUrl}/${id}`)
      .then(response => {
        if (response.data) {
          return this.mapApiQuestionToModel(response.data);
        }
        return null;
      })
      .catch(error => {
        console.error(`Error fetching question ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new question
   * @param question The question data
   * @returns Promise with the created question
   */
  public createQuestion(question: Omit<Question, 'id'>): Promise<Question | null> {
    // Map frontend data to backend DTO format
    const addQuestionDto = {
      titre: question.title,
      contenu: question.statement,
      chapitre: question.chapter_id ? { id: question.chapter_id } : null,
      reponses: [] // Empty for now, will be handled separately
    };

    return this.apiService.post<any>(`${this.apiUrl}/question`, addQuestionDto)
      .then(response => {
        // Backend returns a success message, not the question object
        // We'll return a mock question object for now
        return {
          id: Date.now(), // Temporary ID
          title: question.title,
          statement: question.statement,
          chapter_id: question.chapter_id
        } as Question;
      })
      .catch(error => {
        console.error('Error creating question:', error);
        return null;
      });
  }

  /**
   * Update a question
   * @param id The question ID
   * @param question The question data
   * @returns Promise with the updated question
   */
  public updateQuestion(id: number, question: Partial<Question>): Promise<Question | null> {
    // Map frontend data to backend DTO format
    const questionDto = {
      id: id,
      titre: question.title,
      contenu: question.statement,
      chapitre: question.chapter_id ? { id: question.chapter_id } : null,
      reponses: [] // Empty for now, will be handled separately
    };

    return this.apiService.patch<any>(`${this.apiUrl}/${id}`, questionDto)
      .then(response => {
        // Backend returns a success message, not the question object
        // We'll return the updated question object
        return {
          id: id,
          title: question.title || '',
          statement: question.statement || '',
          chapter_id: question.chapter_id || 0
        } as Question;
      })
      .catch(error => {
        console.error(`Error updating question ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete a question
   * @param id The question ID
   * @returns Promise with success status
   */
  public deleteQuestion(id: number): Promise<boolean> {
    return this.apiService.delete(`${this.apiUrl}/${id}`)
      .then(() => true)
      .catch(error => {
        console.error(`Error deleting question ${id}:`, error);
        return false;
      });
  }

  /**
   * Get questions by chapter ID
   * @param chapterId The chapter ID
   * @returns Promise with the questions
   */
  public getQuestionsByChapterId(chapterId: number): Promise<Question[]> {
    return this.apiService.get<Question[]>(`${this.apiUrl}/chapter/${chapterId}`)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error fetching questions for chapter ${chapterId}:`, error);
        return [];
      });
  }
}
