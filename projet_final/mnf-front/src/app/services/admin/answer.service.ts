import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Answer } from '../../models/admin/Answer';

@Injectable({
  providedIn: 'root'
})
export class AnswerService {
  private readonly apiUrl = '/api/reponses';

  constructor(private apiService: ApiService) {}

  /**
   * Maps API response to Answer model
   * @param item The API response item
   * @returns Mapped Answer object or null if item is falsy
   */
  private mapApiResponseToAnswer(item: any): Answer | null {
    if (!item) return null;

    return {
      id: item.id,
      label: item.label,
      text: item.contenu || item.text, // Map contenu to text
      valid_answer: item.is_correct !== undefined ? item.is_correct : item.valid_answer, // Map is_correct to valid_answer
      question_id: item.question_id,
      question: item.question
    };
  }

  /**
   * Get the total count of answers
   * @returns Promise with the count
   */
  public getAnswersTotal(): Promise<number> {
    return this.apiService
      .get<number>(`${this.apiUrl}/total`)
      .then((response) => {
        if (response && response.data !== undefined) {
          return response.data;
        }
        return 0;
      })
      .catch((error) => {
        console.error("Error fetching answers count:", error);
        return 0;
      });
  }

  /**
   * Get all answers with pagination and search
   * @param search Optional search term for filtering answers
   * @param currentPage Current page number (0-based)
   * @param pageSize Number of items per page
   * @param sortBy Field to sort by (default: 'id')
   * @returns Promise with the answers
   */
  public getAnswers(
    search?: string,
    currentPage: number = 0,
    pageSize: number = 10,
    sortBy: string = 'id'
  ): Promise<{ content: Answer[], totalElements: number, totalPages: number }> {
    // Build query parameters
    const params: Record<string, string> = {
      currentPage: currentPage.toString(),
      pageSize: pageSize.toString(),
      sortBy: sortBy
    };

    // Add search parameter if provided
    if (search) {
      params['search'] = search;
    }

    return this.apiService
      .get<any>(this.apiUrl, { params })
      .then((response) => {
        if (response && response.data) {
          let answers: Answer[] = [];
          let totalElements = 0;
          let totalPages = 0;

          // Handle paginated response format
          if (response.data.content && Array.isArray(response.data.content)) {
            answers = response.data.content
              .map((item: any) => this.mapApiResponseToAnswer(item))
              .filter((answer: Answer | null): answer is Answer => answer !== null);
            totalElements = response.data.totalElements || answers.length;
            totalPages = response.data.totalPages || Math.ceil(totalElements / pageSize);
          }
          // Handle direct array in data
          else if (Array.isArray(response.data)) {
            answers = response.data
              .map((item: any) => this.mapApiResponseToAnswer(item))
              .filter((answer: Answer | null): answer is Answer => answer !== null);
            totalElements = answers.length;
            totalPages = Math.ceil(totalElements / pageSize);
          }
          // Handle answers array in data.reponses
          else if (
            response.data.reponses &&
            Array.isArray(response.data.reponses)
          ) {
            answers = response.data.reponses
              .map((item: any) => this.mapApiResponseToAnswer(item))
              .filter((answer: Answer | null): answer is Answer => answer !== null);
            totalElements = answers.length;
            totalPages = Math.ceil(totalElements / pageSize);
          }
          // Handle answers array in data.reponsesDtos
          else if (
            response.data.reponsesDtos &&
            Array.isArray(response.data.reponsesDtos)
          ) {
            answers = response.data.reponsesDtos
              .map((item: any) => this.mapApiResponseToAnswer(item))
              .filter((answer: Answer | null): answer is Answer => answer !== null);
            totalElements = answers.length;
            totalPages = Math.ceil(totalElements / pageSize);
          }

          return {
            content: answers,
            totalElements,
            totalPages
          };
        }
        console.error(
          "Error: API response is not in expected format",
          response
        );
        return {
          content: [],
          totalElements: 0,
          totalPages: 0
        };
      })
      .catch((error) => {
        console.error("Error fetching answers:", error);
        return {
          content: [],
          totalElements: 0,
          totalPages: 0
        };
      });
  }

  /**
   * Get an answer by ID
   * @param id The answer ID
   * @returns Promise with the answer
   */
  public getAnswerById(id: number): Promise<Answer | null> {
    return this.apiService.get<any>(`${this.apiUrl}/${id}`)
      .then(response => {
        if (response && response.data) {
          // Handle direct object in data
          if (!Array.isArray(response.data) && typeof response.data === "object") {
            // Check if it's an answer object directly
            if (response.data.id !== undefined) {
              return this.mapApiResponseToAnswer(response.data);
            }
            // Check if it's in reponseDto
            else if (response.data.reponseDto) {
              return this.mapApiResponseToAnswer(response.data.reponseDto);
            }
            // Check if it's in reponsesDtos array
            else if (
              response.data.reponsesDtos &&
              Array.isArray(response.data.reponsesDtos) &&
              response.data.reponsesDtos.length > 0
            ) {
              return this.mapApiResponseToAnswer(response.data.reponsesDtos[0]);
            }
          }
          // Handle array in data (take the first item)
          else if (Array.isArray(response.data) && response.data.length > 0) {
            return this.mapApiResponseToAnswer(response.data[0]);
          }
        }
        console.error(`Error fetching answer ${id}: Invalid response format`, response);
        return null;
      })
      .catch(error => {
        console.error(`Error fetching answer ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new answer
   * @param answer The answer data
   * @returns Promise with the created answer
   */
  public createAnswer(answer: Omit<Answer, 'id'>): Promise<Answer | null> {
    // Map Answer model to API format
    const apiData = {
      label: answer.label,
      contenu: answer.text, // Map text to contenu
      is_correct: answer.valid_answer, // Map valid_answer to is_correct
      question_id: answer.question_id
    };

    return this.apiService.post<any>(this.apiUrl, apiData)
      .then(response => {
        if (response && response.data) {
          // Handle direct object in data
          if (!Array.isArray(response.data) && typeof response.data === "object") {
            // Check if it's an answer object directly
            if (response.data.id !== undefined) {
              return this.mapApiResponseToAnswer(response.data);
            }
            // Check if it's in reponseDto
            else if (response.data.reponseDto) {
              return this.mapApiResponseToAnswer(response.data.reponseDto);
            }
            // Check if it's in reponsesDtos array
            else if (
              response.data.reponsesDtos &&
              Array.isArray(response.data.reponsesDtos) &&
              response.data.reponsesDtos.length > 0
            ) {
              return this.mapApiResponseToAnswer(response.data.reponsesDtos[0]);
            }
          }
          // Handle array in data (take the first item)
          else if (Array.isArray(response.data) && response.data.length > 0) {
            return this.mapApiResponseToAnswer(response.data[0]);
          }
        }
        console.error('Error creating answer: Invalid response format', response);
        return null;
      })
      .catch(error => {
        console.error('Error creating answer:', error);
        return null;
      });
  }

  /**
   * Update an answer
   * @param id The answer ID
   * @param answer The answer data
   * @returns Promise with the updated answer
   */
  public updateAnswer(id: number, answer: Partial<Answer>): Promise<Answer | null> {
    // Map Answer model to API format
    const apiData: any = {};

    if (answer.label !== undefined) {
      apiData.label = answer.label;
    }

    if (answer.text !== undefined) {
      apiData.contenu = answer.text; // Map text to contenu
    }

    if (answer.valid_answer !== undefined) {
      apiData.is_correct = answer.valid_answer; // Map valid_answer to is_correct
    }

    if (answer.question_id !== undefined) {
      apiData.question_id = answer.question_id;
    }

    return this.apiService.put<any>(`${this.apiUrl}/${id}`, apiData)
      .then(response => {
        if (response && response.data) {
          // Handle direct object in data
          if (!Array.isArray(response.data) && typeof response.data === "object") {
            // Check if it's an answer object directly
            if (response.data.id !== undefined) {
              return this.mapApiResponseToAnswer(response.data);
            }
            // Check if it's in reponseDto
            else if (response.data.reponseDto) {
              return this.mapApiResponseToAnswer(response.data.reponseDto);
            }
            // Check if it's in reponsesDtos array
            else if (
              response.data.reponsesDtos &&
              Array.isArray(response.data.reponsesDtos) &&
              response.data.reponsesDtos.length > 0
            ) {
              return this.mapApiResponseToAnswer(response.data.reponsesDtos[0]);
            }
          }
          // Handle array in data (take the first item)
          else if (Array.isArray(response.data) && response.data.length > 0) {
            return this.mapApiResponseToAnswer(response.data[0]);
          }
        }
        console.error(`Error updating answer ${id}: Invalid response format`, response);
        return null;
      })
      .catch(error => {
        console.error(`Error updating answer ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete an answer
   * @param id The answer ID
   * @returns Promise with success status and error message if applicable
   */
  public deleteAnswer(id: number): Promise<{ success: boolean; errorMessage?: string }> {
    return this.apiService.delete(`${this.apiUrl}/${id}`)
      .then(() => ({ success: true }))
      .catch((error) => {
        console.error(`Error deleting answer ${id}:`, error);

        // Check for foreign key constraint violation
        if (error && error.response && error.response.data &&
            (error.response.data.message?.includes('foreign key constraint') ||
             error.response.data.error?.includes('foreign key constraint') ||
             error.response.data.includes('foreign key constraint'))) {
          return {
            success: false,
            errorMessage: "Cannot delete this answer because it is used in a question. Please remove the answer from the question first."
          };
        }

        return {
          success: false,
          errorMessage: "Failed to delete answer. Please try again."
        };
      });
  }

  /**
   * Get answers by question ID
   * @param questionId The question ID
   * @returns Promise with the answers
   */
  public getAnswersByQuestionId(questionId: number): Promise<Answer[]> {
    return this.apiService.get<any>(`${this.apiUrl}/question/${questionId}`)
      .then(response => {
        if (response && response.data) {
          let answers: Answer[] = [];

          // Handle direct array in data
          if (Array.isArray(response.data)) {
            answers = response.data
              .map((item: any) => this.mapApiResponseToAnswer(item))
              .filter((answer: Answer | null): answer is Answer => answer !== null);
          }
          // Handle answers array in data.reponses
          else if (
            response.data.reponses &&
            Array.isArray(response.data.reponses)
          ) {
            answers = response.data.reponses
              .map((item: any) => this.mapApiResponseToAnswer(item))
              .filter((answer: Answer | null): answer is Answer => answer !== null);
          }
          // Handle answers array in data.reponsesDtos
          else if (
            response.data.reponsesDtos &&
            Array.isArray(response.data.reponsesDtos)
          ) {
            answers = response.data.reponsesDtos
              .map((item: any) => this.mapApiResponseToAnswer(item))
              .filter((answer: Answer | null): answer is Answer => answer !== null);
          }
          // Handle answers array in data.content (paginated response)
          else if (
            response.data.content &&
            Array.isArray(response.data.content)
          ) {
            answers = response.data.content
              .map((item: any) => this.mapApiResponseToAnswer(item))
              .filter((answer: Answer | null): answer is Answer => answer !== null);
          }

          return answers;
        }
        console.error(`Error fetching answers for question ${questionId}: Invalid response format`, response);
        return [];
      })
      .catch(error => {
        console.error(`Error fetching answers for question ${questionId}:`, error);
        return [];
      });
  }
}
