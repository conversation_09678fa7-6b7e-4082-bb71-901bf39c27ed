import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Answer } from '../../models/admin/Answer';

@Injectable({
  providedIn: 'root'
})
export class AnswerService {
  private readonly apiUrl = '/api/reponses';

  constructor(private apiService: ApiService) {}

  /**
   * Maps API response to Answer model
   * @param item The API response item
   * @returns Mapped Answer object or null if item is falsy
   */
  private mapApiResponseToAnswer(item: any): Answer | null {
    if (!item) return null;

    return {
      id: item.id,
      label: item.label,
      text: item.contenu || item.text, // Map contenu to text
      valid_answer: item.is_correct !== undefined ? item.is_correct : item.valid_answer, // Map is_correct to valid_answer
      question_id: item.question_id,
      question: item.question
    };
  }

  /**
   * Get all answers
   * @returns Promise with the answers
   */
  public getAnswers(): Promise<Answer[]> {
    return this.apiService.get<any[]>(this.apiUrl)
      .then(response => {
        // Map API response to Answer model and filter out null values
        return response.data
          .map(item => this.mapApiResponseToAnswer(item))
          .filter((answer): answer is Answer => answer !== null);
      })
      .catch(error => {
        console.error('Error fetching answers:', error);
        return [];
      });
  }

  /**
   * Get an answer by ID
   * @param id The answer ID
   * @returns Promise with the answer
   */
  public getAnswerById(id: number): Promise<Answer | null> {
    return this.apiService.get<any>(`${this.apiUrl}/${id}`)
      .then(response => {
        return this.mapApiResponseToAnswer(response.data);
      })
      .catch(error => {
        console.error(`Error fetching answer ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new answer
   * @param answer The answer data
   * @returns Promise with the created answer
   */
  public createAnswer(answer: Omit<Answer, 'id'>): Promise<Answer | null> {
    // Map Answer model to API format
    const apiData = {
      label: answer.label,
      contenu: answer.text, // Map text to contenu
      is_correct: answer.valid_answer, // Map valid_answer to is_correct
      question_id: answer.question_id
    };

    return this.apiService.post<any>(this.apiUrl, apiData)
      .then(response => {
        return this.mapApiResponseToAnswer(response.data);
      })
      .catch(error => {
        console.error('Error creating answer:', error);
        return null;
      });
  }

  /**
   * Update an answer
   * @param id The answer ID
   * @param answer The answer data
   * @returns Promise with the updated answer
   */
  public updateAnswer(id: number, answer: Partial<Answer>): Promise<Answer | null> {
    // Map Answer model to API format
    const apiData: any = {};

    if (answer.label !== undefined) {
      apiData.label = answer.label;
    }

    if (answer.text !== undefined) {
      apiData.contenu = answer.text; // Map text to contenu
    }

    if (answer.valid_answer !== undefined) {
      apiData.is_correct = answer.valid_answer; // Map valid_answer to is_correct
    }

    if (answer.question_id !== undefined) {
      apiData.question_id = answer.question_id;
    }

    return this.apiService.put<any>(`${this.apiUrl}/${id}`, apiData)
      .then(response => {
        return this.mapApiResponseToAnswer(response.data);
      })
      .catch(error => {
        console.error(`Error updating answer ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete an answer
   * @param id The answer ID
   * @returns Promise with success status
   */
  public deleteAnswer(id: number): Promise<boolean> {
    return this.apiService.delete(`${this.apiUrl}/${id}`)
      .then(() => true)
      .catch(error => {
        console.error(`Error deleting answer ${id}:`, error);
        return false;
      });
  }

  /**
   * Get answers by question ID
   * @param questionId The question ID
   * @returns Promise with the answers
   */
  public getAnswersByQuestionId(questionId: number): Promise<Answer[]> {
    return this.apiService.get<any[]>(`${this.apiUrl}/question/${questionId}`)
      .then(response => {
        // Map API response to Answer model and filter out null values
        return response.data
          .map(item => this.mapApiResponseToAnswer(item))
          .filter((answer): answer is Answer => answer !== null);
      })
      .catch(error => {
        console.error(`Error fetching answers for question ${questionId}:`, error);
        return [];
      });
  }
}
