import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Intern } from '../../models/admin/Intern';

@Injectable({
  providedIn: 'root'
})
export class InternService {
  private readonly apiUrl = '/api/stagiaires';

  constructor(private apiService: ApiService) {}

  /**
   * Get all interns
   * @returns Promise with the interns
   */
  public getInterns(): Promise<Intern[]> {
    return this.apiService.get<Intern[]>(this.apiUrl)
      .then(response => response.data)
      .catch(error => {
        console.error('Error fetching interns:', error);
        return [];
      });
  }

  /**
   * Get an intern by ID
   * @param id The intern ID
   * @returns Promise with the intern
   */
  public getInternById(id: number): Promise<Intern | null> {
    return this.apiService.get<Intern>(`${this.apiUrl}/${id}`)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error fetching intern ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new intern
   * @param intern The intern data
   * @returns Promise with the created intern
   */
  public createIntern(intern: Omit<Intern, 'id'>): Promise<Intern | null> {
    return this.apiService.post<Intern>(this.apiUrl, intern)
      .then(response => response.data)
      .catch(error => {
        console.error('Error creating intern:', error);
        return null;
      });
  }

  /**
   * Update an intern
   * @param id The intern ID
   * @param intern The intern data
   * @returns Promise with the updated intern
   */
  public updateIntern(id: number, intern: Partial<Intern>): Promise<Intern | null> {
    return this.apiService.put<Intern>(`${this.apiUrl}/${id}`, intern)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error updating intern ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete an intern
   * @param id The intern ID
   * @returns Promise with success status
   */
  public deleteIntern(id: number): Promise<boolean> {
    return this.apiService.delete(`${this.apiUrl}/${id}`)
      .then(() => true)
      .catch(error => {
        console.error(`Error deleting intern ${id}:`, error);
        return false;
      });
  }

  /**
   * Get interns by promotion ID
   * @param promotionId The promotion ID
   * @returns Promise with the interns
   */
  public getInternsByPromotionId(promotionId: number): Promise<Intern[]> {
    return this.apiService.get<Intern[]>(`${this.apiUrl}/promotion/${promotionId}`)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error fetching interns for promotion ${promotionId}:`, error);
        return [];
      });
  }
}
