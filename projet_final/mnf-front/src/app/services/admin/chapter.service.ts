import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Chapter } from '../../models/admin/Chapter';

@Injectable({
  providedIn: 'root'
})
export class ChapterService {
  private readonly apiUrl = '/api/chapters';

  constructor(private apiService: ApiService) {}

  /**
   * Maps API response to Chapter model
   * @param apiChapter The chapter data from API
   * @returns Chapter model
   */
  private mapApiResponseToChapter(apiChapter: any): Chapter {
    // Create a new chapter object with the API data
    const chapter: Chapter = {
      id: apiChapter.id,
      name: apiChapter.name || apiChapter.titre || '',
      title: apiChapter.titre || apiChapter.title || '',
      path: apiChapter.path || '',
      content: apiChapter.content || '',
      // Handle parent data which can be null, empty array, or populated array
      parent_path: apiChapter.parent_path || '',
      parentPath: apiChapter.parentPath || '',
      parentName: apiChapter.parentName || '',
      parents: apiChapter.parents
    };

    return chapter;
  }

  /**
   * Get the total count of chapters
   * @returns Promise with the count
   */
  public getChaptersTotal(): Promise<number> {
    return this.apiService
      .get<number>(`${this.apiUrl}/total`)
      .then((response) => {
        if (response && response.data !== undefined) {
          return response.data;
        }
        return 0;
      })
      .catch((error) => {
        console.error("Error fetching chapters count:", error);
        return 0;
      });
  }

  /**
   * Get all chapters
   * @returns Promise with the chapters
   */
  public getChapters(): Promise<Chapter[]> {
    return this.apiService.get<any[]>(this.apiUrl)
      .then(response => {
        if (Array.isArray(response.data)) {
          return response.data.map((chapter: any) => this.mapApiResponseToChapter(chapter));
        }
        console.error('Unexpected API response format:', response);
        return [];
      })
      .catch(error => {
        console.error('Error fetching chapters:', error);
        return [];
      });
  }


  /**
   * Get all admin chapters
   * @returns Promise with the chapters
   */
  public getChaptersAdmin(): Promise<Chapter[]> {
    return this.apiService.get<any>(`${this.apiUrl}/admin`)
      .then(response => {
        // Handle nested chapitreDto array inside data object
        if (response.data && response.data.chapitreDto && Array.isArray(response.data.chapitreDto)) {
          return response.data.chapitreDto.map((chapter: any) => this.mapApiResponseToChapter(chapter));
        } else if (Array.isArray(response.data)) {
          // Fallback to original behavior if data is directly an array
          return response.data.map((chapter: any) => this.mapApiResponseToChapter(chapter));
        }
        console.error('Unexpected API response format:', response);
        return [];
      })
      .catch(error => {
        console.error('Error fetching chapters:', error);
        return [];
      });
  }

  /**
   * Get all chapters with pagination and search
   * @param search Optional search term for filtering chapters
   * @param currentPage Current page number (0-based)
   * @param pageSize Number of items per page
   * @returns Promise with the chapters
   */
  public getChaptersPaginated(
    search?: string,
    currentPage: number = 0,
    pageSize: number = 10
  ): Promise<{ content: Chapter[], totalElements: number, totalPages: number }> {
    // Build query parameters
    const params: Record<string, string> = {
      currentPage: currentPage.toString(),
      pageSize: pageSize.toString()
    };

    // Add search parameter if provided
    if (search) {
      params['search'] = search;
    }

    console.log(`ChapterService.getChaptersPaginated: Fetching page ${currentPage}, size ${pageSize}, search: "${search || ''}", API URL: ${this.apiUrl}/admin, params:`, params);

    // First get the total count of chapters
    return this.getChaptersTotal()
      .then(totalCount => {
        console.log(`ChapterService.getChaptersPaginated: Total chapters count: ${totalCount}`);

        // Then get the paginated data
        return this.apiService
          .get<any>(`${this.apiUrl}/admin`, { params })
          .then((response) => {
            console.log(`ChapterService.getChaptersPaginated: Received raw response:`, response);
            console.log(`ChapterService.getChaptersPaginated: Received response data:`, response.data);

            let chapters: Chapter[] = [];
            let totalElements = totalCount; // Use the total count from getChaptersTotal
            let totalPages = Math.ceil(totalElements / pageSize);

            // Handle nested chapitreDto array inside data object
            if (response.data && response.data.chapitreDto && Array.isArray(response.data.chapitreDto)) {
              console.log(`ChapterService.getChaptersPaginated: Found chapitreDto array with ${response.data.chapitreDto.length} items`);

              // For chapitreDto format, we need to manually paginate since the API doesn't do it
              const allChapters = response.data.chapitreDto.map((chapter: any) => this.mapApiResponseToChapter(chapter));

              // Apply search filtering if needed
              let filteredChapters = allChapters;
              if (search) {
                const searchLower = search.toLowerCase();
                filteredChapters = allChapters.filter((chapter: Chapter) =>
                  chapter.name.toLowerCase().includes(searchLower) ||
                  chapter.title.toLowerCase().includes(searchLower)
                );
                console.log(`ChapterService.getChaptersPaginated: Filtered to ${filteredChapters.length} chapters matching search`);
              }

              // Log filtered chapters before pagination
              console.log(`ChapterService.getChaptersPaginated: Filtered chapters before pagination (chapitreDto format):`, filteredChapters);

              // Apply pagination manually
              const startIndex = currentPage * pageSize;
              const endIndex = Math.min(startIndex + pageSize, filteredChapters.length);

              // Check if we're trying to access a page that doesn't exist
              if (startIndex >= filteredChapters.length && filteredChapters.length > 0) {
                console.log(`ChapterService.getChaptersPaginated: Warning - startIndex (${startIndex}) is beyond the end of filteredChapters (length ${filteredChapters.length}). Returning first page instead.`);
                // Return the first page instead
                chapters = filteredChapters.slice(0, pageSize);
              } else {
                chapters = filteredChapters.slice(startIndex, endIndex);
              }

              // Log paginated chapters
              console.log(`ChapterService.getChaptersPaginated: Paginated chapters (${chapters.length}):`, chapters);

              // Update pagination info based on filtered results
              // Keep the original total count (totalCount) for display purposes
              // Calculate totalPages based on the total count, not the filtered count
              totalPages = Math.ceil(totalCount / pageSize);

              console.log(`ChapterService.getChaptersPaginated: Manually paginated to ${chapters.length} chapters from index ${startIndex} to ${endIndex}`);
            }
            // Handle direct array in data
            else if (Array.isArray(response.data)) {
              console.log(`ChapterService.getChaptersPaginated: Found direct array with ${response.data.length} items`);

              // For direct array format, we need to manually paginate since the API doesn't do it
              const allChapters = response.data.map((chapter: any) => this.mapApiResponseToChapter(chapter));

              // Apply search filtering if needed
              let filteredChapters = allChapters;
              if (search) {
                const searchLower = search.toLowerCase();
                filteredChapters = allChapters.filter((chapter: Chapter) =>
                  chapter.name.toLowerCase().includes(searchLower) ||
                  chapter.title.toLowerCase().includes(searchLower)
                );
                console.log(`ChapterService.getChaptersPaginated: Filtered to ${filteredChapters.length} chapters matching search`);
              }

              // Log filtered chapters before pagination
              console.log(`ChapterService.getChaptersPaginated: Filtered chapters before pagination (direct array format):`, filteredChapters);

              // Apply pagination manually
              const startIndex = currentPage * pageSize;
              const endIndex = Math.min(startIndex + pageSize, filteredChapters.length);

              // Check if we're trying to access a page that doesn't exist
              if (startIndex >= filteredChapters.length && filteredChapters.length > 0) {
                console.log(`ChapterService.getChaptersPaginated: Warning - startIndex (${startIndex}) is beyond the end of filteredChapters (length ${filteredChapters.length}). Returning first page instead.`);
                // Return the first page instead
                chapters = filteredChapters.slice(0, pageSize);
              } else {
                chapters = filteredChapters.slice(startIndex, endIndex);
              }

              // Log paginated chapters
              console.log(`ChapterService.getChaptersPaginated: Paginated chapters (${chapters.length}):`, chapters);

              // Update pagination info based on filtered results
              // Keep the original total count (totalCount) for display purposes
              // Calculate totalPages based on the total count, not the filtered count
              totalPages = Math.ceil(totalCount / pageSize);

              console.log(`ChapterService.getChaptersPaginated: Manually paginated to ${chapters.length} chapters from index ${startIndex} to ${endIndex}`);
            }
            // Handle paginated response format
            else if (response.data && response.data.content && Array.isArray(response.data.content)) {
              console.log(`ChapterService.getChaptersPaginated: Found paginated response with ${response.data.content.length} items`);

              // For paginated response, the API has already done the pagination
              chapters = response.data.content.map((chapter: any) => this.mapApiResponseToChapter(chapter));

              // Log chapters from paginated response
              console.log(`ChapterService.getChaptersPaginated: Chapters from paginated response (${chapters.length}):`, chapters);

              // Use pagination info from response if available
              if (response.data.totalElements !== undefined) {
                totalElements = response.data.totalElements;
                console.log(`ChapterService.getChaptersPaginated: Using totalElements from response: ${totalElements}`);
              }

              if (response.data.totalPages !== undefined) {
                totalPages = response.data.totalPages;
                console.log(`ChapterService.getChaptersPaginated: Using totalPages from response: ${totalPages}`);
              }
            }

            console.log(`ChapterService.getChaptersPaginated: Returning ${chapters.length} chapters, totalElements: ${totalElements}, totalPages: ${totalPages}`);

            const result = {
              content: chapters,
              totalElements: totalElements,
              totalPages: totalPages
            };

            console.log(`ChapterService.getChaptersPaginated: Final result:`, result);

            return result;
          });
      })
      .catch((error) => {
        console.error("Error fetching chapters:", error);
        return {
          content: [],
          totalElements: 0,
          totalPages: 0
        };
      });
  }

  /**
   * Get a chapter by ID
   * @param id The chapter ID
   * @returns Promise with the chapter
   */
  public getChapterById(id: number): Promise<Chapter | null> {
    return this.apiService.get<any>(`${this.apiUrl}/${id}`)
      .then(response => {
        if (response.data) {
          return this.mapApiResponseToChapter(response.data);
        }
        return null;
      })
      .catch(error => {
        console.error(`Error fetching chapter ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new chapter
   * @param chapter The chapter data
   * @returns Promise with the created chapter
   */
  public createChapter(chapter: Omit<Chapter, 'id'>): Promise<Chapter | null> {
    // Map frontend data to backend DTO format
    const chapterDto = {
      titre: chapter.title,
      parentName: chapter.parentName || null,
      parentId: null // For now, we only support parentName
    };

    return this.apiService.post<any>(this.apiUrl, chapterDto)
      .then(response => {
        if (response.data) {
          return this.mapApiResponseToChapter(response.data);
        }
        return null;
      })
      .catch(error => {
        console.error('Error creating chapter:', error);
        return null;
      });
  }

  /**
   * Update a chapter
   * @param id The chapter ID
   * @param chapter The chapter data
   * @returns Promise with the updated chapter
   */
  public updateChapter(id: number, chapter: Partial<Chapter>): Promise<Chapter | null> {
    // Map frontend data to backend DTO format
    const chapterDto = {
      id: id,
      titre: chapter.title,
      parentName: chapter.parentName || null,
      parentId: null // For now, we only support parentName
    };

    // Use PATCH instead of PUT as the backend expects PATCH
    return this.apiService.patch<any>(`${this.apiUrl}/${id}`, chapterDto)
      .then(response => {
        if (response.data) {
          return this.mapApiResponseToChapter(response.data);
        }
        return null;
      })
      .catch(error => {
        console.error(`Error updating chapter ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete a chapter
   * @param id The chapter ID
   * @returns Promise with success status
   */
  public deleteChapter(id: number): Promise<boolean> {
    return this.apiService.delete(`${this.apiUrl}/${id}`)
      .then(() => true)
      .catch(error => {
        console.error(`Error deleting chapter ${id}:`, error);
        return false;
      });
  }

  /**
   * Get chapters by parent path
   * @param parentPath The parent path
   * @returns Promise with the chapters
   */
  public getChaptersByParentPath(parentPath: string): Promise<Chapter[]> {
    return this.apiService.get<any[]>(`${this.apiUrl}/parent/${parentPath}`)
      .then(response => {
        if (Array.isArray(response.data)) {
          return response.data.map((chapter: any) => this.mapApiResponseToChapter(chapter));
        }
        console.error('Unexpected API response format:', response);
        return [];
      })
      .catch(error => {
        console.error(`Error fetching chapters for parent path ${parentPath}:`, error);
        return [];
      });
  }
}
