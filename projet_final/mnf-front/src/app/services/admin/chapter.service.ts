import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Chapter } from '../../models/admin/Chapter';
import { MOCK_CHAPTERS } from './mock-chapters';

@Injectable({
  providedIn: 'root'
})
export class ChapterService {
  private readonly apiUrl = '/api/admin/chapters';
  private chapters: Chapter[] = MOCK_CHAPTERS;

  constructor(private apiService: ApiService) {}

  /**
   * Get all chapters
   * @returns Promise with the chapters
   */
  public getChapters(): Promise<Chapter[]> {
    // Using mock data instead of API call
    return Promise.resolve([...this.chapters]);
  }

  /**
   * Get a chapter by ID
   * @param id The chapter ID
   * @returns Promise with the chapter
   */
  public getChapterById(id: number): Promise<Chapter | null> {
    // Using mock data instead of API call
    const chapter = this.chapters.find(c => c.id === id);
    return Promise.resolve(chapter || null);
  }

  /**
   * Create a new chapter
   * @param chapter The chapter data
   * @returns Promise with the created chapter
   */
  public createChapter(chapter: Omit<Chapter, 'id'>): Promise<Chapter | null> {
    // Using mock data instead of API call
    const newId = Math.max(...this.chapters.map(c => c.id || 0)) + 1;
    const newChapter: Chapter = {
      ...chapter,
      id: newId,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.chapters.push(newChapter);
    return Promise.resolve({...newChapter});
  }

  /**
   * Update a chapter
   * @param id The chapter ID
   * @param chapter The chapter data
   * @returns Promise with the updated chapter
   */
  public updateChapter(id: number, chapter: Partial<Chapter>): Promise<Chapter | null> {
    // Using mock data instead of API call
    const index = this.chapters.findIndex(c => c.id === id);
    if (index === -1) {
      return Promise.resolve(null);
    }

    const updatedChapter: Chapter = {
      ...this.chapters[index],
      ...chapter,
      updatedAt: new Date()
    };

    this.chapters[index] = updatedChapter;
    return Promise.resolve({...updatedChapter});
  }

  /**
   * Delete a chapter
   * @param id The chapter ID
   * @returns Promise with success status
   */
  public deleteChapter(id: number): Promise<boolean> {
    // Using mock data instead of API call
    const index = this.chapters.findIndex(c => c.id === id);
    if (index === -1) {
      return Promise.resolve(false);
    }

    this.chapters.splice(index, 1);
    return Promise.resolve(true);
  }

  /**
   * Get chapters by parent path
   * @param parentPath The parent path
   * @returns Promise with the chapters
   */
  public getChaptersByParentPath(parentPath: string): Promise<Chapter[]> {
    // Using mock data instead of API call
    const filteredChapters = this.chapters.filter(c => c.parent_path === parentPath);
    return Promise.resolve([...filteredChapters]);
  }
}
