import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Chapter } from '../../models/admin/Chapter';

@Injectable({
  providedIn: 'root'
})
export class ChapterService {
  private readonly apiUrl = '/api/admin/chapters';

  constructor(private apiService: ApiService) {}

  /**
   * Get all chapters
   * @returns Promise with the chapters
   */
  public getChapters(): Promise<Chapter[]> {
    return this.apiService.get<Chapter[]>(this.apiUrl)
      .then(response => response.data)
      .catch(error => {
        console.error('Error fetching chapters:', error);
        return [];
      });
  }

  /**
   * Get a chapter by ID
   * @param id The chapter ID
   * @returns Promise with the chapter
   */
  public getChapterById(id: number): Promise<Chapter | null> {
    return this.apiService.get<Chapter>(`${this.apiUrl}/${id}`)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error fetching chapter ${id}:`, error);
        return null;
      });
  }

  /**
   * Create a new chapter
   * @param chapter The chapter data
   * @returns Promise with the created chapter
   */
  public createChapter(chapter: Omit<Chapter, 'id'>): Promise<Chapter | null> {
    return this.apiService.post<Chapter>(this.apiUrl, chapter)
      .then(response => response.data)
      .catch(error => {
        console.error('Error creating chapter:', error);
        return null;
      });
  }

  /**
   * Update a chapter
   * @param id The chapter ID
   * @param chapter The chapter data
   * @returns Promise with the updated chapter
   */
  public updateChapter(id: number, chapter: Partial<Chapter>): Promise<Chapter | null> {
    return this.apiService.put<Chapter>(`${this.apiUrl}/${id}`, chapter)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error updating chapter ${id}:`, error);
        return null;
      });
  }

  /**
   * Delete a chapter
   * @param id The chapter ID
   * @returns Promise with success status
   */
  public deleteChapter(id: number): Promise<boolean> {
    return this.apiService.delete(`${this.apiUrl}/${id}`)
      .then(() => true)
      .catch(error => {
        console.error(`Error deleting chapter ${id}:`, error);
        return false;
      });
  }

  /**
   * Get chapters by parent path
   * @param parentPath The parent path
   * @returns Promise with the chapters
   */
  public getChaptersByParentPath(parentPath: string): Promise<Chapter[]> {
    return this.apiService.get<Chapter[]>(`${this.apiUrl}/parent/${encodeURIComponent(parentPath)}`)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error fetching chapters for parent path ${parentPath}:`, error);
        return [];
      });
  }
}
