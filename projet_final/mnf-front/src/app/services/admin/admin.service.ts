import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';

interface Admin {
  id: number;
  name: string;
  email: string;
}

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private mockAdmins: Admin[] = [
    { id: 1, name: 'Admin User', email: '<EMAIL>' },
    { id: 2, name: 'Super Admin', email: '<EMAIL>' }
  ];

  constructor(private apiService: ApiService) {}

  getAdmins(): Promise<Admin[]> {
    // In a real app, this would call your API
    // return this.apiService.get<Admin[]>('/api/admin/admins');

    // For now, return mock data
    return Promise.resolve(this.mockAdmins);
  }

  getAdmin(id: number): Promise<Admin | null> {
    // In a real app, this would call your API
    // return this.apiService.get<Admin>(`/api/admin/admins/${id}`);

    // For now, return mock data
    const admin = this.mockAdmins.find(a => a.id === id) || null;
    return Promise.resolve(admin);
  }

  createAdmin(admin: Omit<Admin, 'id'>): Promise<Admin> {
    // In a real app, this would call your API
    // return this.apiService.post<Admin>('/api/admin/admins', admin);

    // For now, simulate creating a new admin
    const newAdmin: Admin = {
      ...admin,
      id: this.mockAdmins.length + 1
    };
    this.mockAdmins.push(newAdmin);
    return Promise.resolve(newAdmin);
  }

  updateAdmin(id: number, admin: Partial<Admin>): Promise<Admin | null> {
    // In a real app, this would call your API
    // return this.apiService.put<Admin>(`/api/admin/admins/${id}`, admin);

    // For now, simulate updating an admin
    const index = this.mockAdmins.findIndex(a => a.id === id);
    if (index === -1) {
      return Promise.resolve(null);
    }

    this.mockAdmins[index] = {
      ...this.mockAdmins[index],
      ...admin
    };

    return Promise.resolve(this.mockAdmins[index]);
  }

  deleteAdmin(id: number): Promise<boolean> {
    // In a real app, this would call your API
    // return this.apiService.delete(`/api/admin/admins/${id}`);

    // For now, simulate deleting an admin
    const index = this.mockAdmins.findIndex(a => a.id === id);
    if (index === -1) {
      return Promise.resolve(false);
    }

    this.mockAdmins.splice(index, 1);
    return Promise.resolve(true);
  }
}
