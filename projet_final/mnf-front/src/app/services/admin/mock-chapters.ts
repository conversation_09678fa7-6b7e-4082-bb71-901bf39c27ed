import { Chapter } from '../../models/admin/Chapter';

// Function to generate 101 mock chapters
function generateMockChapters(): Chapter[] {
  const chapters: Chapter[] = [];

  // Topics for chapters
  const topics = [
    'Programming Basics',
    'Web Development',
    'Data Structures',
    'Algorithms',
    'Database Design',
    'Frontend Frameworks',
    'Backend Development',
    'Mobile Development',
    'DevOps',
    'Cloud Computing',
    'Machine Learning',
    'Artificial Intelligence',
    'Cybersecurity',
    'Network Programming',
    'Game Development',
    'UI/UX Design',
    'Software Architecture',
    'Testing Methodologies',
    'Version Control',
    'Agile Development'
  ];

  // Generate 101 chapters
  for (let i = 1; i <= 101; i++) {
    const topicIndex = (i - 1) % topics.length;
    const topic = topics[topicIndex];
    const chapterNumber = Math.floor((i - 1) / topics.length) + 1;

    chapters.push({
      id: i,
      name: `${topic.toLowerCase().replace(/\s+/g, '-')}-${chapterNumber}`,
      title: `${topic} - Chapter ${chapterNumber}`,
      path: `/programming/${topic.toLowerCase().replace(/\s+/g, '-')}/${chapterNumber}`,
      content: `This chapter covers ${topic.toLowerCase()} concepts, part ${chapterNumber}.`,
      parent_path: `/programming/${topic.toLowerCase().replace(/\s+/g, '-')}`,
      createdAt: new Date(2023, 0, i),
      updatedAt: new Date(2023, 1, i)
    });
  }

  return chapters;
}

// Generate 101 mock chapters
export const MOCK_CHAPTERS: Chapter[] = generateMockChapters();

// Original mock chapters for reference (commented out)
/*
export const MOCK_CHAPTERS: Chapter[] = [
  {
    id: 1,
    name: 'introduction',
    title: 'Introduction to Programming',
    path: '/programming/introduction',
    content: 'This chapter introduces basic programming concepts.',
    parent_path: '/programming',
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-02-10')
  },
  {
    id: 2,
    name: 'variables',
    title: 'Variables and Data Types',
    path: '/programming/variables',
    content: 'Learn about variables and different data types in programming.',
    parent_path: '/programming',
    createdAt: new Date('2023-01-20'),
    updatedAt: new Date('2023-02-15')
  },
  {
    id: 3,
    name: 'control-flow',
    title: 'Control Flow Statements',
    path: '/programming/control-flow',
    content: 'Understand how to control the flow of your program using conditional statements and loops.',
    parent_path: '/programming',
    createdAt: new Date('2023-01-25'),
    updatedAt: new Date('2023-02-20')
  },
  {
    id: 4,
    name: 'functions',
    title: 'Functions and Methods',
    path: '/programming/functions',
    content: 'Learn how to create and use functions to organize your code.',
    parent_path: '/programming',
    createdAt: new Date('2023-01-30'),
    updatedAt: new Date('2023-02-25')
  },
  {
    id: 5,
    name: 'objects',
    title: 'Objects and Classes',
    path: '/programming/objects',
    content: 'Introduction to object-oriented programming concepts.',
    parent_path: '/programming',
    createdAt: new Date('2023-02-05'),
    updatedAt: new Date('2023-03-01')
  },
  {
    id: 6,
    name: 'arrays',
    title: 'Arrays and Collections',
    path: '/programming/arrays',
    content: 'Working with arrays and other collection types.',
    parent_path: '/programming',
    createdAt: new Date('2023-02-10'),
    updatedAt: new Date('2023-03-05')
  },
  {
    id: 7,
    name: 'error-handling',
    title: 'Error Handling',
    path: '/programming/error-handling',
    content: 'Learn how to handle errors and exceptions in your code.',
    parent_path: '/programming',
    createdAt: new Date('2023-02-15'),
    updatedAt: new Date('2023-03-10')
  },
  {
    id: 8,
    name: 'modules',
    title: 'Modules and Packages',
    path: '/programming/modules',
    content: 'Organizing code into modules and packages for better maintainability.',
    parent_path: '/programming',
    createdAt: new Date('2023-02-20'),
    updatedAt: new Date('2023-03-15')
  },
  {
    id: 9,
    name: 'testing',
    title: 'Testing and Debugging',
    path: '/programming/testing',
    content: 'Techniques for testing and debugging your code.',
    parent_path: '/programming',
    createdAt: new Date('2023-02-25'),
    updatedAt: new Date('2023-03-20')
  },
  {
    id: 10,
    name: 'advanced',
    title: 'Advanced Topics',
    path: '/programming/advanced',
    content: 'Advanced programming concepts and techniques.',
    parent_path: '/programming',
    createdAt: new Date('2023-03-01'),
    updatedAt: new Date('2023-03-25')
  },
  {
    id: 11,
    name: 'web-basics',
    title: 'Web Development Basics',
    path: '/programming/web-basics',
    content: 'Introduction to web development fundamentals.',
    parent_path: '/programming',
    createdAt: new Date('2023-03-05'),
    updatedAt: new Date('2023-03-30')
  },
  {
    id: 12,
    name: 'html-css',
    title: 'HTML and CSS',
    path: '/programming/html-css',
    content: 'Learn the basics of HTML and CSS for web development.',
    parent_path: '/programming',
    createdAt: new Date('2023-03-10'),
    updatedAt: new Date('2023-04-05')
  },
  {
    id: 13,
    name: 'javascript',
    title: 'JavaScript Fundamentals',
    path: '/programming/javascript',
    content: 'Introduction to JavaScript programming language.',
    parent_path: '/programming',
    createdAt: new Date('2023-03-15'),
    updatedAt: new Date('2023-04-10')
  },
  {
    id: 14,
    name: 'dom-manipulation',
    title: 'DOM Manipulation',
    path: '/programming/dom-manipulation',
    content: 'Learn how to manipulate the Document Object Model with JavaScript.',
    parent_path: '/programming',
    createdAt: new Date('2023-03-20'),
    updatedAt: new Date('2023-04-15')
  },
  {
    id: 15,
    name: 'api-integration',
    title: 'API Integration',
    path: '/programming/api-integration',
    content: 'Working with APIs and handling asynchronous operations.',
    parent_path: '/programming',
    createdAt: new Date('2023-03-25'),
    updatedAt: new Date('2023-04-20')
  },
  {
    id: 16,
    name: 'frameworks',
    title: 'Frontend Frameworks',
    path: '/programming/frameworks',
    content: 'Introduction to popular frontend frameworks like React, Angular, and Vue.',
    parent_path: '/programming',
    createdAt: new Date('2023-03-30'),
    updatedAt: new Date('2023-04-25')
  },
  {
    id: 17,
    name: 'backend-basics',
    title: 'Backend Development Basics',
    path: '/programming/backend-basics',
    content: 'Introduction to server-side programming and backend development.',
    parent_path: '/programming',
    createdAt: new Date('2023-04-05'),
    updatedAt: new Date('2023-04-30')
  },
  {
    id: 18,
    name: 'databases',
    title: 'Databases and SQL',
    path: '/programming/databases',
    content: 'Working with databases and learning SQL fundamentals.',
    parent_path: '/programming',
    createdAt: new Date('2023-04-10'),
    updatedAt: new Date('2023-05-05')
  },
  {
    id: 19,
    name: 'authentication',
    title: 'Authentication and Authorization',
    path: '/programming/authentication',
    content: 'Implementing user authentication and authorization in web applications.',
    parent_path: '/programming',
    createdAt: new Date('2023-04-15'),
    updatedAt: new Date('2023-05-10')
  },
  {
    id: 20,
    name: 'security',
    title: 'Web Security',
    path: '/programming/security',
    content: 'Best practices for securing web applications.',
    parent_path: '/programming',
    createdAt: new Date('2023-04-20'),
    updatedAt: new Date('2023-05-15')
  },
  {
    id: 21,
    name: 'deployment',
    title: 'Deployment and DevOps',
    path: '/programming/deployment',
    content: 'Learn how to deploy applications and basic DevOps concepts.',
    parent_path: '/programming',
    createdAt: new Date('2023-04-25'),
    updatedAt: new Date('2023-05-20')
  },
  {
    id: 22,
    name: 'mobile-dev',
    title: 'Mobile Development',
    path: '/programming/mobile-dev',
    content: 'Introduction to mobile app development.',
    parent_path: '/programming',
    createdAt: new Date('2023-04-30'),
    updatedAt: new Date('2023-05-25')
  },
  {
    id: 23,
    name: 'performance',
    title: 'Performance Optimization',
    path: '/programming/performance',
    content: 'Techniques for optimizing application performance.',
    parent_path: '/programming',
    createdAt: new Date('2023-05-05'),
    updatedAt: new Date('2023-05-30')
  },
  {
    id: 24,
    name: 'accessibility',
    title: 'Web Accessibility',
    path: '/programming/accessibility',
    content: 'Making web applications accessible to all users.',
    parent_path: '/programming',
    createdAt: new Date('2023-05-10'),
    updatedAt: new Date('2023-06-05')
  },
  {
    id: 25,
    name: 'seo',
    title: 'Search Engine Optimization',
    path: '/programming/seo',
    content: 'Basics of SEO for web developers.',
    parent_path: '/programming',
    createdAt: new Date('2023-05-15'),
    updatedAt: new Date('2023-06-10')
  },
  {
    id: 26,
    name: 'career-development',
    title: 'Career Development',
    path: '/programming/career-development',
    content: 'Tips and resources for advancing your programming career.',
    parent_path: '/programming',
    createdAt: new Date('2023-05-20'),
    updatedAt: new Date('2023-06-15')
  }
];
*/
