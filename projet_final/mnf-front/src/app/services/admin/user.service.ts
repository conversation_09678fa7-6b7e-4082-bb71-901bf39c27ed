import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private mockUsers: User[] = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'User' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Editor' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Viewer' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'User' },
    { id: 5, name: '<PERSON>', email: '<EMAIL>', role: 'Editor' },
    { id: 6, name: '<PERSON>', email: '<EMAIL>', role: 'Viewer' },
    { id: 7, name: '<PERSON>', email: '<EMAIL>', role: 'User' },
    { id: 8, name: '<PERSON>', email: '<PERSON><PERSON>@example.com', role: 'Editor' },
    { id: 9, name: '<PERSON>', email: '<EMAIL>', role: 'Viewer' },
    { id: 10, name: '<PERSON>', email: '<EMAIL>', role: 'User' },
    { id: 11, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'Editor' },
    { id: 12, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'Viewer' },
    { id: 13, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'User' },
    { id: 14, name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'Editor' },
    { id: 15, name: 'Michael Young', email: '<EMAIL>', role: 'Viewer' },
    { id: 16, name: 'Natalie Adams', email: '<EMAIL>', role: 'User' },
    { id: 17, name: 'Oliver Scott', email: '<EMAIL>', role: 'Editor' },
    { id: 18, name: 'Patricia King', email: '<EMAIL>', role: 'Viewer' },
    { id: 19, name: 'Quentin Green', email: '<EMAIL>', role: 'User' },
    { id: 20, name: 'Rachel Baker', email: '<EMAIL>', role: 'Editor' },
    { id: 21, name: 'Samuel Cooper', email: '<EMAIL>', role: 'Viewer' }
  ];

  constructor(private apiService: ApiService) {}

  getUsers(): Promise<User[]> {
    // In a real app, this would call your API
    // return this.apiService.get<User[]>('/api/admin/users');

    // For now, return mock data
    return Promise.resolve(this.mockUsers);
  }

  getUser(id: number): Promise<User | null> {
    // In a real app, this would call your API
    // return this.apiService.get<User>(`/api/admin/users/${id}`);

    // For now, return mock data
    const user = this.mockUsers.find(u => u.id === id) || null;
    return Promise.resolve(user);
  }

  createUser(user: Omit<User, 'id'>): Promise<User> {
    // In a real app, this would call your API
    // return this.apiService.post<User>('/api/admin/users', user);

    // For now, simulate creating a new user
    const newUser: User = {
      ...user,
      id: this.mockUsers.length + 1
    };
    this.mockUsers.push(newUser);
    return Promise.resolve(newUser);
  }

  updateUser(id: number, user: Partial<User>): Promise<User | null> {
    // In a real app, this would call your API
    // return this.apiService.put<User>(`/api/admin/users/${id}`, user);

    // For now, simulate updating a user
    const index = this.mockUsers.findIndex(u => u.id === id);
    if (index === -1) {
      return Promise.resolve(null);
    }

    this.mockUsers[index] = {
      ...this.mockUsers[index],
      ...user
    };

    return Promise.resolve(this.mockUsers[index]);
  }

  deleteUser(id: number): Promise<boolean> {
    // In a real app, this would call your API
    // return this.apiService.delete(`/api/admin/users/${id}`);

    // For now, simulate deleting a user
    const index = this.mockUsers.findIndex(u => u.id === id);
    if (index === -1) {
      return Promise.resolve(false);
    }

    this.mockUsers.splice(index, 1);
    return Promise.resolve(true);
  }
}
