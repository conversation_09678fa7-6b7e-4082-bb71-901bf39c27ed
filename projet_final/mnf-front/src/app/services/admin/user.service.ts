import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';

interface User {
  id?: number;
  name?: string;
  firstname?: string;
  lastname?: string;
  email: string;
  role: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {

  constructor(private apiService: ApiService) {}

  getUsers(): Promise<User[]> {
    return this.apiService.get<any>('/api/user')
      .then(response => {
        // Check if the response has a content property (new API format)
        if (response.data && response.data.content && Array.isArray(response.data.content)) {
          return response.data.content;
        }
        // Fallback to the old format
        return Array.isArray(response.data) ? response.data : [];
      })
      .catch(error => {
        console.error('Error fetching users:', error);
        return [];
      });
  }

  getUser(id: number): Promise<User | null> {
    return this.apiService.get<User>(`/api/user/${id}`)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error fetching user with id ${id}:`, error);
        return null;
      });
  }

  createUser(user: Omit<User, 'id'>): Promise<User> {
    return this.apiService.post<User>('/api/user', user)
      .then(response => response.data)
      .catch(error => {
        console.error('Error creating user:', error);
        throw error;
      });
  }

  updateUser(id: number, user: Partial<User>): Promise<User | null> {
    return this.apiService.put<User>(`/api/user/${id}`, user)
      .then(response => response.data)
      .catch(error => {
        console.error(`Error updating user with id ${id}:`, error);
        return null;
      });
  }

  deleteUser(id: number): Promise<boolean> {
    return this.apiService.delete(`/api/user/${id}`)
      .then(() => true)
      .catch(error => {
        console.error(`Error deleting user with id ${id}:`, error);
        return false;
      });
  }
}
