import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay } from 'rxjs/operators';
import { LanguageService } from './language.service';

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private translations: { [key: string]: any } = {};
  private translationCache: { [lang: string]: Observable<any> } = {};
  private previousLang: string | null = null;

  constructor(
    private http: HttpClient,
    private languageService: LanguageService
  ) {}

  // Load translations for a specific language
  loadTranslations(lang: string): Observable<any> {
    if (this.translationCache[lang]) {
      return this.translationCache[lang];
    }

    // Store current language as previous before loading new one
    const currentLang = this.languageService.getCurrentLanguage().code;
    if (currentLang !== lang && this.translations[currentLang]) {
      this.previousLang = currentLang;
    }

    this.translationCache[lang] = this.http.get(`/assets/i18n/${lang}.json`).pipe(
      map(response => {
        this.translations[lang] = response;
        return response;
      }),
      catchError(error => {
        console.error(`Error loading translations for ${lang}:`, error);
        return of({});
      }),
      shareReplay(1)
    );

    return this.translationCache[lang];
  }

  // Get a translation by key
  translate(key: string): string {
    const currentLang = this.languageService.getCurrentLanguage().code;

    // If current language translations aren't loaded yet, try using previous language
    if (!this.translations[currentLang] && this.previousLang && this.translations[this.previousLang]) {
      // Use previous language translations during loading
      return this.getTranslationValue(key, this.previousLang);
    } else if (!this.translations[currentLang]) {
      return ''; // Return empty string instead of key during loading
    }

    return this.getTranslationValue(key, currentLang);
  }

  // Helper method to get translation value from a specific language
  private getTranslationValue(key: string, lang: string): string {
    // Split the key by dots to navigate the nested JSON structure
    const keys = key.split('.');
    let value = this.translations[lang];

    // Navigate through the nested structure
    for (const k of keys) {
      if (value && value[k] !== undefined) {
        value = value[k];
      } else {
        return lang === this.previousLang ? '' : key; // Return empty string if using previous language
      }
    }

    return typeof value === 'string' ? value : key;
  }

  // Initialize translations for the current language
  initTranslations(): Observable<any> {
    const currentLang = this.languageService.getCurrentLanguage().code;

    // If we're switching languages and have previous translations, keep them available
    if (this.previousLang && this.translations[this.previousLang]) {
      console.log(`Keeping ${this.previousLang} translations available while loading ${currentLang}`);
    }

    return this.loadTranslations(currentLang);
  }
}
