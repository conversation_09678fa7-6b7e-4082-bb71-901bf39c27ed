import { Injectable } from '@angular/core';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { EnvironmentService } from './environment.service';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private axiosInstance: AxiosInstance;

  constructor(private environmentService: EnvironmentService) {
    // Create Axios instance with default configuration
    this.axiosInstance = axios.create({
      baseURL: this.environmentService.getApiBaseUrl(),
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Update baseURL when environment changes
    this.environmentService.currentEnvironment$.subscribe(env => {
      this.axiosInstance.defaults.baseURL = env.apiBaseUrl;
      console.log(`API baseURL updated to: ${env.apiBaseUrl}`);
    });

    // Add request interceptor for authentication, logging, etc.
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Add authentication token to headers if available
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling, data transformation, etc.
    this.axiosInstance.interceptors.response.use(
      (response) => {
        // You can transform response data here if needed
        return response;
      },
      (error) => {
        // Handle errors globally
        console.error('API Error:', error);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Make a GET request
   * @param url The URL to request
   * @param config Optional Axios request configuration
   * @returns Promise with the response
   */
  public get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.get<T>(url, config);
  }

  /**
   * Make a POST request
   * @param url The URL to request
   * @param data The data to send
   * @param config Optional Axios request configuration
   * @returns Promise with the response
   */
  public post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.post<T>(url, data, config);
  }

  /**
   * Make a PUT request
   * @param url The URL to request
   * @param data The data to send
   * @param config Optional Axios request configuration
   * @returns Promise with the response
   */
  public put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.put<T>(url, data, config);
  }

  /**
   * Make a DELETE request
   * @param url The URL to request
   * @param config Optional Axios request configuration
   * @returns Promise with the response
   */
  public delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.delete<T>(url, config);
  }

  /**
   * Make a PATCH request
   * @param url The URL to request
   * @param data The data to send
   * @param config Optional Axios request configuration
   * @returns Promise with the response
   */
  public patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.patch<T>(url, data, config);
  }

  /**
   * Get the Axios instance for custom requests
   * @returns The Axios instance
   */
  public getAxiosInstance(): AxiosInstance {
    return this.axiosInstance;
  }

  /**
   * Change the environment at runtime
   * @param env The environment to set
   */
  public setEnvironment(env: 'default' | 'local' | 'dev' | 'preprod' | 'production'): void {
    this.environmentService.setEnvironment(env);
  }
}
