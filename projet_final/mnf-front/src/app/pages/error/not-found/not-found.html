<div class="error-container">
  <mat-card class="error-card">
    <mat-card-header>
      <mat-card-title>
        <h1 class="error-title">{{ 'error.notFound.title' | translate }}</h1>
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="error-icon">
        <mat-icon>error_outline</mat-icon>
      </div>
      <p class="error-message">{{ 'error.notFound.message' | translate }}</p>
    </mat-card-content>
    <mat-card-actions>
      <a mat-raised-button color="primary" routerLink="/">
        {{ 'error.backToHome' | translate }}
      </a>
    </mat-card-actions>
  </mat-card>
</div>
