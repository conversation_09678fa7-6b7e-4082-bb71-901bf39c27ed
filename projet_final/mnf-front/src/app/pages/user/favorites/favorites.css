.favorites-container {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  padding: 2rem;
  min-height: calc(100vh - 64px);
  background-color: var(--bg-secondary);
}

.content-wrapper {
  width: 100%;
  max-width: 800px;
}

.favorites-card {
  background-color: var(--bg-card);
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.search-container {
  margin-bottom: 1.5rem;
}

.search-field {
  width: 100%;
}

.pagination-container {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

.favorites-title {
  color: var(--text-primary);
  font-size: 1.75rem;
  margin-bottom: 1.5rem;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.75rem;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  padding: 2rem;
}

.error-message {
  color: var(--error-color);
  background-color: var(--error-bg);
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 4rem;
  height: 4rem;
  width: 4rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.home-button {
  margin-top: 1rem;
}

.favorites-table-container {
  overflow-x: auto;
  margin-top: 1rem;
}

.favorites-table {
  width: 100%;
  background-color: var(--bg-card);
}

.mat-column-actions {
  width: 80px;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .favorites-container {
    padding: 1rem;
  }

  .favorites-card {
    padding: 1rem;
  }

  .favorites-title {
    font-size: 1.5rem;
  }
}
