import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslatePipe } from '../../../pipes/translate.pipe';
import { Router } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import { FavoritesService, Favorite } from '../../../services/favorites/favorites.service';
import { UserService } from '../../../services/admin/user.service';
import { PaginationComponent } from '../../../components/shared/pagination/pagination';
import { TranslationService } from '../../../services/translation.service';

@Component({
  selector: 'app-favorites',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatInputModule,
    MatFormFieldModule,
    TranslatePipe,
    PaginationComponent
  ],
  templateUrl: './favorites.html',
  styleUrl: './favorites.css'
})
export class FavoritesPage implements OnInit {
  // Favorites section
  favorites: Favorite[] = [];
  displayedColumns: string[] = ['lastname', 'firstname', 'actions'];
  loading = true;
  error = '';

  // Users section
  users: any[] = [];
  filteredUsers: any[] = [];
  userDisplayedColumns: string[] = ['lastname', 'firstname', 'actions'];
  usersLoading = true;
  usersError = '';
  searchTerm = '';

  // Pagination for users
  currentPage = 1;
  pageSize = 5;
  totalItems = 0;
  totalPages = 0;

  constructor(
    private router: Router,
    private authService: AuthService,
    private favoritesService: FavoritesService,
    private userService: UserService,
    private snackBar: MatSnackBar,
    private translationService: TranslationService
  ) {}

  ngOnInit(): void {
    // Get the current user from the auth service
    const user = this.authService.currentUserValue;

    // If user is not authenticated, redirect to auth page
    if (!user) {
      this.router.navigate(['/auth']);
      return;
    }

    // Load favorites
    this.loadFavorites(user.id);

    // Load users
    this.loadUsers();
  }

  /**
   * Load favorites for the current user
   */
  loadFavorites(userId: number): void {
    this.loading = true;
    // Use the new getCurrentUserFavorites method if no userId is provided
    const favoritesPromise = userId
      ? this.favoritesService.getFavorites(userId)
      : this.favoritesService.getCurrentUserFavorites();

    favoritesPromise
      .then(favorites => {
        this.favorites = favorites;
        this.loading = false;
      })
      .catch(error => {
        this.error = this.translationService.translate('favorites.loadError');
        this.loading = false;
        console.error('Error loading favorites:', error);
      });
  }

  /**
   * Remove a user from favorites
   */
  removeFavorite(favoriteIdOrEmailOrUsername: number | string): void {
    // If the parameter is a string, check if it's an email (contains @)
    if (typeof favoriteIdOrEmailOrUsername === 'string') {
      if (favoriteIdOrEmailOrUsername.includes('@')) {
        // It's an email
        this.favoritesService.removeFavoriteByEmail(favoriteIdOrEmailOrUsername)
          .then(success => {
            if (success) {
              // Filter out the favorite with the matching email
              this.favorites = this.favorites.filter(fav =>
                fav.favoriteUserEmail?.toLowerCase() !== favoriteIdOrEmailOrUsername.toLowerCase()
              );
              this.snackBar.open(
                this.translationService.translate('favorites.removeSuccess'),
                this.translationService.translate('favorites.close'),
                { duration: 3000 }
              );
            } else {
              this.snackBar.open(
                this.translationService.translate('favorites.removeError'),
                this.translationService.translate('favorites.close'),
                { duration: 3000 }
              );
            }
          })
          .catch(error => {
            this.snackBar.open(
              this.translationService.translate('favorites.removeError'),
              this.translationService.translate('favorites.close'),
              { duration: 3000 }
            );
            console.error('Error removing favorite by email:', error);
          });
      } else {
        // It's a username (deprecated)
        this.favoritesService.removeFavoriteByUsername(favoriteIdOrEmailOrUsername)
          .then(success => {
            if (success) {
              // Filter out the favorite with the matching username
              this.favorites = this.favorites.filter(fav =>
                fav.favoriteUserName?.toLowerCase() !== favoriteIdOrEmailOrUsername.toLowerCase()
              );
              this.snackBar.open(
                this.translationService.translate('favorites.removeSuccess'),
                this.translationService.translate('favorites.close'),
                { duration: 3000 }
              );
            } else {
              this.snackBar.open(
                this.translationService.translate('favorites.removeError'),
                this.translationService.translate('favorites.close'),
                { duration: 3000 }
              );
            }
          })
          .catch(error => {
            this.snackBar.open(
              this.translationService.translate('favorites.removeError'),
              this.translationService.translate('favorites.close'),
              { duration: 3000 }
            );
            console.error('Error removing favorite by username:', error);
          });
      }
    } else {
      // It's an ID
      this.favoritesService.removeFavorite(favoriteIdOrEmailOrUsername)
        .then(success => {
          if (success) {
            this.favorites = this.favorites.filter(fav => fav.id !== favoriteIdOrEmailOrUsername);
            this.snackBar.open(
              this.translationService.translate('favorites.removeSuccess'),
              this.translationService.translate('favorites.close'),
              { duration: 3000 }
            );
          } else {
            this.snackBar.open(
              this.translationService.translate('favorites.removeError'),
              this.translationService.translate('favorites.close'),
              { duration: 3000 }
            );
          }
        })
        .catch(error => {
          this.snackBar.open(
            this.translationService.translate('favorites.removeError'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
          console.error('Error removing favorite:', error);
        });
    }
  }

  /**
   * Navigate to home page
   */
  goToHome(): void {
    this.router.navigate(['/']);
  }

  /**
   * Load all users
   */
  loadUsers(): void {
    this.usersLoading = true;
    this.userService.getUsers()
      .then(users => {
        // Filter out users with role "ADMIN", keep only "USER" role
        this.users = users.filter(user => user.role !== 'ADMIN');
        this.applyFilter();
        this.usersLoading = false;
      })
      .catch(error => {
        this.usersError = this.translationService.translate('users.loadError');
        this.usersLoading = false;
        console.error('Error loading users:', error);
      });
  }

  /**
   * Apply search filter to users
   */
  applyFilter(): void {
    // Check if this.users is an array and not empty
    if (!Array.isArray(this.users)) {
      console.error('Error: this.users is not an array', this.users);
      this.filteredUsers = [];
      return;
    }

    if (!this.searchTerm.trim()) {
      this.filteredUsers = [...this.users];
    } else {
      const searchTermLower = this.searchTerm.toLowerCase().trim();
      this.filteredUsers = this.users.filter(user => {
        // Check if user has name property
        if (user.name) {
          return user.name.toLowerCase().includes(searchTermLower);
        }
        // Check if user has firstname and lastname properties
        else if (user.firstname || user.lastname) {
          const fullName = `${user.firstname || ''} ${user.lastname || ''}`.trim().toLowerCase();
          return fullName.includes(searchTermLower);
        }
        // Check email as fallback
        else if (user.email) {
          return user.email.toLowerCase().includes(searchTermLower);
        }
        return false;
      });
    }

    this.totalItems = this.filteredUsers.length;
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
    this.updatePaginatedUsers();
  }

  /**
   * Update the paginated users based on current page and page size
   */
  updatePaginatedUsers(): void {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredUsers.length);
    // We don't need to store the paginated users separately as we'll calculate them in the template
  }

  /**
   * Get the current page of users
   */
  get paginatedUsers(): any[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredUsers.length);
    return this.filteredUsers.slice(startIndex, endIndex);
  }

  /**
   * Handle page change event
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updatePaginatedUsers();
  }

  /**
   * Add a user to favorites
   */
  addToFavorites(user: any): void {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      this.snackBar.open(
        this.translationService.translate('favorites.loginRequired'),
        this.translationService.translate('favorites.close'),
        { duration: 3000 }
      );
      return;
    }

    // If email is available, use the email-based endpoint
    if (user.email) {
      this.favoritesService.addFavoriteByEmail(user.email)
        .then(favorite => {
          this.favorites.push(favorite);
          this.snackBar.open(
            this.translationService.translate('favorites.addSuccess'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
        })
        .catch(error => {
          this.snackBar.open(
            this.translationService.translate('favorites.addError'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
          console.error('Error adding favorite by email:', error);
        });
    }
    // If username is available but no email, use the username-based endpoint (deprecated)
    else if (user.username) {
      this.favoritesService.addFavoriteByUsername(user.username)
        .then(favorite => {
          this.favorites.push(favorite);
          this.snackBar.open(
            this.translationService.translate('favorites.addSuccess'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
        })
        .catch(error => {
          this.snackBar.open(
            this.translationService.translate('favorites.addError'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
          console.error('Error adding favorite by username:', error);
        });
    } else {
      // Determine the name to use
      let userName = user.name;
      if (!userName && (user.firstname || user.lastname)) {
        userName = `${user.firstname || ''} ${user.lastname || ''}`.trim();
      }

      // Fall back to the old method if neither email nor username is available
      this.favoritesService.addFavorite(currentUser.id, user.id, userName, user.email)
        .then(favorite => {
          this.favorites.push(favorite);
          this.snackBar.open(
            this.translationService.translate('favorites.addSuccess'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
        })
        .catch(error => {
          this.snackBar.open(
            this.translationService.translate('favorites.addError'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
          console.error('Error adding favorite:', error);
        });
    }
  }

  /**
   * Check if a user is already in favorites
   */
  isInFavorites(userId: number): boolean {
    return this.favorites.some(fav => fav.favoriteUserId === userId);
  }
}
