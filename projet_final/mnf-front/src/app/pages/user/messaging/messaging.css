/* Messaging Container */
.messaging-container {
  display: flex;
  height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: var(--background-color);
  color: var(--text-color);
}

/* Conversations Panel */
.conversations-panel {
  width: 350px;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  background-color: var(--surface-color);
}

.conversations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.conversations-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--primary-color);
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.conversation-item:hover {
  background-color: var(--surface-color);
}

.conversation-item.active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-left: 3px solid var(--primary-color);
}

.avatar-container {
  margin-right: 12px;
  position: relative;
}

.user-avatar, .group-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  position: relative;
}

.user-avatar.small, .group-avatar.small {
  width: 36px;
  height: 36px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.avatar-placeholder.small {
  font-size: 1.2rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: absolute;
  bottom: 0;
  right: 0;
  border: 2px solid var(--surface-color);
}

.status-indicator.small {
  width: 8px;
  height: 8px;
}

.status-indicator.online {
  background-color: #4CAF50;
}

.status-indicator.away {
  background-color: #FFC107;
}

.status-indicator.busy {
  background-color: #F44336;
}

.status-indicator.offline {
  background-color: #9E9E9E;
}

.status-text {
  font-size: 0.8rem;
  margin-top: -5px;
}

.status-text.online {
  color: #4CAF50;
}

.status-text.away {
  color: #FFC107;
}

.status-text.busy {
  color: #F44336;
}

.status-text.offline {
  color: #9E9E9E;
}

.conversation-details {
  flex: 1;
  min-width: 0; /* Ensure text truncation works */
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}

.conversation-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 0.75rem;
  color: var(--text-color);
  opacity: 0.7;
}

.conversation-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-text {
  margin: 4px 0 0;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-color);
  opacity: 0.8;
  max-width: 200px;
}

.no-messages {
  font-style: italic;
  opacity: 0.6;
}

.unread-badge {
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  padding: 0 4px;
}

.delete-button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-item:hover .delete-button {
  opacity: 0.7;
}

.conversation-item:hover .delete-button:hover {
  opacity: 1;
}

/* Chat Panel */
.chat-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color);
}

.chat-title {
  display: flex;
  align-items: center;
}

.chat-title h3 {
  margin: 0 0 0 12px;
  font-size: 1.1rem;
}

.chat-actions {
  display: flex;
  align-items: center;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: var(--background-color);
}

.messages-list {
  display: flex;
  flex-direction: column;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  max-width: 80%;
}

.message-item.incoming {
  align-self: flex-start;
}

.message-item.outgoing {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  margin-right: 8px;
}

.message-item.outgoing .message-avatar {
  margin-right: 0;
  margin-left: 8px;
}

.message-content {
  display: flex;
  flex-direction: column;
}

.message-sender {
  font-size: 0.8rem;
  margin-bottom: 4px;
  color: var(--primary-color);
}

.message-bubble {
  padding: 10px 12px;
  border-radius: 18px;
  position: relative;
}

.message-item.incoming .message-bubble {
  background-color: var(--surface-color);
  border-bottom-left-radius: 4px;
}

.message-item.outgoing .message-bubble {
  background-color: var(--primary-color);
  color: white;
  border-bottom-right-radius: 4px;
}

.message-bubble p {
  margin: 0;
  word-break: break-word;
}

.message-time {
  font-size: 0.7rem;
  margin-top: 4px;
  align-self: flex-end;
  opacity: 0.7;
}

.message-item.outgoing .message-time {
  color: rgba(255, 255, 255, 0.9);
}

.message-input {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
  background-color: var(--surface-color);
}

.message-field {
  flex: 1;
  margin-right: 8px;
}

/* Loading and Empty States */
.loading-container, .empty-state, .empty-chat, .empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
  color: var(--primary-color);
}

/* Dark Mode Specific Adjustments */
[data-theme="dark"] .conversation-item.active {
  background-color: rgba(var(--primary-color-rgb), 0.2);
}

[data-theme="dark"] .message-item.incoming .message-bubble {
  background-color: var(--card-background);
}

/* Add CSS variable for primary color with RGB values for opacity */
:root {
  --primary-color-rgb: 63, 81, 181; /* RGB values for #3f51b5 */
}

[data-theme="dark"] {
  --primary-color-rgb: 121, 134, 203; /* RGB values for #7986cb */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .messaging-container {
    flex-direction: column;
    height: calc(100vh - 56px); /* Adjust for smaller header on mobile */
  }

  .conversations-panel {
    width: 100%;
    height: 40%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .chat-panel {
    height: 60%;
  }

  .message-item {
    max-width: 90%;
  }
}
