<div class="question-series-container">
  <!-- Left panel: Chapters and Questions -->
  <div class="left-panel">

    <!-- Chapters list -->
    <div class="chapters-container">
      @if (loadingChapters) {
        <div class="loading-container">
          <mat-spinner diameter="30"></mat-spinner>
          <span i18n="@@loadingChapters">Loading chapters...</span>
        </div>
      } @else if (chapterError) {
        <div class="error-message" i18n="@@chapterErrorMessage">{{ chapterError }}</div>
      } @else if (chapters.length === 0) {
        <div class="empty-message" i18n="@@noChaptersAvailable">No chapters available</div>
      } @else {
        <mat-accordion>
          @for (chapter of chapters; track chapter.id) {
            <mat-expansion-panel
              [expanded]="selectedChapter?.id === chapter.id"
              (opened)="loadQuestionsForChapter(chapter)">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <span style="color: #fff; font-weight: 500;">
                    {{ chapter.title }}
                  </span>
                </mat-panel-title>
                <mat-panel-description class="chapter-actions">
                  <button
                    mat-icon-button
                    color="primary"
                    style="color: #fff;"
                    (click)="$event.stopPropagation(); addRandomQuestions(chapter)"
                    i18n-title="@@addRandomQuestionsFromChapter" title="Add random questions from this chapter">
                    <mat-icon>shuffle</mat-icon>
                  </button>
                </mat-panel-description>
              </mat-expansion-panel-header>

              <!-- Questions list -->
              <div class="questions-container">
                @if (loadingQuestions && selectedChapter?.id === chapter.id) {
                  <div class="loading-container">
                    <mat-spinner diameter="24"></mat-spinner>
                    <span i18n="@@loadingQuestions" style="color: #fff;">Loading questions...</span>
                  </div>
                } @else if (questionError && selectedChapter?.id === chapter.id) {
                  <div class="error-message" i18n="@@questionErrorMessage">{{ questionError }}</div>
                } @else if (chapterQuestions.length === 0 && selectedChapter?.id === chapter.id) {
                  <div class="empty-message"><span style="color: #fff" i18n="@@noQuestionsForChapter">No questions available for this chapter</span></div>
                } @else if (selectedChapter?.id === chapter.id) {
                  <div
                    cdkDropList
                    #questionsList="cdkDropList"
                    id="questionsList"
                    [cdkDropListData]="chapterQuestionsAsItems"
                    [cdkDropListConnectedTo]="['seriesList']"
                    class="questions-list"
                    (cdkDropListDropped)="onQuestionDrop($event)">
                    @for (question of chapterQuestionsAsItems; track question.questionId) {
                      <div class="question-item" cdkDrag [cdkDragData]="question">
                        <div class="question-content">
                          <div class="question-title">{{ question.title }}</div>
                          <div class="question-statement" [innerHTML]="question.statement"></div>
                        </div>
                        <div class="question-actions">
                          <button mat-icon-button (click)="$event.stopPropagation(); openQuestionDetails(question)" i18n-title="@@viewQuestionDetails" title="View question details">
                            <mat-icon>visibility</mat-icon>
                          </button>
                          <button mat-icon-button cdkDragHandle (click)="$event.stopPropagation()" i18n-title="@@dragToReorder" title="Drag to reorder">
                            <mat-icon>drag_indicator</mat-icon>
                          </button>
                        </div>
                      </div>
                    }
                  </div>
                }
              </div>
            </mat-expansion-panel>
          }
        </mat-accordion>

        <!-- Enhanced pagination -->
        <div class="pagination-wrapper">
          <div class="pagination-left">
            <div class="page-size-selector">
              <label for="pageSize" i18n="@@itemsPerPage">Items per page:</label>
              <select
                id="pageSize"
                [(ngModel)]="pageSize"
                (change)="onPageSizeChange()"
              >
                <option value="10">10</option>
              </select>
            </div>
            <div class="pagination-info">
              @if (totalChapters > 0) {
                <span i18n="@@paginationInfoChapters">Showing {{ (pageIndex * pageSize) + 1 }} to {{ Math.min((pageIndex + 1) * pageSize, totalChapters) }} of {{ totalChapters }} chapters</span>
              } @else {
                <span i18n="@@noChaptersToDisplay">No chapters to display</span>
              }
            </div>
          </div>
          <div class="pagination-controls">
            <div class="pagination-buttons">
              <button
                class="pagination-button"
                [disabled]="pageIndex === 0"
                (click)="goToPreviousPage()"
                [attr.aria-label]="'Previous page'" i18n-attr.aria-label="@@previousPage">
                <span class="material-icons">chevron_left</span>
              </button>

              @for (pageNum of getPageNumbers(); track pageNum) {
                @if (pageNum === -1 || pageNum === -2) {
                  <span class="pagination-ellipsis">...</span>
                } @else {
                  <button
                    class="pagination-button page-number"
                    [class.active]="pageIndex === pageNum"
                    (click)="goToPage(pageNum)"
                    [attr.aria-label]="'Go to page ' + (pageNum + 1)" i18n-attr.aria-label="@@goToPage">
                    {{ pageNum + 1 }}
                  </button>
                }
              }

              <button
                class="pagination-button"
                [disabled]="pageIndex >= Math.ceil(totalChapters / pageSize) - 1"
                (click)="goToNextPage()"
                [attr.aria-label]="'Next page'" i18n-attr.aria-label="@@nextPage">
                <span class="material-icons">chevron_right</span>
              </button>
            </div>
          </div>
        </div>
      }
    </div>
  </div>

  <!-- Right panel: Question Series -->
  <div class="right-panel">

    <!-- Series form -->
    <div class="series-form">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label i18n="@@seriesName">Series Name</mat-label>
        <input matInput [(ngModel)]="seriesName" i18n-placeholder="@@enterSeriesName" placeholder="Enter series name">
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label i18n="@@description">Description</mat-label>
        <textarea matInput [(ngModel)]="seriesDescription" i18n-placeholder="@@enterDescription" placeholder="Enter description" rows="2"></textarea>
      </mat-form-field>

      <div class="form-actions">
        @if (currentSeries) {
          <button
            mat-raised-button
            color="primary"
            (click)="updateSeries()"
            [disabled]="!seriesName.trim() || loadingSeries">
            <mat-icon>save</mat-icon>
            <span i18n="@@updateSeries">Update Series</span>
          </button>
        }
      </div>
    </div>

    <mat-divider></mat-divider>

    <!-- Series items -->
    <div class="series-items-container">
      <div class="series-header">
        <h3 i18n="@@questionsInSeries">Questions in this Series</h3>
        <div class="series-actions">
          <button mat-icon-button (click)="sortQuestionsByChapter()" i18n-title="@@sortByChapter" title="Sort by chapter">
            <mat-icon>sort</mat-icon>
          </button>
          <button mat-icon-button (click)="randomizeQuestions()" i18n-title="@@randomizeQuestionsOrder" title="Randomize questions order">
            <mat-icon>shuffle</mat-icon>
          </button>
          <button mat-icon-button (click)="openSeriesStatistics()" i18n-title="@@viewStatistics" title="View statistics">
            <mat-icon>bar_chart</mat-icon>
          </button>
          <button mat-icon-button color="warn" (click)="deleteAllQuestions()" i18n-title="@@deleteAllQuestions" title="Delete all questions">
            <mat-icon>delete_sweep</mat-icon>
          </button>
        </div>
      </div>

      @if (loadingSeries) {
        <div class="loading-container">
          <mat-spinner diameter="30"></mat-spinner>
          <span i18n="@@loadingSeries">Loading series...</span>
        </div>
      } @else if (seriesError) {
        <div class="error-message" i18n="@@seriesErrorMessage">{{ seriesError }}</div>
      } @else {
        <div
          cdkDropList
          #seriesList="cdkDropList"
          id="seriesList"
          [cdkDropListData]="seriesItems"
          [cdkDropListConnectedTo]="['questionsList']"
          class="series-list"
          (cdkDropListDropped)="onQuestionDrop($event)">
          @if (seriesItems.length === 0) {
            <div class="empty-message" style="pointer-events: none;" i18n="@@dragQuestionsHere">Drag questions here to add them to the series</div>
          }
          @for (item of seriesItems; track item.questionId) {
            <div class="series-item" cdkDrag [cdkDragData]="item">
              <div class="series-item-content">
                <div class="series-item-chapter">{{ item.chapterName }}</div>
                <div class="series-item-title">{{ item.title }}</div>
                <div class="series-item-statement" [innerHTML]="item.statement"></div>
              </div>
              <div class="series-item-actions">
                <button mat-icon-button (click)="$event.stopPropagation(); openQuestionDetails(item)" i18n-title="@@viewQuestionDetails" title="View question details">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button (click)="$event.stopPropagation(); removeQuestion(item.questionId)" i18n-title="@@deleteQuestion" title="Delete question">
                  <mat-icon>delete</mat-icon>
                </button>
                <button mat-icon-button cdkDragHandle (click)="$event.stopPropagation()" i18n-title="@@dragToReorder" title="Drag to reorder">
                  <mat-icon>drag_indicator</mat-icon>
                </button>
              </div>
            </div>
          }

          <!-- Pagination removed to fix drag and drop functionality -->
        </div>
      }
    </div>

    <!-- Create button at the bottom -->
    <div class="bottom-create-button">
      <button
        mat-raised-button
        color="primary"
        (click)="$event.stopPropagation(); createNewSeries()"
        [disabled]="loadingSeries || !seriesName.trim() || !seriesDescription.trim() || seriesItems.length < 3">
        <mat-icon>check</mat-icon>
        <span i18n="@@createSeries">Create Series</span>
      </button>
      @if (seriesItems.length < 3) {
        <div class="requirement-message" i18n="@@addMoreQuestions">
          Add at least {{ 3 - seriesItems.length }} more question(s)
        </div>
      }
    </div>
  </div>
</div>
