import { Component, OnInit, ViewChild } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatExpansionModule } from "@angular/material/expansion";
import { MatListModule } from "@angular/material/list";
import { MatCardModule } from "@angular/material/card";
import { MatDividerModule } from "@angular/material/divider";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { MatSnackBar } from "@angular/material/snack-bar";
import { MatDialog } from "@angular/material/dialog";
import { MatSelectModule } from "@angular/material/select";
import {
  MatPaginatorModule,
  PageEvent,
  MatPaginator,
} from "@angular/material/paginator";
import {
  DragDropModule,
  CdkDragDrop,
  transferArrayItem,
  moveItemInArray,
  CdkDrag,
  CdkDropList,
  DragRef,
  Point,
  CdkDragEnter,
  CdkDragExit,
  CdkDragStart,
  CdkDragEnd,
} from "@angular/cdk/drag-drop";
import { A11yModule } from "@angular/cdk/a11y";
import { Router } from "@angular/router";

import { QuestionDetailsDialogComponent } from "../../../components/user/question-details-dialog/question-details-dialog";
import { RandomQuestionsDialogComponent } from "../../../components/user/random-questions-dialog/random-questions-dialog";
import { SeriesStatisticsDialogComponent } from "../../../components/user/series-statistics-dialog/series-statistics-dialog";

import { ChapterService } from '../../../services/admin/chapter.service';
import { QuestionService } from '../../../services/admin/question.service';
import { QuestionSeriesService } from '../../../services/user/question-series.service';
import { QuizService } from '../../../services/user/quiz.service';
import { AuthService } from '../../../services/auth.service';
import { TranslationService } from '../../../services/translation.service';

import { Chapter } from "../../../models/admin/Chapter";
import { Question } from "../../../models/admin/Question";
import {
  QuestionSeries,
  QuestionSeriesItem,
} from "../../../models/user/QuestionSeries";
import { Quiz } from "../../../models/user/Quiz";

@Component({
  selector: "app-question-series",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatExpansionModule,
    MatListModule,
    MatCardModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatSelectModule,
    DragDropModule,
    A11yModule
  ],
  templateUrl: "./question-series.html",
  styleUrl: "./question-series.css",
})
export class QuestionSeriesPage implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  // Make Math available to the template
  Math = Math;

  // Data
  allChapters: Chapter[] = []; // All chapters from API
  chapters: Chapter[] = []; // Paginated chapters
  questions: { [chapterId: number]: Question[] } = {};
  selectedChapter: Chapter | null = null;
  currentSeries: QuestionSeries | null = null;
  seriesItems: QuestionSeriesItem[] = [];

  // UI state
  loadingChapters = false;
  loadingQuestions = false;
  loadingSeries = false;
  seriesName = "";
  seriesDescription = "";

  // Pagination for chapters
  pageSize = 10;
  pageIndex = 0;
  totalChapters = 0;

  // Total count for series items
  totalSeriesItems = 0;

  // Error messages
  chapterError = "";
  questionError = "";
  seriesError = "";

  constructor(
    private chapterService: ChapterService,
    private questionService: QuestionService,
    private questionSeriesService: QuestionSeriesService,
    private quizService: QuizService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private router: Router,
    private dialog: MatDialog,
    private translationService: TranslationService
  ) {}

  /**
   * Show a notification in the bottom right corner
   * @param message The translation key for the message
   * @param action The translation key for the action button text (defaults to 'common.close')
   * @param duration The duration in milliseconds to show the notification
   * @param params Optional parameters for the translation
   */
  private showNotification(
    message: string,
    action: string = "",
    duration: number = 3000,
    params: any = {}
  ): void {
    // For now, we'll handle the count parameter specially since the TranslationService doesn't support parameters
    let translatedMessage = this.translationService.translate(message);

    // Replace {count} with the actual count if provided
    if (params.count !== undefined) {
      translatedMessage = translatedMessage.replace(
        "{count}",
        params.count.toString()
      );
    }

    const translatedAction =
      action || this.translationService.translate("common.close");

    this.snackBar.open(translatedMessage, translatedAction, {
      duration: duration,
      horizontalPosition: "end",
      verticalPosition: "bottom",
    });
  }

  ngOnInit(): void {
    this.loadChapters();

    // Subscribe to current series changes
    this.questionSeriesService.currentSeries$.subscribe((series) => {
      this.currentSeries = series;
      if (series) {
        this.seriesName = series.name;
        this.seriesDescription = series.description || "";
        this.loadSeriesItems();
      }
      // We don't clear seriesItems when there's no series
      // This allows adding questions before creating a series
    });
  }

  /**
   * Load chapters for the current page
   */
  loadChapters(): void {
    this.loadingChapters = true;
    this.chapterError = "";

    // Get chapters with pagination
    this.chapterService
      .getChaptersPaginated("", this.pageIndex, this.pageSize)
      .then((result) => {
        // Filter chapters to only include those without a parent path
        const parentlessChapters = result.content.filter(
          (chapter) => !chapter.parent_path && !chapter.parentPath
        );

        this.chapters = parentlessChapters;
        this.totalChapters = result.totalElements;
        this.loadingChapters = false;
      })
      .catch((error) => {
        this.chapterError = this.translationService.translate(
          "questionSeries.errors.failedToLoadChapters"
        );
        this.loadingChapters = false;
        console.error("Error loading chapters:", error);
      });
  }

  /**
   * Navigate to the previous page
   */
  goToPreviousPage(): void {
    if (this.pageIndex > 0) {
      this.pageIndex--;
      this.loadChapters();
    }
  }

  /**
   * Navigate to the next page
   */
  goToNextPage(): void {
    const maxPageIndex = Math.ceil(this.totalChapters / this.pageSize) - 1;
    if (this.pageIndex < maxPageIndex) {
      this.pageIndex++;
      this.loadChapters();
    }
  }

  /**
   * Navigate to a specific page
   */
  goToPage(pageIndex: number): void {
    const maxPageIndex = Math.ceil(this.totalChapters / this.pageSize) - 1;
    if (pageIndex >= 0 && pageIndex <= maxPageIndex) {
      this.pageIndex = pageIndex;
      this.loadChapters();
    }
  }

  /**
   * Get an array of page numbers for pagination with ellipsis
   */
  getPageNumbers(): number[] {
    const totalPages = Math.ceil(this.totalChapters / this.pageSize);

    // If we have 7 or fewer pages, show all pages
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i);
    }

    // Otherwise, show first page, last page, and pages around current page
    const pageNumbers: number[] = [];

    // Always add first page
    pageNumbers.push(0);

    // Calculate range around current page
    const startPage = Math.max(1, this.pageIndex - 1);
    const endPage = Math.min(totalPages - 2, this.pageIndex + 1);

    // Add ellipsis after first page if needed
    if (startPage > 1) {
      pageNumbers.push(-1); // -1 represents ellipsis
    }

    // Add pages around current page
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    // Add ellipsis before last page if needed
    if (endPage < totalPages - 2) {
      pageNumbers.push(-2); // -2 represents ellipsis
    }

    // Always add last page
    pageNumbers.push(totalPages - 1);

    return pageNumbers;
  }

  /**
   * Handle page size change event for chapters
   */
  onPageSizeChange(): void {
    // Reset to first page when changing page size
    this.pageIndex = 0;
    if (this.paginator) {
      this.paginator.pageIndex = 0;
    }
    this.loadChapters();
  }

  /**
   * Load questions for a chapter
   */
  loadQuestionsForChapter(chapter: Chapter): void {
    if (this.questions[chapter.id!]) {
      // Questions already loaded
      this.selectedChapter = chapter;
      return;
    }

    this.loadingQuestions = true;
    this.questionError = "";
    this.selectedChapter = chapter;

    this.questionService
      .getQuestionsByChapterId(chapter.id!)
      .then((questions) => {
        this.questions[chapter.id!] = questions;
        this.loadingQuestions = false;
      })
      .catch((error) => {
        this.questionError = this.translationService.translate(
          "questionSeries.errors.failedToLoadQuestions"
        );
        this.loadingQuestions = false;
        console.error("Error loading questions:", error);
      });
  }

  /**
   * Load items for the current series
   */
  loadSeriesItems(): void {
    if (!this.currentSeries) return;

    this.loadingSeries = true;
    this.seriesError = "";
    this.seriesItems = [];

    // Ensure questions is an array before mapping
    if (!this.currentSeries.questions) {
      this.currentSeries.questions = [];
      console.warn(
        "Series questions was undefined, initialized to empty array"
      );
    }

    // Load questions for each question ID in the series
    const promises = this.currentSeries.questions.map((questionId) =>
      this.questionService.getQuestionById(questionId)
    );

    Promise.all(promises)
      .then((questions) => {
        // Filter out null values and map to series items
        this.seriesItems = questions
          .filter((q) => q !== null)
          .map((q) => {
            // Handle both API formats (title/statement and titre/contenu)
            const title = (q as any).titre || q!.title;
            const statement = (q as any).contenu || q!.statement;

            return {
              questionId: q!.id!,
              title: title,
              statement: statement,
              chapterId: q!.chapter_id,
              chapterName: q!.chapter?.name,
            };
          });

        // Update total count for series items
        this.totalSeriesItems = this.seriesItems.length;

        this.loadingSeries = false;
      })
      .catch((error) => {
        this.seriesError = this.translationService.translate(
          "questionSeries.errors.failedToLoadSeriesItems"
        );
        this.loadingSeries = false;
        console.error("Error loading series items:", error);
      });
  }

  // Flag to prevent duplicate calls
  private isCreatingSeries = false;

  /**
   * Create a new series and create a quiz from it
   */
  createNewSeries(): void {
    console.log(
      "Creating new series with name:",
      this.seriesName,
      "and description:",
      this.seriesDescription
    );

    // Prevent duplicate calls
    if (this.isCreatingSeries) {
      console.log("Already creating series, ignoring duplicate call");
      return;
    }

    if (!this.seriesName.trim()) {
      this.showNotification("questionSeries.validation.enterSeriesName");
      return;
    }

    if (!this.seriesDescription.trim()) {
      this.showNotification("questionSeries.validation.enterSeriesDescription");
      return;
    }

    if (this.seriesItems.length < 3) {
      this.showNotification(
        "questionSeries.validation.addAtLeastThreeQuestions"
      );
      return;
    }

    this.isCreatingSeries = true;
    this.loadingSeries = true;
    this.seriesError = "";

    // Ensure we have question IDs for the quiz
    console.log("SeriesItems before mapping:", this.seriesItems);
    // Filter out any items with null or undefined questionId
    const validSeriesItems = this.seriesItems.filter(
      (item) => item.questionId != null
    );
    const questionIds = validSeriesItems.map((item) => item.questionId);
    console.log("Extracted questionIds:", questionIds);
    console.log("Current series:", this.currentSeries);

    if (questionIds.length === 0) {
      this.showNotification("questionSeries.validation.noQuestionsSelected");
      this.loadingSeries = false;
      return;
    }

    // If no series exists yet, create one
    if (!this.currentSeries) {
      // Create a copy of seriesItems to use for creating the quiz directly
      const questionItems = [...this.seriesItems];

      console.log(
        "Calling createEmptySeries with name:",
        this.seriesName,
        "and description:",
        this.seriesDescription
      );
      this.questionSeriesService
        .createEmptySeries(this.seriesName, this.seriesDescription, questionIds)
        .then((series) => {
          console.log("createEmptySeries returned series:", series);
          if (series) {
            // Set as current series
            console.log("Setting current series:", series);
            this.questionSeriesService.setCurrentSeries(series);
            this.currentSeries = series;

            // Create a quiz directly using the questionItems we already have
            // This ensures we have all questions available for the quiz
            console.log("Creating quiz with series:", series);
            console.log("Using questionIds:", questionIds);

            // Create an array of QuizQuestion objects with position information and question objects
            const quizQuestions = questionIds.map((questionId, index) => {
              // Try to find the corresponding question item to get title and statement
              const questionItem = questionItems.find(
                (item) => item.questionId === questionId
              );

              return {
                position: index + 1,
                question: {
                  id: questionId,
                  title: questionItem?.title || "", // Use title from question item if available
                  statement: questionItem?.statement || "", // Use statement from question item if available
                },
              };
            });

            const quiz: Quiz = {
              name: `Quiz: ${series.name}`,
              description: series.description,
              userId: this.authService.currentUserValue?.id!,
              questions: questionIds, // Keep the question IDs for backward compatibility
              quizQuestions: quizQuestions, // Add the quiz questions with position information
            };

            console.log("Quiz object before sending:", quiz);
            console.log("Questions to be included:", questionIds);
            console.log(
              "Quiz questions array type:",
              Array.isArray(quiz.questions) ? "Array" : typeof quiz.questions
            );

            // Create the quiz
            console.log("Calling quizService.createQuiz with quiz:", quiz);
            this.quizService
              .createQuiz(quiz)
              .then((createdQuiz) => {
                console.log(
                  "quizService.createQuiz returned quiz:",
                  createdQuiz
                );
                if (createdQuiz) {
                  console.log("Quiz created successfully:", createdQuiz);
                  this.showNotification(
                    "questionSeries.success.seriesAndQuizCreated"
                  );
                  // Redirect to quiz management
                  this.router.navigate(["/quiz-management"]);
                } else {
                  console.error(
                    "Failed to create quiz: createdQuiz is null or undefined"
                  );
                  this.seriesError = this.translationService.translate(
                    "questionSeries.errors.failedToCreateQuiz"
                  );
                  this.loadingSeries = false;
                }
                // Reset the flag
                this.isCreatingSeries = false;
              })
              .catch((error) => {
                console.error("Error creating quiz:", error);
                if (
                  error.message === "Quiz must contain at least one question"
                ) {
                  this.showNotification(
                    "questionSeries.errors.quizMustContainOneQuestion"
                  );
                  this.seriesError = this.translationService.translate(
                    "questionSeries.errors.quizMustContainOneQuestion"
                  );
                } else {
                  this.seriesError = this.translationService.translate(
                    "questionSeries.errors.failedToCreateQuiz"
                  );
                }
                this.loadingSeries = false;
                // Reset the flag
                this.isCreatingSeries = false;
              });

            // Also add all questions to the series for future reference
            console.log(
              "Adding questions to series. questionItems:",
              questionItems
            );
            questionItems.forEach((item) => {
              console.log("Adding question to series:", item);
              this.questionSeriesService.addQuestionToCurrentSeries(item);
            });
            console.log("All questions added to series");
          } else {
            this.seriesError = this.translationService.translate(
              "questionSeries.errors.failedToCreateSeries"
            );
            this.loadingSeries = false;
            // Reset the flag
            this.isCreatingSeries = false;
          }
        })
        .catch((error) => {
          console.error("Error creating series:", error);
          if (error.message === "Series must contain at least one question") {
            this.showNotification(
              "questionSeries.errors.seriesMustContainOneQuestion"
            );
            this.seriesError = this.translationService.translate(
              "questionSeries.errors.seriesMustContainOneQuestion"
            );
          } else {
            this.seriesError = this.translationService.translate(
              "questionSeries.errors.failedToCreateSeries"
            );
          }
          this.loadingSeries = false;
          // Reset the flag
          this.isCreatingSeries = false;
        });
      return;
    }

    // The series is already created, we just need to update it with the final data
    // Create a copy of seriesItems to use for creating the quiz directly
    const questionItems = [...this.seriesItems];

    const updatedSeries = {
      ...this.currentSeries,
      name: this.seriesName,
      description: this.seriesDescription,
    };

    this.questionSeriesService
      .updateQuestionSeries(this.currentSeries.id!, updatedSeries)
      .then((series) => {
        if (series) {
          // Create a quiz directly using the questionItems we already have
          // This ensures we have all questions available for the quiz
          // Create an array of QuizQuestion objects with position information and question objects
          const quizQuestions = questionIds.map((questionId, index) => {
            // Try to find the corresponding question item to get title and statement
            const questionItem = questionItems.find(
              (item) => item.questionId === questionId
            );

            return {
              position: index + 1,
              question_id: questionId,
            };
          });

          const quiz: Quiz = {
            name: `Quiz: ${series.name}`,
            description: series.description,
            userId: this.authService.currentUserValue?.id!,
            questions: questionIds, // Keep the question IDs for backward compatibility
            quizQuestions: quizQuestions, // Add the quiz questions with position information
          };

          console.log("Quiz object before sending (update case):", quiz);
          console.log("Questions to be included (update case):", questionIds);

          // Create the quiz
          this.quizService
            .createQuiz(quiz)
            .then((createdQuiz) => {
              if (createdQuiz) {
                this.showNotification(
                  "questionSeries.success.seriesAndQuizCreated"
                );
                // Redirect to quiz management
                this.router.navigate(["/quiz-management"]);
              } else {
                this.seriesError = "Failed to create quiz";
                this.loadingSeries = false;
              }
              // Reset the flag
              this.isCreatingSeries = false;
            })
            .catch((error) => {
              console.error("Error creating quiz:", error);
              if (error.message === "Quiz must contain at least one question") {
                this.showNotification(
                  "questionSeries.errors.quizMustContainOneQuestion"
                );
                this.seriesError = this.translationService.translate(
                  "questionSeries.errors.quizMustContainOneQuestion"
                );
              } else {
                this.seriesError = this.translationService.translate(
                  "questionSeries.errors.failedToCreateQuiz"
                );
              }
              this.loadingSeries = false;
              // Reset the flag
              this.isCreatingSeries = false;
            });
        } else {
          this.seriesError = this.translationService.translate(
            "questionSeries.errors.failedToFinalizeSeries"
          );
          this.loadingSeries = false;
          // Reset the flag
          this.isCreatingSeries = false;
        }
      })
      .catch((error) => {
        this.seriesError = this.translationService.translate(
          "questionSeries.errors.failedToFinalizeSeries"
        );
        this.loadingSeries = false;
        console.error("Error finalizing series:", error);
        // Reset the flag
        this.isCreatingSeries = false;
      });
  }

  /**
   * Create a quiz from a series
   */
  private createQuizFromSeries(series: QuestionSeries): void {
    console.log(
      "createQuizFromSeries - seriesItems length:",
      this.seriesItems.length
    );
    console.log("createQuizFromSeries - series.questions:", series.questions);

    // Check if series.questions is empty or has fewer items than seriesItems
    // If so, use question IDs from seriesItems instead
    let questionIds: number[] = [];
    if (
      !series.questions ||
      series.questions.length === 0 ||
      series.questions.length < this.seriesItems.length
    ) {
      console.log(
        "Using question IDs from seriesItems instead of series.questions"
      );
      // Filter out any items with null or undefined questionId
      const validSeriesItems = this.seriesItems.filter(
        (item) => item.questionId != null
      );
      questionIds = validSeriesItems.map((item) => item.questionId);
    } else {
      // Filter out any null or undefined values from series.questions
      questionIds = series.questions.filter((id) => id != null);
    }

    console.log("Question IDs to be used:", questionIds);
    console.log("Series items:", this.seriesItems);

    // Check if we have any valid question IDs after filtering
    if (questionIds.length === 0) {
      console.error("No valid question IDs found after filtering");
      this.showNotification("questionSeries.errors.noValidQuestionsSelected");
      return;
    }

    // Create a quiz from the series
    // Create an array of QuizQuestion objects with position information and question objects
    const quizQuestions = questionIds.map((questionId, index) => {
      // Try to find the corresponding series item to get title and statement
      const seriesItem = this.seriesItems.find(
        (item) => item.questionId === questionId
      );

      return {
        position: index + 1,
        question: {
          id: questionId,
          title: seriesItem?.title || "", // Use title from series item if available
          statement: seriesItem?.statement || "", // Use statement from series item if available
        },
      };
    });

    const quiz: Quiz = {
      name: `Quiz: ${series.name}`,
      description: series.description,
      userId: this.authService.currentUserValue?.id!,
      questions: questionIds, // Keep the question IDs for backward compatibility
      quizQuestions: quizQuestions, // Add the quiz questions with question objects
    };
    console.log("Quiz object before sending:", quiz);

    this.quizService
      .createQuiz(quiz)
      .then((createdQuiz) => {
        if (createdQuiz) {
          this.showNotification("questionSeries.success.seriesAndQuizCreated");
          // Redirect to quiz management
          this.router.navigate(["/quiz-management"]);
        } else {
          this.seriesError = this.translationService.translate(
            "questionSeries.errors.failedToCreateQuizFromSeries"
          );
          this.loadingSeries = false;
        }
      })
      .catch((error) => {
        console.error("Error creating quiz:", error);
        if (error.message === "Quiz must contain at least one question") {
          this.showNotification(
            "questionSeries.errors.quizMustContainOneQuestion"
          );
          this.seriesError = this.translationService.translate(
            "questionSeries.errors.quizMustContainOneQuestion"
          );
        } else {
          this.seriesError = this.translationService.translate(
            "questionSeries.errors.failedToCreateQuizFromSeries"
          );
        }
        this.loadingSeries = false;
      });
  }

  /**
   * Update the current series
   */
  updateSeries(): void {
    if (!this.currentSeries) return;

    if (!this.seriesName.trim()) {
      this.showNotification("questionSeries.validation.enterSeriesName");
      return;
    }

    this.loadingSeries = true;

    const updatedSeries = {
      ...this.currentSeries,
      name: this.seriesName,
      description: this.seriesDescription,
    };

    this.questionSeriesService
      .updateQuestionSeries(this.currentSeries.id!, updatedSeries)
      .then((series) => {
        if (series) {
          this.showNotification("questionSeries.success.seriesUpdated");
        } else {
          this.seriesError = this.translationService.translate(
            "questionSeries.errors.failedToUpdateSeries"
          );
        }
        this.loadingSeries = false;
      })
      .catch((error) => {
        this.seriesError = this.translationService.translate(
          "questionSeries.errors.failedToUpdateSeries"
        );
        this.loadingSeries = false;
        console.error("Error updating series:", error);
      });
  }

  // Initial position for drag elements
  dragPosition = { x: 0, y: 0 };

  /**
   * Handle when a drag operation starts
   */
  onDragStarted(event: CdkDragStart): void {
    console.log("Drag started:", event);
  }

  /**
   * Handle when a drag operation ends
   */
  onDragEnded(event: CdkDragEnd): void {
    console.log("Drag ended:", event);
  }

  /**
   * Handle when an item enters a drop list
   */
  onListEntered(event: CdkDragEnter): void {
    console.log("Item entered list:", event);
  }

  /**
   * Handle when an item exits a drop list
   */
  onListExited(event: CdkDragExit): void {
    console.log("Item exited list:", event);
  }

  /**
   * Constrain the position of the drag element to stay within the bounds of the container
   */
  constrainPosition(point: Point, dragRef: DragRef): Point {
    // Ensure the drag element stays within reasonable bounds
    return {
      x: Math.max(-100, Math.min(point.x, 100)),
      y: Math.max(-100, Math.min(point.y, 100)),
    };
  }

  /**
   * Predicate function to determine if an item can be dropped into a list
   */
  canEnterSeriesList(
    drag: CdkDrag<QuestionSeriesItem>,
    drop: CdkDropList<QuestionSeriesItem[]>
  ): boolean {
    // Always allow dropping within the same container (for reordering)
    if (drag.dropContainer === drop) {
      return true;
    }

    // Check if the item is already in the series
    const item = drag.data;
    return !this.seriesItems.some(
      (existingItem) => existingItem.questionId === item.questionId
    );
  }

  /**
   * Handle drag and drop of questions
   */
  onQuestionDrop(event: CdkDragDrop<QuestionSeriesItem[]>): void {
    console.log("Drop event:", event);
    console.log("Container ID:", event.container.id);
    console.log("Previous container ID:", event.previousContainer.id);

    if (event.previousContainer === event.container) {
      // Reordering within the same container
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );

      // Update the order in the service if a series exists
      if (this.currentSeries) {
        // The service might need a method to update the order, but for now
        // we'll just ensure the UI is updated
      }
    } else if (
      event.previousContainer.id === "questionsList" &&
      event.container.id === "seriesList"
    ) {
      // Moving from questions to series
      const question = event.previousContainer.data[event.previousIndex];

      // Add to series if not already there
      if (
        !this.seriesItems.some(
          (item) => item.questionId === question.questionId
        )
      ) {
        // Create a copy of the question
        const questionCopy = { ...question };

        // Add the question to the seriesItems array directly
        this.seriesItems.splice(event.currentIndex, 0, questionCopy);

        // Add to service if a series exists
        if (this.currentSeries) {
          this.questionSeriesService.addQuestionToCurrentSeries(questionCopy);
        }

        // Update total count
        this.totalSeriesItems = this.seriesItems.length;

        console.log("Added question to series:", questionCopy);
        console.log("Current seriesItems:", this.seriesItems);
      }
    } else if (
      event.previousContainer.id === "seriesList" &&
      event.container.id === "questionsList"
    ) {
      // Moving from series to questions - this shouldn't normally happen
      // but we'll handle it by removing the question from the series
      const question = event.previousContainer.data[event.previousIndex];

      // Remove from seriesItems array
      this.seriesItems = this.seriesItems.filter(
        (item) => item.questionId !== question.questionId
      );

      // Update total count
      this.totalSeriesItems = this.seriesItems.length;

      // Remove from service if a series exists
      if (this.currentSeries) {
        this.questionSeriesService.removeQuestionFromCurrentSeries(
          question.questionId
        );
      }

      console.log("Removed question from series:", question);
      console.log("Current seriesItems:", this.seriesItems);
    }
  }

  /**
   * Remove a question from the series
   */
  removeQuestion(questionId: number): void {
    // Remove from seriesItems array
    this.seriesItems = this.seriesItems.filter(
      (item) => item.questionId !== questionId
    );

    // Update total count
    this.totalSeriesItems = this.seriesItems.length;

    // Remove from service if a series exists
    if (this.currentSeries) {
      this.questionSeriesService.removeQuestionFromCurrentSeries(questionId);
    }
  }

  /**
   * Delete all questions from the series
   */
  deleteAllQuestions(): void {
    // Show confirmation dialog
    const confirmDelete = window.confirm(
      "Are you sure you want to delete all questions from this series?"
    );

    if (!confirmDelete) {
      return;
    }

    // Store the original count for the notification
    const originalCount = this.seriesItems.length;

    if (originalCount === 0) {
      this.showNotification("questionSeries.info.noQuestionsToDelete");
      return;
    }

    // Clear the seriesItems array
    this.seriesItems = [];

    // Update total count
    this.totalSeriesItems = 0;

    // Remove all questions from service if a series exists
    if (this.currentSeries) {
      // Since there's no bulk remove method, we'd need to update the series with an empty questions array
      // This would typically be handled by the service
      if (this.currentSeries.questions === undefined) {
        this.currentSeries.questions = [];
        console.warn(
          "Series questions was undefined in deleteAllQuestions, initialized to empty array"
        );
      } else {
        this.currentSeries.questions = [];
      }

      // Update the series in the service
      this.questionSeriesService
        .updateQuestionSeries(this.currentSeries.id!, this.currentSeries)
        .then(() => {
          this.showNotification(
            "questionSeries.success.deletedAllQuestions",
            "",
            3000,
            { count: originalCount }
          );
        })
        .catch((error) => {
          console.error(
            "Error updating series after deleting all questions:",
            error
          );
          this.showNotification("questionSeries.errors.errorUpdatingSeries");
        });
    } else {
      this.showNotification(
        "questionSeries.success.deletedAllQuestions",
        "",
        3000,
        { count: originalCount }
      );
    }
  }

  /**
   * Get questions for the selected chapter
   */
  get chapterQuestions(): Question[] {
    if (!this.selectedChapter || !this.selectedChapter.id) return [];
    return this.questions[this.selectedChapter.id] || [];
  }

  /**
   * Sort questions in the series by chapter name
   */
  sortQuestionsByChapter(): void {
    if (this.seriesItems.length <= 1) {
      this.showNotification("questionSeries.info.noQuestionsToSort");
      return;
    }

    // Sort the seriesItems array by chapterName
    this.seriesItems.sort((a, b) => {
      // First sort by chapterName, handling undefined values
      const aChapter = a.chapterName || "";
      const bChapter = b.chapterName || "";
      const chapterComparison = aChapter.localeCompare(bChapter);

      // If chapters are the same, sort by title
      if (chapterComparison === 0) {
        return a.title.localeCompare(b.title);
      }

      return chapterComparison;
    });

    this.showNotification("questionSeries.success.questionsSortedByChapter");
  }

  /**
   * Randomize the order of questions in the series
   */
  randomizeQuestions(): void {
    if (this.seriesItems.length <= 1) {
      this.showNotification("questionSeries.info.noQuestionsToRandomize");
      return;
    }

    // Fisher-Yates shuffle algorithm
    for (let i = this.seriesItems.length - 1; i > 0; i--) {
      // Generate a random index between 0 and i
      const j = Math.floor(Math.random() * (i + 1));
      // Swap elements at indices i and j
      [this.seriesItems[i], this.seriesItems[j]] = [
        this.seriesItems[j],
        this.seriesItems[i],
      ];
    }

    this.showNotification("questionSeries.success.questionsRandomized");
  }

  /**
   * Remove duplicate questions from the series
   */
  removeDuplicateQuestions(): void {
    const initialCount = this.seriesItems.length;

    if (initialCount <= 1) {
      this.showNotification("questionSeries.info.noDuplicatesToRemove");
      return;
    }

    // Create a map to track seen question IDs
    const seenQuestionIds = new Map<number, boolean>();
    const uniqueItems: QuestionSeriesItem[] = [];

    // Filter out duplicates
    this.seriesItems.forEach((item) => {
      if (!seenQuestionIds.has(item.questionId)) {
        seenQuestionIds.set(item.questionId, true);
        uniqueItems.push(item);
      }
    });

    // Update the seriesItems array
    this.seriesItems = uniqueItems;
    this.totalSeriesItems = this.seriesItems.length;

    const removedCount = initialCount - this.seriesItems.length;

    if (removedCount > 0) {
      this.showNotification(
        "questionSeries.success.removedDuplicates",
        "",
        3000,
        { count: removedCount }
      );

      // Update the service if a series exists
      if (this.currentSeries) {
        // The service might need to be updated with the new list
        // For now, we'll just ensure the UI is updated
      }
    } else {
      this.showNotification("questionSeries.info.noDuplicatesFound");
    }
  }

  /**
   * Open a dialog with statistics about the series
   */
  openSeriesStatistics(): void {
    if (this.seriesItems.length === 0) {
      this.showNotification("questionSeries.info.noQuestionsToAnalyze");
      return;
    }

    // Calculate statistics
    const stats = this.calculateSeriesStatistics();

    // Open dialog with statistics
    this.dialog.open(SeriesStatisticsDialogComponent, {
      width: "500px",
      data: stats,
    });
  }

  /**
   * Calculate statistics for the current series
   */
  private calculateSeriesStatistics(): any {
    // Count questions by chapter
    const chapterCounts = new Map<string, number>();
    this.seriesItems.forEach((item) => {
      // Use a default value for undefined chapterName
      const chapterName = item.chapterName || "Unknown Chapter";
      const count = chapterCounts.get(chapterName) || 0;
      chapterCounts.set(chapterName, count + 1);
    });

    // Convert to array for easier display
    const chapterStats = Array.from(chapterCounts.entries()).map(
      ([name, count]) => ({
        name,
        count,
        percentage: Math.round((count / this.seriesItems.length) * 100),
      })
    );

    // Sort by count (descending)
    chapterStats.sort((a, b) => b.count - a.count);

    // Calculate additional statistics
    const avgQuestionsPerChapter =
      this.seriesItems.length / chapterStats.length;
    const mostRepresentedChapter =
      chapterStats.length > 0 ? chapterStats[0] : null;
    const leastRepresentedChapter =
      chapterStats.length > 0 ? chapterStats[chapterStats.length - 1] : null;

    // Calculate difficulty distribution (placeholder - in a real app, you'd get this from question data)
    const difficultyDistribution = {
      easy: Math.round(Math.random() * 40),
      medium: Math.round(Math.random() * 40),
      hard: Math.round(Math.random() * 40),
    };

    // Normalize to 100%
    const total =
      difficultyDistribution.easy +
      difficultyDistribution.medium +
      difficultyDistribution.hard;
    if (total > 0) {
      difficultyDistribution.easy = Math.round(
        (difficultyDistribution.easy / total) * 100
      );
      difficultyDistribution.medium = Math.round(
        (difficultyDistribution.medium / total) * 100
      );
      difficultyDistribution.hard = Math.round(
        (difficultyDistribution.hard / total) * 100
      );

      // Ensure they sum to 100%
      const sum =
        difficultyDistribution.easy +
        difficultyDistribution.medium +
        difficultyDistribution.hard;
      if (sum !== 100) {
        difficultyDistribution.medium += 100 - sum;
      }
    }

    return {
      totalQuestions: this.seriesItems.length,
      chapterStats,
      uniqueChapters: chapterStats.length,
      avgQuestionsPerChapter: parseFloat(avgQuestionsPerChapter.toFixed(1)),
      mostRepresentedChapter,
      leastRepresentedChapter,
      difficultyDistribution,
      estimatedTimeMinutes: Math.round(this.seriesItems.length * 1.5), // Assuming 1.5 minutes per question
    };
  }

  /**
   * Convert questions to series items for drag and drop
   */
  get chapterQuestionsAsItems(): QuestionSeriesItem[] {
    return this.chapterQuestions.map((q) => {
      // Handle both API formats (title/statement and titre/contenu)
      const title = (q as any).titre || q.title;
      const statement = (q as any).contenu || q.statement;

      return {
        questionId: q.id!,
        title: title,
        statement: statement,
        chapterId: q.chapter_id,
        chapterName: q.chapter?.name,
      };
    });
  }

  /**
   * Open question details dialog
   */
  openQuestionDetails(question: QuestionSeriesItem): void {
    this.dialog.open(QuestionDetailsDialogComponent, {
      width: "500px",
      data: question,
    });
  }

  /**
   * Add random questions from a chapter to the series
   * @param chapter The chapter to add questions from
   */
  addRandomQuestions(chapter: Chapter): void {
    if (!chapter.id) {
      this.showNotification("questionSeries.errors.invalidChapter");
      return;
    }

    // Load questions for the chapter if not already loaded
    if (!this.questions[chapter.id!]) {
      this.loadingQuestions = true;
      this.questionError = "";

      this.questionService
        .getQuestionsByChapterId(chapter.id!)
        .then((questions) => {
          this.questions[chapter.id!] = questions;
          this.loadingQuestions = false;
          this.openRandomQuestionsDialog(chapter);
        })
        .catch((error) => {
          this.questionError = this.translationService.translate(
            "questionSeries.errors.failedToLoadQuestions"
          );
          this.loadingQuestions = false;
          console.error("Error loading questions:", error);
        });
    } else {
      this.openRandomQuestionsDialog(chapter);
    }
  }

  /**
   * Open dialog to select number of random questions
   * @param chapter The chapter to add questions from
   */
  private openRandomQuestionsDialog(chapter: Chapter): void {
    const chapterQuestions = this.questions[chapter.id!];

    if (!chapterQuestions || chapterQuestions.length === 0) {
      this.showNotification("questionSeries.info.noQuestionsForChapter");
      return;
    }

    // Filter out questions that are already in the series
    const availableQuestions = chapterQuestions.filter(
      (q) => !this.seriesItems.some((item) => item.questionId === q.id)
    );

    if (availableQuestions.length === 0) {
      this.showNotification("questionSeries.info.allQuestionsAlreadyInSeries");
      return;
    }

    // Open dialog to select number of questions
    const dialogRef = this.dialog.open(RandomQuestionsDialogComponent, {
      width: "400px",
      data: {
        maxCount: availableQuestions.length,
        chapterTitle: chapter.title,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && typeof result === "number") {
        this.processRandomQuestions(chapter, result);
      }
    });
  }

  /**
   * Process random questions selection and addition to series
   * @param chapter The chapter to add questions from
   * @param count The number of questions to add
   */
  private processRandomQuestions(chapter: Chapter, count: number): void {
    // Questions should already be loaded by the time we get here
    this.selectAndAddRandomQuestions(chapter, count);
  }

  /**
   * Select and add random questions to the series
   * @param chapter The chapter to add questions from
   * @param count The number of questions to add
   */
  private selectAndAddRandomQuestions(chapter: Chapter, count: number): void {
    const chapterQuestions = this.questions[chapter.id!];

    if (!chapterQuestions || chapterQuestions.length === 0) {
      this.showNotification("questionSeries.info.noQuestionsForChapter");
      return;
    }

    // Filter out questions that are already in the series
    const availableQuestions = chapterQuestions.filter(
      (q) => !this.seriesItems.some((item) => item.questionId === q.id)
    );

    if (availableQuestions.length === 0) {
      this.showNotification("questionSeries.info.allQuestionsAlreadyInSeries");
      return;
    }

    // Determine how many questions to add (minimum of available questions and requested count)
    const questionsToAdd = Math.min(availableQuestions.length, count);

    // Randomly select questions
    const selectedQuestions: Question[] = [];
    const availableIndices = Array.from(
      { length: availableQuestions.length },
      (_, i) => i
    );

    for (let i = 0; i < questionsToAdd; i++) {
      // Select a random index from the available indices
      const randomIndex = Math.floor(Math.random() * availableIndices.length);
      const questionIndex = availableIndices[randomIndex];

      // Remove the selected index from available indices
      availableIndices.splice(randomIndex, 1);

      // Add the question to selected questions
      selectedQuestions.push(availableQuestions[questionIndex]);
    }

    // Convert selected questions to series items and add them to the series
    const newSeriesItems = selectedQuestions.map((q) => {
      // Handle both API formats (title/statement and titre/contenu)
      const title = (q as any).titre || q.title;
      const statement = (q as any).contenu || q.statement;

      return {
        questionId: q.id!,
        title: title,
        statement: statement,
        chapterId: q.chapter_id,
        chapterName: chapter.title,
      };
    });

    // Add to UI - add to the seriesItems array
    this.seriesItems = [...this.seriesItems, ...newSeriesItems];

    // Update total count
    this.totalSeriesItems = this.seriesItems.length;

    // Add to service if a series exists
    if (this.currentSeries) {
      newSeriesItems.forEach((item) => {
        this.questionSeriesService.addQuestionToCurrentSeries(item);
      });
    }

    this.showNotification(
      "questionSeries.success.addedRandomQuestions",
      "",
      3000,
      { count: questionsToAdd, chapter: chapter.title }
    );
  }
}
