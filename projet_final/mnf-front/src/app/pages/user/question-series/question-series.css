.question-series-container {
  display: flex;
  height: calc(100vh - 64px); /* Adjust based on your header height */
  width: 100%;
  overflow: hidden;
}

/* Panel styles */
.left-panel, .right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  box-sizing: border-box;
}

.left-panel {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  max-width: 50%;
}

.panel-header {
  margin-bottom: 16px;
}

.panel-header h2 {
  margin: 0;
  font-size: 24px;
  color: var(--primary-color);
}

/* Chapters container */
.chapters-container {
  flex: 1;
  overflow-y: auto;
}

/* Questions container */
.questions-container {
  margin-top: 8px;
  margin-bottom: 8px;
}

/* Questions list */
.questions-list {
  min-height: 100px;
  border-radius: 4px;
  overflow: hidden;
}

/* Question item */
.question-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: grab;
  user-select: none;
}

.question-content {
  flex: 1;
  overflow: hidden;
}

.question-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.question-statement {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  overflow: hidden;
  max-height: 60px;
}

.question-actions {
  display: flex;
  align-items: center;
}

/* Series form */
.series-form {
  margin-bottom: 16px;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

/* Series items container */
.series-items-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-top: 16px;
}

.series-items-container h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: var(--primary-color);
}

/* Series header with actions */
.series-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.series-header h3 {
  margin: 0;
}

.series-actions {
  display: flex;
  gap: 8px;
}

/* Series list */
.series-list {
  flex: 1;
  min-height: 200px;
  max-height: 400px;
  border: 1px dashed rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 8px;
  overflow-y: auto;
}

/* Series item */
.series-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: grab;
  user-select: none;
}

.series-item-content {
  flex: 1;
  overflow: hidden;
}

.series-item-chapter {
  font-size: 12px;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.series-item-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.series-item-statement {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  overflow: hidden;
  max-height: 60px;
}

.series-item-actions {
  display: flex;
  align-items: center;
}

/* Loading container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.loading-container span {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.6);
}

/* Error message */
.error-message {
  color: var(--warn-color);
  padding: 16px;
  text-align: center;
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: 4px;
}

/* Empty message */
.empty-message {
  padding: 16px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

/* Drag & Drop styles */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
  z-index: 1000 !important;
  opacity: 0.8;
  background-color: white;
  pointer-events: none;
}

.drag-preview {
  padding: 12px;
  border: 2px solid #3f51b5;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
  z-index: 1000 !important;
  opacity: 0.9;
  pointer-events: none;
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background-color: #e0e0e0;
  border: 1px dashed #ccc;
  border-radius: 4px;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.questions-list.cdk-drop-list-dragging .question-item:not(.cdk-drag-placeholder),
.series-list.cdk-drop-list-dragging .series-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Highlight drop zones when dragging */
.cdk-drop-list-dragging {
  background-color: rgba(63, 81, 181, 0.05);
  border: 2px dashed rgba(63, 81, 181, 0.3) !important;
}

/* Add some padding to empty drop lists to make them more visible */
.questions-list:empty, .series-list:empty {
  min-height: 100px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.02);
  border: 1px dashed rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

/* Bottom create button */
.bottom-create-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px;
  padding: 8px;
  position: sticky;
  bottom: 16px;
  right: 0;
  width: 100%;
}

.requirement-message {
  margin-top: 8px;
  color: var(--warn-color);
  font-size: 14px;
  text-align: center;
}

/* Chapter actions */
.chapter-actions {
  display: flex;
  justify-content: flex-end;
  margin-left: auto;
}

/* Pagination */
mat-paginator {
  margin-top: 16px;
  background-color: transparent;
}

/* Responsive styles */
@media (max-width: 768px) {
  .question-series-container {
    flex-direction: column;
    height: auto;
  }

  .left-panel, .right-panel {
    max-width: 100%;
    height: 50vh;
  }

  .left-panel {
    border-right: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  }
}
