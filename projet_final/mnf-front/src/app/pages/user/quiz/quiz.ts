import { Component, OnInit, signal } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { Reponse } from "../../../components/user/quiz/reponse/reponse";
import { AnswerService } from "../../../services/admin/answer.service";
import { QuizService } from "../../../services/user/quiz.service";
import { Answer } from "../../../models/admin/Answer";
import { Question } from "../../../models/admin/Question";
import { QuizQuestion } from "../../../models/user/Quiz";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { QuestionService } from "../../../services/admin/question.service";

@Component({
  selector: "app-quiz",
  imports: [Reponse, CommonModule, MatButtonModule, MatIconModule],
  templateUrl: "./quiz.html",
  styleUrl: "./quiz.css",
})
export class QuizPage implements OnInit {
  // Quiz data
  quizId: number | null = null;
  numberOfQuestions: number = 5;
  quizQuestions: QuizQuestion[] = [];
  currentQuestionIndex: number = 0;
  loading: boolean = true;
  error: string | null = null;

  // Quiz results tracking
  correctAnswers: number = 0;
  incorrectAnswers: number = 0;
  questionsAnswered: number = 0;

  // UI state
  showPercentage = signal(false);

  // Current question and answers
  question: Question = {
    statement: "",
    title: "",
    chapter_id: 0,
  };

  reponses: Answer[] = [];

  selectedAnswers = signal<Answer[]>([]);

  constructor(
    public answerService: AnswerService,
    private quizService: QuizService,
    private questionService: QuestionService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.quizId = params["chapterId"] ? Number(params["chapterId"]) : null;
      this.numberOfQuestions = params["numberOfQuestions"]
        ? Number(params["numberOfQuestions"])
        : 5;

      if (this.quizId) {
        this.loadQuizQuestions(this.quizId);
      } else {
        this.error = "No quiz selected";
        this.loading = false;
      }
    });
  }

  loadQuizQuestions(quizId: number): void {
    this.loading = true;
    this.error = null;

    this.quizService
      .getQuizQuestions(quizId)
      .then((questions) => {
        if (questions && questions.length > 0) {
          // Sort questions by position
          this.quizQuestions = questions.sort(
            (a, b) => a.position - b.position
          );

          // Limit to the number of questions specified
          if (
            this.numberOfQuestions > 0 &&
            this.quizQuestions.length > this.numberOfQuestions
          ) {
            this.quizQuestions = this.quizQuestions.slice(
              0,
              this.numberOfQuestions
            );
          }

          // Load the first question
          this.loadCurrentQuestion();
        } else {
          this.error = "No questions found for this quiz";
          this.loading = false;
        }
      })
      .catch((error) => {
        console.error("Error loading quiz questions:", error);
        this.error = "Failed to load quiz questions";
        this.loading = false;
      });
  }

  loadCurrentQuestion(): void {
    if (
      this.quizQuestions.length === 0 ||
      this.currentQuestionIndex >= this.quizQuestions.length
    ) {
      this.error = "No more questions";
      this.loading = false;
      return;
    }

    const currentQuizQuestion = this.quizQuestions[this.currentQuestionIndex];

    // Load question for the current question
    this.questionService
      .getQuestionById(currentQuizQuestion.id || 0)
      .then((questionCurrent) => {
        if (questionCurrent) {
          this.question = {
            id: questionCurrent.id,
            title: questionCurrent.title,
            statement: questionCurrent.statement,
            chapter_id: questionCurrent.chapter_id || 0,
          };
          this.loading = false;
        } else {
          this.error = "No answers found for this question";
          this.loading = false;
        }
      })
      .catch((error) => {
        console.error("Error loading answers:", error);
        this.error = "Failed to load answers";
        this.loading = false;
      });

    // Reset answers and validation state
    this.reponses = [];
    this.selectedAnswers.set([]);
    this.isValidated.set(false);

    // Load answers for the current question
    this.answerService
      .getAnswersByQuestionId(this.question.id || 0)
      .then((answers) => {
        if (answers && answers.length > 0) {
          this.reponses = answers;
          this.loading = false;
        } else {
          this.error = "No answers found for this question";
          this.loading = false;
        }
      })
      .catch((error) => {
        console.error("Error loading answers:", error);
        this.error = "Failed to load answers";
        this.loading = false;
      });
  }

  toggleAnswer(answer: Answer) {
    if (this.isValidated()) return;

    const current = this.selectedAnswers();
    const index = current.indexOf(answer);
    if (index > -1) {
      // Déjà sélectionné → on le retire
      this.selectedAnswers.set([
        ...current.slice(0, index),
        ...current.slice(index + 1),
      ]);
    } else {
      // Pas encore sélectionné → on l’ajoute
      this.selectedAnswers.set([...current, answer]);
    }
  }

  isSelected(answer: Answer): boolean {
    return this.selectedAnswers().includes(answer);
  }

  isValidated = signal(false);

  validate() {
    this.isValidated.set(true);
    this.updateQuizProgress();
  }

  updateQuizProgress() {
    // Count correct and incorrect answers for the current question
    let correctCount = 0;
    let incorrectCount = 0;

    // Check each selected answer
    this.selectedAnswers().forEach((answer) => {
      if (answer.valid_answer) {
        correctCount++;
      } else {
        incorrectCount++;
      }
    });

    // Check for missed correct answers
    this.reponses.forEach((answer) => {
      if (answer.valid_answer && !this.isSelected(answer)) {
        // Missed a correct answer
        incorrectCount++;
      }
    });

    // Update overall counts
    if (correctCount > 0 && incorrectCount === 0) {
      // All correct answers selected and no incorrect ones
      this.correctAnswers++;
    } else {
      this.incorrectAnswers++;
    }

    this.questionsAnswered++;
  }

  calculateSuccessPercentage(): number {
    if (this.questionsAnswered === 0) return 0;
    return Math.round((this.correctAnswers / this.questionsAnswered) * 100);
  }

  togglePercentageDisplay() {
    this.showPercentage.update((value) => !value);
  }

  getAnswerClass(answer: Answer): string {
    if (!this.isValidated()) return this.isSelected(answer) ? "selected" : "";

    const isSelected = this.isSelected(answer);
    const isValid = answer.valid_answer;

    if (isSelected && isValid) return "correct";
    if (isSelected && !isValid) return "incorrect";
    if (!isSelected && isValid) return "missed";

    return "validated";
  }

  suivant() {
    // Move to the next question
    this.currentQuestionIndex++;

    // Check if we've reached the end of the quiz
    if (this.currentQuestionIndex >= this.quizQuestions.length) {
      // Quiz completed - navigate to results page
      this.goToResultsPage();
      return;
    }

    // Load the next question
    this.loading = true;
    this.loadCurrentQuestion();
  }

  goToResultsPage() {
    // Navigate to the results page with quiz results data
    this.router.navigate(["/quiz-results"], {
      queryParams: {
        correctAnswers: this.correctAnswers,
        totalQuestions: this.questionsAnswered,
        quizId: this.quizId,
      },
    });
  }

  goToLobby() {
    this.router.navigate(["/lobby"]);
  }
}
