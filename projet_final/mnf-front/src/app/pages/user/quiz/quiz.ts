import { Component, signal } from "@angular/core";
import { <PERSON>onse } from "../../../components/user/quiz/reponse/reponse";
import { AnswerService } from "../../../services/admin/answer.service";
import { Answer } from "../../../models/admin/Answer";
import { Question } from "../../../models/admin/Question";

@Component({
  selector: "app-quiz",
  imports: [Reponse],
  templateUrl: "./quiz.html",
  styleUrl: "./quiz.css",
})
export class QuizPage {
  constructor(public answerService: AnswerService) {}

  // ngOnInit() {
  //   this.answerService.getAnswers().then((chaps) => (this.chapitres = chaps));
  // }
  question: Question = {
    statement: `<p>What is the output if this class is run with java Indexing cars carts?&nbsp;</p><p>&nbsp;</p><pre><code class="language-java">public class Indexing { 
	public static void main(String... books) { 
		StringBuilder sb = new StringBuilder(); 
		for (String book : books) 
			sb.insert(sb.indexOf("c"), book); 
		System.out.println(sb); 
	} 
}</code></pre>`,
    title: "",
    chapter_id: 0,
  };

  reponses: Answer[] = [
    {
      text: "cars",
      valid_answer: false,
      label: "",
      question_id: 0,
    },
    {
      text: "cars carts",
      valid_answer: false,
      label: "",
      question_id: 0,
    },
    {
      text: "ccars arts",
      valid_answer: false,
      label: "",
      question_id: 0,
    },
    {
      text: "The code does not compile",
      valid_answer: false,
      label: "",
      question_id: 0,
    },
    {
      text: "The code compiles but throws an exception at runtime",
      valid_answer: true,
      label: "",
      question_id: 0,
    },
  ];

  selectedAnswers = signal<Answer[]>([]);

  toggleAnswer(answer: Answer) {
    if (this.isValidated()) return;

    const current = this.selectedAnswers();
    const index = current.indexOf(answer);
    if (index > -1) {
      // Déjà sélectionné → on le retire
      this.selectedAnswers.set([
        ...current.slice(0, index),
        ...current.slice(index + 1),
      ]);
    } else {
      // Pas encore sélectionné → on l’ajoute
      this.selectedAnswers.set([...current, answer]);
    }
  }

  isSelected(answer: Answer): boolean {
    return this.selectedAnswers().includes(answer);
  }

  isValidated = signal(false);

  validate() {
    this.isValidated.set(true);
  }

  getAnswerClass(answer: Answer): string {
    if (!this.isValidated()) return this.isSelected(answer) ? "selected" : "";

    const isSelected = this.isSelected(answer);
    const isValid = answer.valid_answer;

    if (isSelected && isValid) return "correct";
    if (isSelected && !isValid) return "incorrect";
    if (!isSelected && isValid) return "missed";

    return "validated";
  }

  suivant() {}
}
