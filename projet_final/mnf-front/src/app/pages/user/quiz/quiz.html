<div class="quiz-container">
  <div class="text-question" [innerHTML]="question.statement"></div>
  <div class="reponse-container">
    @for (reponse of reponses; track $index) {
    <app-reponse
      [reponse]="reponse"
      (click)="!isValidated() && toggleAnswer(reponse)"
      [selected]="isSelected(reponse)"
      [class]="getAnswerClass(reponse)"
    ></app-reponse>
    }
  </div>
  @if(!isValidated()){
  <button class="valider-button" (click)="validate()">Valider</button>
  } @if(isValidated()){
  <button class="valider-button" (click)="suivant()">Suivant</button>
  }
</div>
