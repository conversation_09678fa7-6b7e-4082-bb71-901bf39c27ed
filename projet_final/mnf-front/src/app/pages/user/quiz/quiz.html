<div class="quiz-container">
  <!-- Loading indicator -->
  @if(loading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement de la question...</p>
    </div>
  }

  <!-- Error message -->
  @if(error && !loading) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button class="valider-button" (click)="goToLobby()">Retour au Lobby</button>
    </div>
  }

  <!-- Quiz content - only show when not loading and no error -->
  @if(!loading && !error) {
    <div class="text-question" [innerHTML]="question.statement"></div>
    <div class="reponse-container">
      @for (reponse of reponses; track $index) {
      <app-reponse
        [reponse]="reponse"
        (click)="!isValidated() && toggleAnswer(reponse)"
        [selected]="isSelected(reponse)"
        [class]="getAnswerClass(reponse)"
      ></app-reponse>
      }
    </div>
    @if(!isValidated()){
    <button class="valider-button" (click)="validate()">Valider</button>
    } @if(isValidated()){
    <button class="valider-button" (click)="suivant()">Suivant</button>
    }

    <!-- Success percentage display -->
    @if(showPercentage()) {
      <div class="success-percentage">
        <p>Pourcentage de réussite: {{ calculateSuccessPercentage() }}%</p>
      </div>
    }

    <!-- Toggle button for success percentage -->
    <button mat-mini-fab class="toggle-percentage-button" (click)="togglePercentageDisplay()">
      <mat-icon>{{ showPercentage() ? 'visibility_off' : 'visibility' }}</mat-icon>
    </button>
  }
</div>
