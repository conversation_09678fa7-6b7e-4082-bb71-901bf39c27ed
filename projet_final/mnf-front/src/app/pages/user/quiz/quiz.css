.quiz-container {
  display: flex;
  margin-top: 50px;
  flex-direction: column;
  align-items: center;
  gap: 60px;
  justify-content: center;
  height: calc(100vh - 120px);
  padding: 0px 10vw;
}

.valider-button {
  width: 15vw;
  min-width: fit-content;
  font-size: x-large;
  padding: 15px;
  border-radius: 25px;
  margin-bottom: 30px;
  box-shadow: 2px 2px 4px #5f5f5f6b;
  align-self: flex-end;
}

.valider-button:hover {
  background-color: var(--secondary-color);
  box-shadow: 3px 3px 4px #5f5f5f6b;
  transition: all 0.2s;
}

.reponse-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  width: 100%;
  height: max-content;
}

.text-question {
  display: flex;
  margin-top: 20px;
  flex-direction: column;
  box-shadow: 0px 0px 5px #91454585;
  background-color: #ffd4d485;
  border-radius: 20px;
  width: 100%;
  align-items: center;
  justify-content: center;
  min-height: 20vh;
  font-size: 18px;
  color: var(--text-color);
}

/* Loading styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 50vh;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 30px;
  background-color: #ffebee;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  margin: 20px 0;
}

.error-message {
  color: #d32f2f;
  font-size: 18px;
  margin-bottom: 20px;
  text-align: center;
}

/* Success percentage styles */
.success-percentage {
  position: fixed;
  bottom: 80px;
  right: 20px;
  background-color: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 100;
  animation: fadeIn 0.3s ease-in-out;
}

.success-percentage p {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

/* Toggle button styles */
.toggle-percentage-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
  background-color: var(--primary-color);
  color: white;
}

.toggle-percentage-button:hover {
  background-color: var(--secondary-color);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
