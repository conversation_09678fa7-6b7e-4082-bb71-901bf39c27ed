.quiz-container {
  display: flex;
  margin-top: 50px;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: calc(100vh - 120px);
  padding: 0px 10vw;
}

.valider-button {
  width: 15vw;
  min-width: fit-content;
  font-size: x-large;
  padding: 15px;
  border-radius: 25px;
  margin-bottom: 30px;
  box-shadow: 2px 2px 4px #5f5f5f6b;
  align-self: flex-end;
}

.valider-button:hover {
  background-color: var(--secondary-color);
  box-shadow: 3px 3px 4px #5f5f5f6b;
  transition: all 0.2s;
}

.reponse-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  width: 100%;
  height: max-content;
}

.text-question {
  display: flex;
  margin-top: 20px;
  flex-direction: column;
  box-shadow: 0px 0px 5px #91454585;
  background-color: #ffd4d485;
  border-radius: 20px;
  width: 100%;
  align-items: center;
  justify-content: center;
  min-height: 20vh;
  font-size: 18px;
  color: var(--text-color);
}
