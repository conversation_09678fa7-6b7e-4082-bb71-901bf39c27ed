.profile-container {
  background-color: #fcf9f8;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.content-wrapper {
  max-width: 800px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
}

.profile-title {
  font-size: 2rem;
  color: #2d2d2d;
  margin-bottom: 1.5rem;
  font-weight: 500;
  text-align: center;
  border-bottom: 2px solid #c94d61;
  padding-bottom: 0.5rem;
}

.profile-content {
  margin-top: 2rem;
}

.profile-info {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.profile-avatar {
  background-color: #f0f0f0;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2rem;
}

.avatar-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #c94d61;
}

.profile-details {
  flex: 1;
}

.profile-name {
  font-size: 1.5rem;
  color: #2d2d2d;
  margin-bottom: 0.5rem;
}

.profile-email {
  color: #666;
  margin-bottom: 0.5rem;
}

.profile-id {
  color: #999;
  font-size: 0.875rem;
}

.profile-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.edit-button {
  background-color: #c94d61;
  color: white;
}

.logout-button {
  background-color: #f44336;
  color: white;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
}

.form-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 1.2rem;
  color: #c94d61;
  margin-bottom: 1rem;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-field {
  width: 100%;
}

.full-width {
  width: 100%;
}

.account-info {
  padding: 0.5rem 0;
}

.account-detail, .profile-detail {
  display: flex;
  margin-bottom: 0.5rem;
  align-items: center;
}

.detail-label {
  font-weight: 500;
  color: #666;
  width: 120px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.save-button {
  background-color: #c94d61;
  color: white;
}

.cancel-button {
  background-color: #f0f0f0;
  color: #666;
}

.profile-error {
  text-align: center;
  color: #666;
  margin: 2rem 0;
}

.home-button {
  background-color: #c94d61;
  color: white;
  margin-top: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .profile-info {
    flex-direction: column;
    text-align: center;
  }

  .profile-avatar {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .profile-actions {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .profile-detail, .account-detail {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .detail-label {
    width: 100%;
    margin-bottom: 0.25rem;
  }

  .profile-details {
    text-align: left;
  }

  .profile-section {
    margin-top: 1.5rem;
  }
}
