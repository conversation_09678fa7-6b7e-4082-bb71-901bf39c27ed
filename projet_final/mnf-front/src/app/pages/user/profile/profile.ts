import { Component, OnInit } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { TranslatePipe } from '../../../pipes/translate.pipe';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FavoritesService } from '../../../services/favorites/favorites.service';
import { TranslationService } from '../../../services/translation.service';

@Component({
  selector: 'app-profile',
  imports: [
    MatToolbarModule,
    TranslatePipe,
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatSnackBarModule
  ],
  templateUrl: './profile.html',
  standalone: true,
  styleUrl: './profile.css'
})
export class ProfilePage implements OnInit {
  user: any = null;
  isEditing = false;
  newPassword: string = '';
  confirmPassword: string = '';

  // Favorite status properties
  showFavoriteStatus = false;
  isFavorite = false;
  profileUsername: string = '';
  currentUser: any = null;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private authService: AuthService,
    private favoritesService: FavoritesService,
    private translationService: TranslationService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Get the current user from the auth service
    this.currentUser = this.authService.currentUserValue;

    // If user is not authenticated, redirect to auth page
    if (!this.currentUser) {
      this.router.navigate(['/auth']);
      return;
    }

    // Check if we're viewing our own profile or someone else's
    this.route.params.subscribe(params => {
      if (params['username']) {
        this.profileUsername = params['username'];

        // If viewing someone else's profile, show favorite status
        if (this.profileUsername !== this.currentUser.username) {
          this.showFavoriteStatus = true;
          this.loadUserProfile(this.profileUsername);
          this.checkFavoriteStatus(this.profileUsername);
        } else {
          // If viewing our own profile, use the current user
          this.user = this.currentUser;
        }
      } else {
        // If no username parameter, we're viewing our own profile
        this.user = this.currentUser;
      }
    });
  }

  /**
   * Load a user profile by username
   */
  loadUserProfile(username: string): void {
    // Here you would typically call a service to get the user profile
    // For now, we'll just set a placeholder user
    this.user = {
      username: username,
      // Add other properties as needed
    };
  }

  /**
   * Check if a user is in favorites
   */
  checkFavoriteStatus(username: string): void {
    // Get the current user's favorites
    this.favoritesService.getCurrentUserFavorites()
      .then(favorites => {
        // Check if the profile user is in favorites
        // First try to match by email if available, then fall back to username
        if (this.user && this.user.email) {
          this.isFavorite = favorites.some(fav =>
            fav.favoriteUserEmail?.toLowerCase() === this.user.email.toLowerCase()
          );
        } else {
          this.isFavorite = favorites.some(fav =>
            fav.favoriteUserName?.toLowerCase() === username.toLowerCase()
          );
        }
      })
      .catch(error => {
        console.error('Error checking favorite status:', error);
        this.isFavorite = false;
      });
  }

  /**
   * Toggle edit mode
   */
  toggleEditMode(): void {
    this.isEditing = !this.isEditing;
    // Reset password fields when toggling edit mode
    this.newPassword = '';
    this.confirmPassword = '';
  }

  /**
   * Save profile changes
   */
  saveProfile(): void {
    // Check if passwords match if a new password is provided
    if (this.newPassword && this.newPassword !== this.confirmPassword) {
      console.error('profile.passwordMismatch');
      // Show error message with red background
      this.snackBar.open(
        this.translationService.translate('profile.passwordMismatch'),
        this.translationService.translate('profile.close'),
        {
          duration: 3000,
          panelClass: ['error-snackbar']
        }
      );
      return;
    }

    // Prepare user data for update
    const userData: any = {
      name: this.user.name,
      firstName: this.user.firstName,
      lastName: this.user.lastName,
      email: this.user.email,
      phone: this.user.phone
    };

    // Add password to update data if provided
    if (this.newPassword) {
      userData.password = this.newPassword;
    }

    // Call the AuthService to update the user profile
    this.authService.updateUser(userData)
      .then(updatedUser => {
        console.log('Profile updated successfully', updatedUser);
        this.user = updatedUser;
        this.isEditing = false;
        // Reset password fields after successful update
        this.newPassword = '';
        this.confirmPassword = '';

        // Show success message with green background
        this.snackBar.open(
          this.translationService.translate('profile.updateSuccess'),
          this.translationService.translate('profile.close'),
          {
            duration: 3000,
            panelClass: ['success-snackbar']
          }
        );
      })
      .catch(error => {
        console.error('Error updating profile', error);
        // Show error message with red background
        this.snackBar.open(
          this.translationService.translate('profile.updateError'),
          this.translationService.translate('profile.close'),
          {
            duration: 3000,
            panelClass: ['error-snackbar']
          }
        );
      });
  }

  /**
   * Navigate to home page
   */
  goToHome(): void {
    this.router.navigate(['/']);
  }

  /**
   * Logout the user
   */
  logout(): void {
    this.authService.logout();
    this.router.navigate(['/auth']);
  }

  /**
   * Add the current profile user to favorites
   */
  addToFavorites(): void {
    if (!this.user) {
      return;
    }

    // Use email if available, otherwise fall back to username
    if (this.user.email) {
      this.favoritesService.addFavoriteByEmail(this.user.email)
        .then(favorite => {
          this.isFavorite = true;
          this.snackBar.open(
            this.translationService.translate('favorites.addSuccess'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
        })
        .catch(error => {
          this.snackBar.open(
            this.translationService.translate('favorites.addError'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
          console.error('Error adding favorite by email:', error);
        });
    } else if (this.profileUsername) {
      // Fall back to username if email is not available
      this.favoritesService.addFavoriteByUsername(this.profileUsername)
        .then(favorite => {
          this.isFavorite = true;
          this.snackBar.open(
            this.translationService.translate('favorites.addSuccess'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
        })
        .catch(error => {
          this.snackBar.open(
            this.translationService.translate('favorites.addError'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
          console.error('Error adding favorite by username:', error);
        });
    }
  }

  /**
   * Remove the current profile user from favorites
   */
  removeFromFavorites(): void {
    if (!this.user) {
      return;
    }

    // Use email if available, otherwise fall back to username
    if (this.user.email) {
      this.favoritesService.removeFavoriteByEmail(this.user.email)
        .then(success => {
          if (success) {
            this.isFavorite = false;
            this.snackBar.open(
              this.translationService.translate('favorites.removeSuccess'),
              this.translationService.translate('favorites.close'),
              { duration: 3000 }
            );
          } else {
            this.snackBar.open(
              this.translationService.translate('favorites.removeError'),
              this.translationService.translate('favorites.close'),
              { duration: 3000 }
            );
          }
        })
        .catch(error => {
          this.snackBar.open(
            this.translationService.translate('favorites.removeError'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
          console.error('Error removing favorite by email:', error);
        });
    } else if (this.profileUsername) {
      // Fall back to username if email is not available
      this.favoritesService.removeFavoriteByUsername(this.profileUsername)
        .then(success => {
          if (success) {
            this.isFavorite = false;
            this.snackBar.open(
              this.translationService.translate('favorites.removeSuccess'),
              this.translationService.translate('favorites.close'),
              { duration: 3000 }
            );
          } else {
            this.snackBar.open(
              this.translationService.translate('favorites.removeError'),
              this.translationService.translate('favorites.close'),
              { duration: 3000 }
            );
          }
        })
        .catch(error => {
          this.snackBar.open(
            this.translationService.translate('favorites.removeError'),
            this.translationService.translate('favorites.close'),
            { duration: 3000 }
          );
          console.error('Error removing favorite by username:', error);
        });
    }
  }
}
