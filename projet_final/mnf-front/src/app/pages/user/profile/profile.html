<div class="profile-container">
  <div class="content-wrapper">
    <!-- Profile Card -->
    <div class="profile-card">
      <h1 class="profile-title">
        {{ 'profile.title' | translate }}
      </h1>

      @if (user) {
        <div class="profile-content">
          <!-- View Mode -->
          @if (!isEditing) {
            <div class="profile-info">
              <div class="profile-details">
                <div class="profile-section">
                  <h3 class="section-title">{{ 'profile.personalInfo' | translate }}</h3>
                  <p class="profile-detail">
                    <span class="detail-label">{{ 'profile.firstName' | translate }}:</span>
                    <span class="detail-value">{{ user.firstname || '-' }}</span>
                  </p>
                  <p class="profile-detail">
                    <span class="detail-label">{{ 'profile.lastName' | translate }}:</span>
                    <span class="detail-value">{{ user.lastname || '-' }}</span>
                  </p>
                </div>

                <div class="profile-section">
                  <h3 class="section-title">{{ 'profile.contactInfo' | translate }}</h3>
                  <p class="profile-detail">
                    <span class="detail-label">{{ 'profile.email' | translate }}:</span>
                    <span class="detail-value">{{ user.email }}</span>
                  </p>
                </div>
              </div>
            </div>
            <div class="profile-actions">
              <button mat-raised-button color="warn" (click)="logout()" class="logout-button">
                <mat-icon>exit_to_app</mat-icon>
                {{ 'profile.logout' | translate }}
              </button>
            </div>
          }

          <!-- Edit Mode -->
          @if (isEditing) {
            <div class="profile-form">
              <div class="form-section">
                <h3 class="section-title">{{ 'profile.personalInfo' | translate }}</h3>

                <div class="form-row">
                  <mat-form-field appearance="outline" class="form-field">
                    <mat-label>{{ 'profile.firstName' | translate }}</mat-label>
                    <input matInput [(ngModel)]="user.firstname" placeholder="Your first name">
                    <mat-icon matSuffix>person</mat-icon>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="form-field">
                    <mat-label>{{ 'profile.lastName' | translate }}</mat-label>
                    <input matInput [(ngModel)]="user.lastname" placeholder="Your last name">
                    <mat-icon matSuffix>person</mat-icon>
                  </mat-form-field>
                </div>

              </div>

              <div class="form-section">
                <h3 class="section-title">{{ 'profile.contactInfo' | translate }}</h3>

                <mat-form-field appearance="outline" class="form-field full-width">
                  <mat-label>{{ 'profile.email' | translate }}</mat-label>
                  <input matInput [(ngModel)]="user.email" placeholder="Your email" type="email">
                  <mat-icon matSuffix>email</mat-icon>
                </mat-form-field>
              </div>

              <div class="form-section">
                <h3 class="section-title">{{ 'profile.changePassword' | translate }}</h3>

                <mat-form-field appearance="outline" class="form-field full-width">
                  <mat-label>{{ 'profile.newPassword' | translate }}</mat-label>
                  <input matInput [(ngModel)]="newPassword" placeholder="New password" type="password">
                  <mat-icon matSuffix>lock</mat-icon>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field full-width">
                  <mat-label>{{ 'profile.confirmPassword' | translate }}</mat-label>
                  <input matInput [(ngModel)]="confirmPassword" placeholder="Confirm password" type="password">
                  <mat-icon matSuffix>lock</mat-icon>
                </mat-form-field>
              </div>

              <div class="form-actions">
                <button mat-raised-button color="primary" (click)="saveProfile()" class="save-button">
                  <mat-icon>save</mat-icon>
                  {{ 'profile.save' | translate }}
                </button>
                <button mat-raised-button (click)="toggleEditMode()" class="cancel-button">
                  <mat-icon>cancel</mat-icon>
                  {{ 'profile.cancel' | translate }}
                </button>
              </div>
            </div>
          }

          <!-- Favorite Status Section -->
          @if (!isEditing && showFavoriteStatus) {
            <div class="favorite-status-section">
              <div class="favorite-status">
                @if (isFavorite) {
                  <div class="is-favorite">
                    <mat-icon color="warn">favorite</mat-icon>
                    <span>{{ 'profile.inFavorites' | translate }}</span>
                    <button mat-raised-button color="warn" (click)="removeFromFavorites()" class="remove-favorite-button">
                      <mat-icon>delete</mat-icon>
                      {{ 'favorites.remove' | translate }}
                    </button>
                  </div>
                } @else {
                  <div class="not-favorite">
                    <mat-icon>favorite_border</mat-icon>
                    <span>{{ 'profile.notInFavorites' | translate }}</span>
                    <button mat-raised-button color="primary" (click)="addToFavorites()" class="add-favorite-button">
                      <mat-icon>favorite</mat-icon>
                      {{ 'favorites.add' | translate }}
                    </button>
                  </div>
                }
              </div>
            </div>
          }
        </div>
      } @else {
        <div class="profile-error">
          <p>{{ 'profile.notAuthenticated' | translate }}</p>
          <button mat-raised-button color="primary" (click)="goToHome()" class="home-button">
            <mat-icon>home</mat-icon>
            {{ 'profile.goToHome' | translate }}
          </button>
        </div>
      }
    </div>
  </div>
</div>
