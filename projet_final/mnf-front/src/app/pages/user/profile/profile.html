<div class="profile-container">
  <div class="content-wrapper">
    <!-- Profile Card -->
    <div class="profile-card">
      <h1 class="profile-title">
        {{ 'profile.title' | translate }}
      </h1>

      @if (user) {
        <div class="profile-content">
          <!-- View Mode -->
          @if (!isEditing) {
            <div class="profile-info">
              <div class="profile-avatar">
                <mat-icon class="avatar-icon">account_circle</mat-icon>
              </div>
              <div class="profile-details">
                <h2 class="profile-name">{{ user.name || 'User' }}</h2>
                <p class="profile-email">{{ user.email }}</p>
                <p class="profile-id">ID: {{ user.id }}</p>
              </div>
            </div>
            <div class="profile-actions">
              <button mat-raised-button color="primary" (click)="toggleEditMode()" class="edit-button">
                <mat-icon>edit</mat-icon>
                {{ 'profile.edit' | translate }}
              </button>
              <button mat-raised-button color="warn" (click)="logout()" class="logout-button">
                <mat-icon>exit_to_app</mat-icon>
                {{ 'profile.logout' | translate }}
              </button>
            </div>
          }

          <!-- Edit Mode -->
          @if (isEditing) {
            <div class="profile-form">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'profile.name' | translate }}</mat-label>
                <input matInput [(ngModel)]="user.name" placeholder="Your name">
                <mat-icon matSuffix>person</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>{{ 'profile.email' | translate }}</mat-label>
                <input matInput [(ngModel)]="user.email" placeholder="Your email" type="email">
                <mat-icon matSuffix>email</mat-icon>
              </mat-form-field>

              <div class="form-actions">
                <button mat-raised-button color="primary" (click)="saveProfile()" class="save-button">
                  <mat-icon>save</mat-icon>
                  {{ 'profile.save' | translate }}
                </button>
                <button mat-raised-button (click)="toggleEditMode()" class="cancel-button">
                  <mat-icon>cancel</mat-icon>
                  {{ 'profile.cancel' | translate }}
                </button>
              </div>
            </div>
          }
        </div>
      } @else {
        <div class="profile-error">
          <p>{{ 'profile.notAuthenticated' | translate }}</p>
          <button mat-raised-button color="primary" (click)="goToHome()" class="home-button">
            <mat-icon>home</mat-icon>
            {{ 'profile.goToHome' | translate }}
          </button>
        </div>
      }
    </div>
  </div>
</div>
