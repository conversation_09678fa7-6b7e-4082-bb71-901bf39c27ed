<div class="results-container">
  <mat-card class="results-card">
    <mat-card-header>
      <mat-card-title>Résultats du Quiz</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div class="percentage-container">
        <div class="percentage-circle" [ngClass]="getResultClass()">
          <span class="percentage-value">{{ successPercentage }}%</span>
        </div>
      </div>

      <div class="result-message">
        <p>{{ getResultMessage() }}</p>
      </div>

      <div class="stats-container">
        <div class="stat-item">
          <mat-icon class="correct-icon">check_circle</mat-icon>
          <span>{{ correctAnswers }} réponses correctes</span>
        </div>
        <div class="stat-item">
          <mat-icon class="total-icon">help_outline</mat-icon>
          <span>{{ totalQuestions }} questions au total</span>
        </div>
      </div>

      <mat-progress-bar
        mode="determinate"
        [value]="successPercentage"
        [ngClass]="getResultClass()">
      </mat-progress-bar>
    </mat-card-content>

    <mat-card-actions>
      <button mat-raised-button color="primary" (click)="retryQuiz()">
        <mat-icon>replay</mat-icon>
        Réessayer
      </button>
      <button mat-raised-button color="accent" (click)="goToLobby()">
        <mat-icon>home</mat-icon>
        Retour au Lobby
      </button>
    </mat-card-actions>
  </mat-card>
</div>
