import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatCardModule } from "@angular/material/card";
import { MatProgressBarModule } from "@angular/material/progress-bar";

@Component({
  selector: "app-quiz-results",
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressBarModule
  ],
  templateUrl: "./quiz-results.html",
  styleUrl: "./quiz-results.css",
})
export class QuizResultsPage implements OnInit {
  // Quiz results data
  correctAnswers: number = 0;
  totalQuestions: number = 0;
  successPercentage: number = 0;
  quizId: number | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.correctAnswers = params['correctAnswers'] ? Number(params['correctAnswers']) : 0;
      this.totalQuestions = params['totalQuestions'] ? Number(params['totalQuestions']) : 0;
      this.quizId = params['quizId'] ? Number(params['quizId']) : null;

      // Calculate success percentage
      this.calculateSuccessPercentage();
    });
  }

  calculateSuccessPercentage(): void {
    if (this.totalQuestions === 0) {
      this.successPercentage = 0;
    } else {
      this.successPercentage = Math.round((this.correctAnswers / this.totalQuestions) * 100);
    }
  }

  getResultMessage(): string {
    if (this.successPercentage >= 80) {
      return "Excellent ! Vous avez très bien réussi ce quiz !";
    } else if (this.successPercentage >= 60) {
      return "Bien joué ! Vous avez réussi ce quiz.";
    } else if (this.successPercentage >= 40) {
      return "Pas mal ! Continuez à vous entraîner.";
    } else {
      return "Vous pouvez faire mieux. Réessayez !";
    }
  }

  getResultClass(): string {
    if (this.successPercentage >= 80) {
      return "excellent";
    } else if (this.successPercentage >= 60) {
      return "good";
    } else if (this.successPercentage >= 40) {
      return "average";
    } else {
      return "poor";
    }
  }

  retryQuiz(): void {
    if (this.quizId) {
      this.router.navigate(['/quiz'], {
        queryParams: {
          chapterId: this.quizId,
          numberOfQuestions: this.totalQuestions
        }
      });
    } else {
      this.goToLobby();
    }
  }

  goToLobby(): void {
    this.router.navigate(['/lobby']);
  }
}
