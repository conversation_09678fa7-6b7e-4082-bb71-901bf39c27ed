.results-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.results-card {
  width: 100%;
  max-width: 600px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

mat-card-header {
  margin-bottom: 20px;
}

mat-card-title {
  font-size: 24px;
  color: var(--primary-color);
  text-align: center;
  width: 100%;
}

.percentage-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.percentage-circle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  font-size: 36px;
  font-weight: bold;
  color: white;
  transition: all 0.3s ease;
}

.percentage-circle.excellent {
  background-color: #4CAF50;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.percentage-circle.good {
  background-color: #8BC34A;
  box-shadow: 0 0 15px rgba(139, 195, 74, 0.5);
}

.percentage-circle.average {
  background-color: #FFC107;
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
}

.percentage-circle.poor {
  background-color: #F44336;
  box-shadow: 0 0 15px rgba(244, 67, 54, 0.5);
}

.result-message {
  text-align: center;
  margin: 20px 0;
  font-size: 18px;
  font-weight: 500;
}

.stats-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 20px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
}

.correct-icon {
  color: #4CAF50;
}

.total-icon {
  color: #2196F3;
}

mat-progress-bar {
  height: 8px;
  border-radius: 4px;
  margin: 20px 0;
}

mat-progress-bar.excellent {
  --mdc-linear-progress-active-indicator-color: #4CAF50;
}

mat-progress-bar.good {
  --mdc-linear-progress-active-indicator-color: #8BC34A;
}

mat-progress-bar.average {
  --mdc-linear-progress-active-indicator-color: #FFC107;
}

mat-progress-bar.poor {
  --mdc-linear-progress-active-indicator-color: #F44336;
}

mat-card-actions {
  display: flex;
  justify-content: space-around;
  padding: 16px 0;
}

button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
}

@media (max-width: 600px) {
  .percentage-circle {
    width: 120px;
    height: 120px;
    font-size: 28px;
  }

  mat-card-actions {
    flex-direction: column;
    gap: 10px;
  }

  button {
    width: 100%;
  }
}
