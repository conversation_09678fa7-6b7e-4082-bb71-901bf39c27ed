<div class="lobby-container">

  <div class="lobby-content">
    <div class="selected-quiz" *ngIf="selectedChapter">
      <h3 style="color: #000000;" >{{ 'lobby.selectedQuiz' | translate:{name: selectedChapter.name} }}</h3>
    </div>

    <div class="lobby-buttons">
      <button mat-raised-button color="primary" style="color: #fff !important;" (click)="openQuizSelectionDialog()">
        {{ 'lobby.selectQuiz' | translate }}
      </button>

      <button mat-raised-button color="accent" [disabled]="!selectedChapter" (click)="launchQuiz()">
        {{ 'lobby.launchQuiz' | translate }}
      </button>
    </div>
  </div>
</div>
