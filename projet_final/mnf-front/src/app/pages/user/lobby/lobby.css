body {
  font-family: Arial, sans-serif;
  font-size: large;
  display: flex;
  align-items: center;
  margin: 0;
}

.lobby-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
  min-height: 70vh;
  margin: 64px;
}

::ng-deep .mat-mdc-dialog-container {
  border-radius: 28px;
}

.lobby-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}

.lobby-title {
  font-size: 28px;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
}

.lobby-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.selected-quiz {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #e0f7fa;
  border-radius: 8px;
  width: 100%;
  text-align: center;
}

.selected-quiz h3 {
  margin: 0;
  color: var(--primary-color);
  font-size: 18px;
}

.lobby-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.lobby-buttons button {
  padding: 15px 20px;
  font-size: 18px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.lobby-buttons button:hover:not([disabled]) {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}
