body {
  font-family: Arial, sans-serif;
  font-size: large;
  display: flex;
  align-items: center;
  margin: 0;
}

.lobby-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  min-height: 70vh;
  margin: 64px;
}

.question-number {
  display: flex;
  align-self: flex-end;
  gap: 10px;
  width: 30%;
}

.number-field {
  flex: 1;
  border-radius: 10px;
  padding: 10px 20px;
  background-color: #7dc5c6;
}

.launch-button {
  background-color: var(--secondary-color);
  color: var(--background-color);
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  font-size: 32px;
  transition: all 0.3s ease; /* Animation douce pour les effets */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.launch-button:hover {
  background-color: #942c3d;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

input:focus {
  border: 3px solid var(--border-color);
}
