import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { MaterialModules } from "../../../material/material";
import { ChapitreList } from "../../../components/user/lobby/chapitre-list/chapitre-list";
import { MatDialog } from "@angular/material/dialog";
import { ChannelInviteDialogComponent } from "../../../components/user/channel-invite-dialog/channel-invite-dialog";
import { AuthService } from "../../../services/auth.service";
import { ChannelService, Channel } from "../../../services/channel/channel.service";

@Component({
  selector: "app-lobby",
  standalone: true,
  imports: [CommonModule, ChapitreList],
  templateUrl: "./lobby.html",
  styleUrls: ["./lobby.css"],
})
export class LobbyPage implements OnInit {
  loading = false;

  constructor(

  ) {}

  ngOnInit(): void {
  }


}
