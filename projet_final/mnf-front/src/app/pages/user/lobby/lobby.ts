import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { MaterialModules } from "../../../material/material";
import { ChapitreList } from "../../../components/user/lobby/chapitre-list/chapitre-list";
import { MatDialog } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { QuizSelectionDialogComponent } from "../../../components/user/quiz-selection-dialog/quiz-selection-dialog.component";
import { Chapter } from "../../../models/admin/Chapter";

@Component({
  selector: "app-lobby",
  standalone: true,
  imports: [CommonModule, MaterialModules],
  templateUrl: "./lobby.html",
  styleUrls: ["./lobby.css"],
})
export class LobbyPage implements OnInit {
  loading = false;
  selectedChapter: Chapter | null = null;
  numberOfQuestions: number = 5; // Default number of questions

  constructor(
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
  }

  openQuizSelectionDialog(): void {
    const dialogRef = this.dialog.open(QuizSelectionDialogComponent, {
      width: '80%',
      maxWidth: '800px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.selectedChapter = result;
      }
    });
  }

  launchQuiz(): void {
    if (this.selectedChapter) {
      // Navigate to quiz page with selected chapter and number of questions
      this.router.navigate(['/quiz'], {
        queryParams: {
          chapterId: this.selectedChapter.id,
          numberOfQuestions: this.numberOfQuestions
        }
      });
    }
  }
}
