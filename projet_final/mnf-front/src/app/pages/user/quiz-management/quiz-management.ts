import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatListModule } from '@angular/material/list';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { DragDropModule, CdkDragDrop, transferArrayItem } from '@angular/cdk/drag-drop';

import { QuizService } from '../../../services/user/quiz.service';
import { AuthService } from '../../../services/auth.service';

import { Quiz } from '../../../models/user/Quiz';
import { ConfirmDialogComponent, ConfirmDialogData } from '../../../shared/components/confirm-dialog/confirm-dialog.component';



@Component({
  selector: 'app-quiz-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatExpansionModule,
    MatListModule,
    MatCardModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    DragDropModule
  ],
  templateUrl: './quiz-management.html',
  styleUrl: './quiz-management.css'
})
export class QuizManagementPage implements OnInit {
  // Chapters and questions
  chapters: Chapter[] = [];
  questions: { [chapterId: number]: Question[] } = {};
  selectedChapter: Chapter | null = null;

  // Loading and error states
  loadingChapters = false;
  loadingQuestions = false;
  loadingQuiz = false;
  loadingQuizzes = false;
  chapterError = '';
  questionError = '';
  quizError = '';
  quizzesError = '';

  // Quiz data
  quizzes: Quiz[] = [];
  currentQuiz: Quiz | null = null;
  quizName = '';
  quizDescription = '';
  quizItems: QuizQuestionItem[] = [];

  constructor(
    private chapterService: ChapterService,
    private questionService: QuestionService,
    private quizService: QuizService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadChapters();
    this.loadQuizzes();

    // Subscribe to current quiz changes
    this.quizService.currentQuiz$.subscribe(quiz => {
      if (quiz) {
        this.currentQuiz = quiz;
        this.quizName = quiz.name;
        this.quizDescription = quiz.description || '';
        this.loadQuizQuestions(quiz.id!);
      } else {
        this.currentQuiz = null;
        this.quizName = '';
        this.quizDescription = '';
        this.quizItems = [];
      }
    });
  }

  /**
   * Load all chapters
   */
  loadChapters(): void {
    this.loadingChapters = true;
    this.chapterError = '';

    this.chapterService.getChapters()
      .then(chapters => {
        this.chapters = chapters;
        this.loadingChapters = false;
      })
      .catch(error => {
        this.chapterError = 'Failed to load chapters';
        this.loadingChapters = false;
        console.error('Error loading chapters:', error);
      });
  }

  /**
   * Load questions for a chapter
   */
  loadQuestionsForChapter(chapter: Chapter): void {
    if (this.questions[chapter.id!]) {
      // Questions already loaded
      this.selectedChapter = chapter;
      return;
    }

    this.loadingQuestions = true;
    this.questionError = '';
    this.selectedChapter = chapter;

    this.questionService.getQuestionsByChapterId(chapter.id!)
      .then(questions => {
        this.questions[chapter.id!] = questions;
        this.loadingQuestions = false;
      })
      .catch(error => {
        this.questionError = 'Failed to load questions';
        this.loadingQuestions = false;
        console.error('Error loading questions:', error);
      });
  }

  /**
   * Load all quizzes for the current user
   */
  loadQuizzes(): void {
    if (!this.authService.currentUserValue) {
      this.quizzesError = 'You must be logged in to view quizzes';
      return;
    }

    this.loadingQuizzes = true;
    this.quizzesError = '';

    this.quizService.getQuizzesByUser(this.authService.currentUserValue.id)
      .then(quizzes => {
        this.quizzes = quizzes;
        this.loadingQuizzes = false;
      })
      .catch(error => {
        this.quizzesError = 'Failed to load quizzes';
        this.loadingQuizzes = false;
        console.error('Error loading quizzes:', error);
      });
  }

  /**
   * Load a quiz for editing
   */
  loadQuiz(quiz: Quiz): void {
    this.quizService.setCurrentQuiz(quiz);
  }

  /**
   * Load questions for a quiz
   */
  loadQuizQuestions(quizId: number): void {
    this.loadingQuiz = true;
    this.quizError = '';
    this.quizItems = [];

    this.quizService.getQuizQuestions(quizId)
      .then(quizQuestions => {
        // Load details for each question
        const promises = quizQuestions.map(quizQuestion =>
          this.questionService.getQuestionById(quizQuestion.questionId)
        );

        return Promise.all(promises).then(questions => {
          // Map questions to quiz items
          this.quizItems = questions
            .filter(q => q !== null)
            .map((q, index) => ({
              questionId: q!.id!,
              title: q!.title,
              statement: q!.statement,
              chapterId: q!.chapter_id,
              chapterName: q!.chapter?.name
            }));

          this.loadingQuiz = false;
        });
      })
      .catch(error => {
        this.quizError = 'Failed to load quiz questions';
        this.loadingQuiz = false;
        console.error('Error loading quiz questions:', error);
      });
  }

  /**
   * Create a new quiz
   */
  createNewQuiz(): void {
    if (!this.quizName.trim()) {
      this.snackBar.open('Please enter a quiz name', 'Close', { duration: 3000 });
      return;
    }

    this.loadingQuiz = true;
    this.quizError = '';

    this.quizService.createEmptyQuiz(this.quizName, this.quizDescription)
      .then(quiz => {
        if (quiz) {
          this.snackBar.open('Quiz created successfully', 'Close', { duration: 3000 });
          this.loadQuizzes(); // Refresh the list
          // Redirect to question-series
          this.router.navigate(['/question-series'], { queryParams: { quizId: quiz.id } });
        } else {
          this.quizError = 'Failed to create quiz';
        }
        this.loadingQuiz = false;
      })
      .catch(error => {
        this.quizError = 'Failed to create quiz';
        this.loadingQuiz = false;
        console.error('Error creating quiz:', error);
      });
  }

  /**
   * Update the current quiz
   */
  updateQuiz(): void {
    if (!this.currentQuiz) return;

    if (!this.quizName.trim()) {
      this.snackBar.open('Please enter a quiz name', 'Close', { duration: 3000 });
      return;
    }

    this.loadingQuiz = true;

    const updatedQuiz = {
      ...this.currentQuiz,
      name: this.quizName,
      description: this.quizDescription
    };

    this.quizService.updateQuiz(this.currentQuiz.id!, updatedQuiz)
      .then(quiz => {
        if (quiz) {
          this.snackBar.open('Quiz updated successfully', 'Close', { duration: 3000 });
          this.loadQuizzes(); // Refresh the list
          // Redirect to question-series
          this.router.navigate(['/question-series'], { queryParams: { quizId: this.currentQuiz!.id } });
        } else {
          this.quizError = 'Failed to update quiz';
        }
        this.loadingQuiz = false;
      })
      .catch(error => {
        this.quizError = 'Failed to update quiz';
        this.loadingQuiz = false;
        console.error('Error updating quiz:', error);
      });
  }

  /**
   * Edit a quiz - redirect to question-series page
   */
  editQuiz(quiz: Quiz): void {
    // Set the current quiz in the service for the question-series page
    this.quizService.setCurrentQuiz(quiz);
    // Navigate to question-series page
    this.router.navigate(['/user/question-series']);
  }

  /**
   * Delete a quiz with confirmation dialog
   */
  deleteQuiz(quizId: number): void {
    const quiz = this.quizzes.find(q => q.id === quizId);
    const quizName = quiz ? quiz.name : 'this quiz';

    const dialogData: ConfirmDialogData = {
      title: 'Delete Quiz',
      message: `Are you sure you want to delete "${quizName}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel'
    };

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: dialogData
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.performDeleteQuiz(quizId);
      }
    });
  }

  /**
   * Perform the actual quiz deletion
   */
  private performDeleteQuiz(quizId: number): void {
    this.quizService.deleteQuiz(quizId)
      .then(success => {
        if (success) {
          this.snackBar.open('Quiz deleted successfully', 'Close', { duration: 3000 });
          this.loadQuizzes(); // Refresh the list

          // Clear current quiz if it's the one being deleted
          if (this.currentQuiz && this.currentQuiz.id === quizId) {
            this.quizService.setCurrentQuiz(null);
          }
        } else {
          this.snackBar.open('Failed to delete quiz', 'Close', { duration: 3000 });
        }
      })
      .catch(error => {
        this.snackBar.open('Error deleting quiz', 'Close', { duration: 3000 });
        console.error('Error deleting quiz:', error);
      });
  }

  /**
   * Handle drag and drop of questions
   */
  onQuestionDrop(event: CdkDragDrop<QuizQuestionItem[]>): void {
    if (event.previousContainer === event.container) {
      // Reordering within the same list
      // We can implement this later if needed
    } else {
      // Moving from questions list to quiz list
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );

      // If we have a current quiz, update it with the new questions
      if (this.currentQuiz) {
        this.updateQuizQuestions();
      }
    }
  }

  /**
   * Update the quiz with the current questions
   */
  updateQuizQuestions(): void {
    if (!this.currentQuiz) return;

    // Create quiz questions from the items
    const quizQuestions = this.quizItems.map((item, index) => ({
      quizId: this.currentQuiz!.id!,
      questionId: item.questionId,
      position: index + 1
    }));

    // TODO: Implement backend method to update quiz questions
    // For now, just show a success message
    this.snackBar.open('Quiz questions updated', 'Close', { duration: 3000 });
  }

  /**
   * Remove a question from the quiz
   */
  removeQuestion(questionId: number): void {
    const index = this.quizItems.findIndex(item => item.questionId === questionId);
    if (index !== -1) {
      this.quizItems.splice(index, 1);

      // If we have a current quiz, update it with the new questions
      if (this.currentQuiz) {
        this.updateQuizQuestions();
      }
    }
  }

  /**
   * Get questions for the selected chapter
   */
  chapterQuestions(): Question[] {
    if (!this.selectedChapter || !this.questions[this.selectedChapter.id!]) {
      return [];
    }

    return this.questions[this.selectedChapter.id!];
  }

  /**
   * Convert chapter questions to quiz question items for drag and drop
   */
  chapterQuestionsAsItems(): QuizQuestionItem[] {
    return this.chapterQuestions().map(q => ({
      questionId: q.id!,
      title: q.title,
      statement: q.statement,
      chapterId: q.chapter_id,
      chapterName: q.chapter?.name
    }));
  }
}
