import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatListModule } from '@angular/material/list';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { TranslatePipe } from '../../../pipes/translate.pipe';
import { TranslationService } from '../../../services/translation.service';

import { QuizService } from '../../../services/user/quiz.service';
import { AuthService } from '../../../services/auth.service';

import { Quiz } from '../../../models/user/Quiz';
import { ConfirmDialogComponent, ConfirmDialogData } from '../../../shared/components/confirm-dialog/confirm-dialog.component';
import { PaginationComponent } from '../../../components/shared/pagination/pagination';
import { ShareQuizDialogComponent } from '../../../components/user/share-quiz-dialog/share-quiz-dialog';


@Component({
  selector: 'app-quiz-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatExpansionModule,
    MatListModule,
    MatCardModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    PaginationComponent,
    TranslatePipe
  ],
  templateUrl: './quiz-management.html',
  styleUrl: './quiz-management.css'
})
export class QuizManagementPage implements OnInit {
  // Loading and error states
  loadingQuizzes = false;
  quizzesError = '';

  // Quiz data
  quizzes: Quiz[] = [];

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  totalPages = 0;
  paginatedQuizzes: Quiz[] = [];

  // Make Math available in the template
  Math = Math;

  // Format pagination info string with parameters
  getPaginationInfo(): string {
    // Calculate the pagination values
    const start = (this.currentPage - 1) * this.pageSize + 1;
    const end = Math.min(this.currentPage * this.pageSize, this.totalItems);
    const total = this.totalItems;

    // Get the translation template
    const template = this.translationService.translate('quizManagement.paginationInfo');

    // Replace the placeholders with the actual values
    return template
      .replace('{{start}}', start.toString())
      .replace('{{end}}', end.toString())
      .replace('{{total}}', total.toString());
  }

  constructor(
    private quizService: QuizService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router,
    private translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.loadQuizzes();
  }

  /**
   * Add a new quiz - redirect to question-series page
   */
  addNewQuiz(): void {
    // Clear any current quiz in the service
    this.quizService.setCurrentQuiz(null);
    // Navigate to question-series page
    this.router.navigate(['/question-series']);
  }





  /**
   * Load all quizzes for the current user
   */
  loadQuizzes(): void {
    if (!this.authService.currentUserValue) {
      this.quizzesError = 'You must be logged in to view quizzes';
      return;
    }

    this.loadingQuizzes = true;
    this.quizzesError = '';

    this.quizService.getQuizzes()
      .then(quizzes => {
        // Process dates to ensure they're in the correct format
        this.quizzes = quizzes.map(quiz => {
          return {
            ...quiz,
            createdAt: quiz.createdAt ? (Array.isArray(quiz.createdAt) ?
              new Date(quiz.createdAt[0], quiz.createdAt[1] - 1, quiz.createdAt[2],
                quiz.createdAt[3] || 0, quiz.createdAt[4] || 0, quiz.createdAt[5] || 0) :
              new Date(quiz.createdAt)) : undefined,
            updatedAt: quiz.updatedAt ? (Array.isArray(quiz.updatedAt) ?
              new Date(quiz.updatedAt[0], quiz.updatedAt[1] - 1, quiz.updatedAt[2],
                quiz.updatedAt[3] || 0, quiz.updatedAt[4] || 0, quiz.updatedAt[5] || 0) :
              new Date(quiz.updatedAt)) : undefined
          };
        });
        this.totalItems = quizzes.length;
        this.totalPages = Math.ceil(this.totalItems / this.pageSize);
        this.updatePaginatedQuizzes();
        this.loadingQuizzes = false;
      })
      .catch(error => {
        this.quizzesError = 'Failed to load quizzes';
        this.loadingQuizzes = false;
        console.error('Error loading quizzes:', error);
      });
  }

  /**
   * Update the paginated quizzes based on the current page and page size
   */
  updatePaginatedQuizzes(): void {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.totalItems);
    this.paginatedQuizzes = this.quizzes.slice(startIndex, endIndex);
  }

  /**
   * Handle page change event from pagination component
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.updatePaginatedQuizzes();
  }

  /**
   * Handle page size change
   */
  onPageSizeChange(): void {
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
    this.currentPage = 1; // Reset to first page
    this.updatePaginatedQuizzes();
  }





  /**
   * Edit a quiz - redirect to question-series page
   */
  editQuiz(quiz: Quiz): void {
    // Set the current quiz in the service for the question-series page
    this.quizService.setCurrentQuiz(quiz);
    // Navigate to question-series page
    this.router.navigate(['/question-series']);
  }

  /**
   * Share a quiz with favorites - open share dialog
   */
  shareQuiz(quiz: Quiz): void {
    const dialogRef = this.dialog.open(ShareQuizDialogComponent, {
      width: '500px',
      data: {
        quizId: quiz.id,
        quizName: quiz.name
      }
    });

    dialogRef.afterClosed().subscribe(selectedFavorites => {
      if (selectedFavorites && selectedFavorites.length > 0) {
        // Show a success message with the names of the selected favorites
        const favoriteNames = selectedFavorites.map((f: any) => f.favoriteUserName).join(', ');
        this.snackBar.open(`Quiz "${quiz.name}" shared with: ${favoriteNames}`, 'Close', {
          duration: 5000,
          horizontalPosition: 'end',
          verticalPosition: 'bottom'
        });
      }
    });
  }

  /**
   * Delete a quiz with confirmation dialog
   */
  deleteQuiz(quizId: number): void {
    const quiz = this.quizzes.find(q => q.id === quizId);
    const quizName = quiz ? quiz.name : 'this quiz';

    const dialogData: ConfirmDialogData = {
      title: this.translationService.translate('quizManagement.deleteConfirmTitle'),
      message: this.translationService.translate('quizManagement.deleteConfirmMessage').replace('{{quizName}}', quizName),
      confirmText: this.translationService.translate('common.delete'),
      cancelText: this.translationService.translate('common.cancel')
    };

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: dialogData
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.performDeleteQuiz(quizId);
      }
    });
  }

  /**
   * Perform the actual quiz deletion
   */
  private performDeleteQuiz(quizId: number): void {
    this.quizService.deleteQuiz(quizId)
      .then(success => {
        if (success) {
          this.snackBar.open(
            this.translationService.translate('quizManagement.deleteSuccess'),
            this.translationService.translate('common.cancel'),
            {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'bottom'
            }
          );
          this.loadQuizzes(); // Refresh the list
        } else {
          this.snackBar.open(
            this.translationService.translate('quizManagement.deleteError'),
            this.translationService.translate('common.cancel'),
            {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'bottom'
            }
          );
        }
      })
      .catch(error => {
        this.snackBar.open(
          this.translationService.translate('quizManagement.deleteError'),
          this.translationService.translate('common.cancel'),
          {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'bottom'
          }
        );
        console.error('Error deleting quiz:', error);
      });
  }

}
