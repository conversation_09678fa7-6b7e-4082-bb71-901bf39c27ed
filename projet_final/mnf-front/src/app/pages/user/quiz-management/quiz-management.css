/* Quiz Management Styles - Similar to promotion dashboard */
.quiz-management-container {
  margin: 120px auto;
  display: flex;
  align-content: center;
  justify-content: center;
  max-width: 1200px;
  padding: 1.5rem;
}

.list-container {
  width: 100%;
}

/* List content */
.list-content {
  background-color: #cd7a88;
  border-radius: 0.5rem;
  padding: 4px;
}

/* Enhanced Table styles */
.table-container {
  background-color: #f0dddb;
  border-radius: 0.5rem;
}

/* Enhanced Header styles */
.list-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-left {
  display: flex;
  width: 80%;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #cd7a88;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.list-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #000000;
  text-align: center;
  margin-bottom: 32px;
}

.header-icon {
  margin-right: 0.75rem;
  color: #3b82f6;
  font-size: 1.5rem;
}

.header-right {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-left: 20px;
}

.add-button {
  background-color: #cd7a88;
  color: white;
  text-wrap: nowrap;
  font-weight: 600;
  padding: 0.75rem 1.25rem;
  border-radius: 16px;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-left: 7px;
  height: 21px;
  z-index: 11;
}

/* Table styles */
.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.table-header {
  background-color: #f0dddb;
}

.table-header-cell {
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.edit-header {
  text-align: center;
}

.table-row {
  transition: background-color 0.2s;
}

.table-row:hover {
  background-color: rgba(243, 244, 246, 0.5);
}

.table-cell {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.edit-cell {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  color: #cd7a88;
}

.action-icon {
  font-size: 1.25rem;
}

/* Pagination styles */
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f0dddb;
  border-radius: 0.5rem;
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-size-selector select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  background-color: white;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

/* Empty state styles */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background-color: #f0dddb;
  border-radius: 0.5rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  color: #cd7a88;
  margin-bottom: 1rem;
}

.empty-state-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.empty-state-subtext {
  color: #6b7280;
}

/* Loading and error styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #f0dddb;
  border-radius: 0.5rem;
}

.error-message {
  padding: 1rem;
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 0.5rem;
  color: #b91c1c;
  margin-bottom: 1rem;
}

.questions-list, .quiz-questions-list {
  min-height: 200px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin: 10px 0;
}

.question-item, .quiz-question-item {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  background-color: white;
  cursor: move;
}

.question-item:last-child, .quiz-question-item:last-child {
  border-bottom: none;
}

.question-content {
  display: flex;
  align-items: flex-start;
}

.question-number {
  min-width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #3f51b5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.question-details {
  flex: 1;
}

.question-details h3 {
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.question-details p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.chapter-tag {
  display: inline-block;
  background-color: #e0e0e0;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-top: 5px;
}



.button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.divider {
  margin: 20px 0;
}

.add-quiz-button {
  margin-top: 10px;
}

.add-quiz-button mat-icon {
  margin-right: 8px;
}

.quiz-list-item {
  border-bottom: 1px solid #e0e0e0;
}

.quiz-list-content {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px 0;
}

.quiz-info {
  flex: 1;
  margin-right: 15px;
}

.quiz-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.quiz-description {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.quiz-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 5px;
}

.metadata-item {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #888;
}

.metadata-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.quiz-actions {
  display: flex;
  gap: 5px;
  flex-shrink: 0;
}

.empty-list {
  padding: 40px 20px;
  text-align: center;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 10px;
  opacity: 0.5;
}

.empty-subtitle {
  font-size: 0.9rem;
  margin-top: 5px;
}

/* Drag and drop styles */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.questions-list.cdk-drop-list-dragging .question-item:not(.cdk-drag-placeholder),
.quiz-questions-list.cdk-drop-list-dragging .quiz-question-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
