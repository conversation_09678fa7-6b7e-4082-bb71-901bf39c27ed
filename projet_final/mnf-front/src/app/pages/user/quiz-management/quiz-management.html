<div class="quiz-management-container">
  <div class="list-container">
    <!-- Enhanced header with dynamic title and add button -->
    <div class="header-container">
      <h2 class="list-title">
        {{ 'quizManagement.title' | translate }}
      </h2>
    </div>


    <!-- Enhanced empty state message -->
    @if (!loadingQuizzes && quizzes.length === 0) {
    <div class="empty-state">
      <mat-icon class="empty-icon">quiz</mat-icon>
      <p class="empty-state-text">
        {{ 'quizManagement.noQuizzesFound' | translate }}
      </p>
      <p class="empty-state-subtext">
        {{ 'quizManagement.createFirstQuiz' | translate }}
      </p>
    </div>
    }

    <!-- Loading indicator -->
    <div *ngIf="loadingQuizzes" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>{{ 'quizManagement.loadingQuizzes' | translate }}</p>
    </div>

    <!-- Enhanced list content with dynamic data -->
    @if (!loadingQuizzes && quizzes.length > 0) {
    <div class="list-content">
      <div class="table-container">
        <table class="data-table">
          <thead class="table-header">
            <tr>
              <th scope="col" class="table-header-cell">{{ 'quizManagement.nameColumn' | translate }}</th>
              <th scope="col" class="table-header-cell">{{ 'quizManagement.descriptionColumn' | translate }}</th>
              <th scope="col" class="table-header-cell">{{ 'quizManagement.createdColumn' | translate }}</th>
              <th scope="col" class="table-header-cell">{{ 'quizManagement.updatedColumn' | translate }}</th>
              <th scope="col" class="table-header-cell edit-header">{{ 'quizManagement.actionsColumn' | translate }}</th>
            </tr>
          </thead>
          <tbody class="table-body">
            @for (quiz of paginatedQuizzes; track quiz.id) {
            <tr class="table-row">
              <td class="table-cell">
                <span class="name-value">{{ quiz.name }}</span>
              </td>
              <td class="table-cell">
                <span class="description-value">
                  @if (quiz.description) {
                    {{ quiz.description }}
                  } @else {
                    <span>{{ 'quizManagement.noDescription' | translate }}</span>
                  }
                </span>
              </td>
              <td class="table-cell">
                <span class="date-value">
                  @if (quiz.createdAt) {
                    {{ quiz.createdAt | date }}
                  } @else {
                    <span>{{ 'quizManagement.notAvailable' | translate }}</span>
                  }
                </span>
              </td>
              <td class="table-cell">
                <span class="date-value">
                  @if (quiz.updatedAt) {
                    {{ quiz.updatedAt | date }}
                  } @else {
                    <span>{{ 'quizManagement.notAvailable' | translate }}</span>
                  }
                </span>
              </td>
              <td class="table-cell edit-cell">

                <button
                  (click)="deleteQuiz(quiz.id!)"
                  class="action-button delete-button"
                  [title]="'quizManagement.deleteButton' | translate"
                  mat-icon-button
                  color="warn"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </tr>
            }
          </tbody>
        </table>

        <!-- Enhanced pagination -->
        <div class="pagination-wrapper">
          <div class="pagination-left">
            <div class="page-size-selector">
              <label for="pageSize">{{ 'quizManagement.itemsPerPage' | translate }}</label>
              <select
                id="pageSize"
                [(ngModel)]="pageSize"
                (change)="onPageSizeChange()"
              >
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
              </select>
            </div>
            <div class="pagination-info">
              @if (totalItems > 0) {
                <span>{{ getPaginationInfo() }}</span>
              } @else {
                <span>{{ 'quizManagement.noQuizzesToDisplay' | translate }}</span>
              }
            </div>
          </div>
          <div class="pagination-controls">
            <app-pagination
              [currentPage]="currentPage"
              [pageSize]="pageSize"
              [totalItems]="totalItems"
              [totalPages]="totalPages"
              (pageChange)="onPageChange($event)"
              class="enhanced-pagination"
            ></app-pagination>
          </div>
        </div>
      </div>
    </div>
    }
  </div>
</div>
