<div class="quiz-management-container">
  <div class="header">
    <h1>Quiz Management</h1>
    <p>Create and manage your quizzes</p>
  </div>

  <div class="content-container">
    <!-- Left panel: Chapters and Questions -->
    <div class="left-panel">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Questions</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <!-- Chapters accordion -->
          <mat-accordion class="chapters-accordion" *ngIf="!loadingChapters">
            <mat-expansion-panel *ngFor="let chapter of chapters">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  {{ chapter.name }}
                </mat-panel-title>
              </mat-expansion-panel-header>

              <!-- Questions list -->
              <div *ngIf="selectedChapter?.id === chapter.id">
                <div *ngIf="loadingQuestions" class="loading-container">
                  <mat-spinner diameter="40"></mat-spinner>
                  <p>Loading questions...</p>
                </div>

                <div *ngIf="questionError" class="error-message">
                  {{ questionError }}
                </div>

              </div>

              <button mat-button color="primary" (click)="loadQuestionsForChapter(chapter)">
                Load Questions
              </button>
            </mat-expansion-panel>
          </mat-accordion>

          <div *ngIf="loadingChapters" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading chapters...</p>
          </div>

          <div *ngIf="chapterError" class="error-message">
            {{ chapterError }}
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Right panel: Quiz Management -->
    <div class="right-panel">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <span *ngIf="!currentQuiz">Create New Quiz</span>
            <span *ngIf="currentQuiz">Edit Quiz: {{ quizName }}</span>
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div class="quiz-form">
            <mat-form-field appearance="fill" class="full-width">
              <mat-label>Quiz Name</mat-label>
              <input matInput [(ngModel)]="quizName" placeholder="Enter quiz name">
            </mat-form-field>

            <mat-form-field appearance="fill" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput [(ngModel)]="quizDescription" placeholder="Enter quiz description" rows="3"></textarea>
            </mat-form-field>

            <div class="button-container">
              <button
                mat-raised-button
                color="primary"
                (click)="createNewQuiz()"
                [disabled]="loadingQuiz || !quizName.trim()"
                *ngIf="!currentQuiz">
                Create Quiz
              </button>

              <button
                mat-raised-button
                color="primary"
                (click)="updateQuiz()"
                [disabled]="loadingQuiz || !quizName.trim()"
                *ngIf="currentQuiz">
                Update Quiz
              </button>
            </div>
          </div>

          <mat-divider class="divider"></mat-divider>

          <div class="quiz-questions-container">
            <h3>Quiz Questions</h3>

            <div *ngIf="loadingQuiz" class="loading-container">
              <mat-spinner diameter="40"></mat-spinner>
              <p>Loading quiz data...</p>
            </div>

            <div *ngIf="quizError" class="error-message">
              {{ quizError }}
            </div>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- Quiz List -->
      <mat-card class="quiz-list-card">
        <mat-card-header>
          <mat-card-title>My Quizzes</mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div *ngIf="loadingQuizzes" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading quizzes...</p>
          </div>

          <div *ngIf="quizzesError" class="error-message">
            {{ quizzesError }}
          </div>

          <mat-list>
            <mat-list-item *ngFor="let quiz of quizzes" class="quiz-list-item">
              <div class="quiz-list-content">
                <h3 matLine>{{ quiz.name }}</h3>
                <p matLine *ngIf="quiz.description">{{ quiz.description }}</p>

                <div class="quiz-actions">
                  <button mat-icon-button color="primary" (click)="loadQuiz(quiz)">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteQuiz(quiz.id!)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </mat-list-item>

            <div *ngIf="quizzes.length === 0" class="empty-list">
              No quizzes available
            </div>
          </mat-list>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
