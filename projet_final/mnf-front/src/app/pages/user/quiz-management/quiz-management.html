<div class="quiz-management-container">
  <div class="list-container">
    <!-- Enhanced header with dynamic title and add button -->

    <h2 class="list-title" i18n="@@quizManagementTitle">
      Quiz Management
    </h2>


    <!-- Enhanced empty state message -->
    @if (!loadingQuizzes && quizzes.length === 0) {
    <div class="empty-state">
      <mat-icon class="empty-icon">quiz</mat-icon>
      <p class="empty-state-text" i18n="@@noQuizzesFound">
        No quizzes found.
      </p>
      <p class="empty-state-subtext" i18n="@@createFirstQuiz">
        Create your first quiz to get started.
      </p>
    </div>
    }

    <!-- Loading indicator -->
    <div *ngIf="loadingQuizzes" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p i18n="@@loadingQuizzes">Loading quizzes...</p>
    </div>

    <!-- Enhanced list content with dynamic data -->
    @if (!loadingQuizzes && quizzes.length > 0) {
    <div class="list-content">
      <div class="table-container">
        <table class="data-table">
          <thead class="table-header">
            <tr>
              <th scope="col" class="table-header-cell" i18n="@@nameColumn">Name</th>
              <th scope="col" class="table-header-cell" i18n="@@descriptionColumn">Description</th>
              <th scope="col" class="table-header-cell" i18n="@@createdColumn">Created</th>
              <th scope="col" class="table-header-cell" i18n="@@updatedColumn">Updated</th>
              <th scope="col" class="table-header-cell edit-header" i18n="@@actionsColumn">Actions</th>
            </tr>
          </thead>
          <tbody class="table-body">
            @for (quiz of paginatedQuizzes; track quiz.id) {
            <tr class="table-row">
              <td class="table-cell">
                <span class="name-value">{{ quiz.name }}</span>
              </td>
              <td class="table-cell">
                <span class="description-value">
                  @if (quiz.description) {
                    {{ quiz.description }}
                  } @else {
                    <span i18n="@@noDescription">No description</span>
                  }
                </span>
              </td>
              <td class="table-cell">
                <span class="date-value">
                  @if (quiz.createdAt) {
                    {{ quiz.createdAt | date }}
                  } @else {
                    <span i18n="@@notAvailable">N/A</span>
                  }
                </span>
              </td>
              <td class="table-cell">
                <span class="date-value">
                  @if (quiz.updatedAt) {
                    {{ quiz.updatedAt | date }}
                  } @else {
                    <span i18n="@@notAvailable">N/A</span>
                  }
                </span>
              </td>
              <td class="table-cell edit-cell">
                <button
                  (click)="editQuiz(quiz)"
                  class="action-button edit-button"
                  i18n-title="@@editButton" title="Edit"
                  mat-icon-button
                  color="primary"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                <button
                  (click)="shareQuiz(quiz)"
                  class="action-button share-button"
                  i18n-title="@@shareButton" title="Share with favorites"
                  mat-icon-button
                  color="accent"
                >
                  <mat-icon>share</mat-icon>
                </button>
                <button
                  (click)="deleteQuiz(quiz.id!)"
                  class="action-button delete-button"
                  i18n-title="@@deleteButton" title="Delete"
                  mat-icon-button
                  color="warn"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </tr>
            }
          </tbody>
        </table>

        <!-- Enhanced pagination -->
        <div class="pagination-wrapper">
          <div class="pagination-left">
            <div class="page-size-selector">
              <label for="pageSize" i18n="@@itemsPerPage">Items per page:</label>
              <select
                id="pageSize"
                [(ngModel)]="pageSize"
                (change)="onPageSizeChange()"
              >
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
              </select>
            </div>
            <div class="pagination-info">
              @if (totalItems > 0) {
                <span i18n="@@paginationInfo">Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} quizzes</span>
              } @else {
                <span i18n="@@noQuizzesToDisplay">No quizzes to display</span>
              }
            </div>
          </div>
          <div class="pagination-controls">
            <app-pagination
              [currentPage]="currentPage"
              [pageSize]="pageSize"
              [totalItems]="totalItems"
              [totalPages]="totalPages"
              (pageChange)="onPageChange($event)"
              class="enhanced-pagination"
            ></app-pagination>
          </div>
        </div>
      </div>
    </div>
    }
  </div>
</div>
