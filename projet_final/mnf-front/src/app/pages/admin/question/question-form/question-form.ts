import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Question } from '../../../../models/admin/Question';
import { QuestionService } from '../../../../services/admin/question.service';
import { ChapterService } from '../../../../services/admin/chapter.service';
import { NotificationService } from '../../../../services/notification.service';
import { Chapter } from '../../../../models/admin/Chapter';

@Component({
  selector: 'app-question-form',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './question-form.html',
  styleUrl: '../../list-container/list-container.css'
})
export class QuestionFormComponent implements OnInit {
  question: Question = {
    title: '',
    statement: '',
    chapter_id: 0
  };
  chapters: Chapter[] = [];
  isEdit = false;
  loading = false;
  error = '';
  success = '';

  constructor(
    private questionService: QuestionService,
    private chapterService: ChapterService,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadChapters();

    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEdit = true;
      this.loadQuestion(+id);
    }
  }

  loadChapters(): void {
    this.loading = true;
    this.chapterService.getChapters()
      .then(chapters => {
        this.chapters = chapters;
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load chapters. Please try again.';
        this.notificationService.error('Failed to load chapters. Please try again.');
        this.loading = false;
        console.error('Error loading chapters:', error);
      });
  }

  loadQuestion(id: number): void {
    this.loading = true;
    this.questionService.getQuestionById(id)
      .then(question => {
        if (question) {
          this.question = question;
        } else {
          this.error = 'Question not found';
          this.notificationService.error('Question not found');
        }
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load question. Please try again.';
        this.notificationService.error('Failed to load question. Please try again.');
        this.loading = false;
        console.error('Error loading question:', error);
      });
  }

  saveQuestion(): void {
    this.loading = true;
    this.error = '';
    this.success = '';

    const save = this.isEdit
      ? this.questionService.updateQuestion(this.question.id!, this.question)
      : this.questionService.createQuestion(this.question);

    save
      .then(() => {
        const successMessage = `Question ${this.isEdit ? 'updated' : 'created'} successfully`;
        this.success = successMessage;
        this.notificationService.success(successMessage);

        if (!this.isEdit) {
          this.question = { title: '', statement: '', chapter_id: 0 };
        }

        // Navigate back to questions list
        this.router.navigate(['/admin/dashboard/questions']);

        this.loading = false;
      })
      .catch(error => {
        const errorMessage = `Failed to ${this.isEdit ? 'update' : 'create'} question. Please try again.`;
        this.error = errorMessage;
        this.notificationService.error(errorMessage);
        this.loading = false;
        console.error(`Error ${this.isEdit ? 'updating' : 'creating'} question:`, error);
      });
  }
}
