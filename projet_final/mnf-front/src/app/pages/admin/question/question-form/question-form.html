<div class="list-container">
  <div class="list-header border-rad">
    <div class="header-left">
      <h2 class="list-title">
        <i class="material-icons header-icon">help</i>
        {{ isEdit ? 'Edit' : 'Create' }} Question
      </h2>
    </div>
    <div class="header-right">
      <a routerLink="/admin/dashboard/questions" class="add-button">
        <span class="material-icons add-icon">arrow_back</span>
        Back to List
      </a>
      <div class="container-behind"></div>
    </div>
  </div>

  <div *ngIf="success" class="alert alert-success">{{ success }}</div>
  <div *ngIf="error" class="alert alert-error">{{ error }}</div>

  <div *ngIf="loading" class="loading-container">
    <div class="loading-spinner"></div>
    <span class="loading-text">Loading...</span>
  </div>

  <form
    *ngIf="!loading"
    (ngSubmit)="saveQuestion()"
    #questionForm="ngForm"
    class="list-content"
  >
    <div class="form-group">
      <label class="form-label" for="title">Title</label>
      <input
        class="form-control"
        [class.invalid]="title.invalid && (title.dirty || title.touched)"
        id="title"
        type="text"
        name="title"
        [(ngModel)]="question.title"
        required
        #title="ngModel"
        placeholder="Question title"
      />
      <div
        *ngIf="title.invalid && (title.dirty || title.touched)"
        class="error-message"
      >
        Title is required
      </div>
    </div>

    <div class="form-group">
      <label class="form-label" for="statement">Statement</label>
      <textarea
        class="form-control"
        [class.invalid]="statement.invalid && (statement.dirty || statement.touched)"
        id="statement"
        name="statement"
        [(ngModel)]="question.statement"
        required
        #statement="ngModel"
        placeholder="Question statement"
        rows="4"
      ></textarea>
      <div
        *ngIf="statement.invalid && (statement.dirty || statement.touched)"
        class="error-message"
      >
        Statement is required
      </div>
    </div>

    <div class="form-group">
      <label class="form-label" for="chapter_id">Chapter</label>
      <select
        class="form-control"
        [class.invalid]="chapter_id.invalid && (chapter_id.dirty || chapter_id.touched)"
        id="chapter_id"
        name="chapter_id"
        [(ngModel)]="question.chapter_id"
        required
        #chapter_id="ngModel"
      >
        <option [ngValue]="null" disabled>Select a chapter</option>
        <option *ngFor="let chapter of chapters" [ngValue]="chapter.id">
          {{ chapter.title || chapter.name }}
        </option>
      </select>
      <div
        *ngIf="chapter_id.invalid && (chapter_id.dirty || chapter_id.touched)"
        class="error-message"
      >
        Chapter is required
      </div>
    </div>

    <div class="actions-container">
      <button
        class="action-button submit-button"
        type="submit"
        [disabled]="questionForm.invalid || loading"
      >
        <span class="material-icons action-icon">{{ isEdit ? 'update' : 'add_circle' }}</span>
        {{ isEdit ? 'Update' : 'Create' }} Question
      </button>
    </div>
  </form>
</div>
