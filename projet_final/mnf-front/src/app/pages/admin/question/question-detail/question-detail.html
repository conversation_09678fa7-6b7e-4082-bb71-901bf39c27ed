<div class="question-detail">
  <div class="list-header border-rad">
    <div class="header-left">
      <h2 class="list-title">
        <i class="material-icons header-icon">help</i>
        Question Details
      </h2>
    </div>
    <div class="header-right">
      <a routerLink="/admin/dashboard/questions" class="add-button">
        <span class="material-icons add-icon">arrow_back</span>
        Back to List
      </a>
      <div *ngIf="question" class="action-buttons">
        <a [routerLink]="['/admin/questions/edit', question.id]" class="edit-button">
          <span class="material-icons">edit</span>
          Edit
        </a>
        <button (click)="deleteQuestion()" class="delete-button">
          <span class="material-icons">delete</span>
          Delete
        </button>
      </div>
      <div class="container-behind"></div>
    </div>
  </div>

  <div *ngIf="error" class="alert alert-error">{{ error }}</div>

  <div *ngIf="loading" class="loading-container">
    <div class="loading-spinner"></div>
    <span class="loading-text">Loading...</span>
  </div>

  <div *ngIf="!loading && question" class="list-content">
    <div class="detail-section">
      <h3 class="section-title">Question Information</h3>
      <div class="detail-grid">
        <div class="detail-item">
          <span class="detail-label">ID:</span>
          <span class="detail-value">{{ question.id }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Title:</span>
          <span class="detail-value">{{ question.title }}</span>
        </div>
        <div class="detail-item full-width">
          <span class="detail-label">Statement:</span>
          <span class="detail-value">{{ question.statement }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Chapter:</span>
          <span class="detail-value" *ngIf="chapter">{{ chapter.title || chapter.name }}</span>
          <span class="detail-value" *ngIf="!chapter">{{ question.chapter_id }}</span>
        </div>
      </div>
    </div>

    <div class="detail-section">
      <h3 class="section-title">Answers</h3>

      <div *ngIf="answers.length === 0" class="empty-state">
        <p class="empty-state-text">No answers found for this question.</p>
        <a [routerLink]="['/admin/answers/create']" [queryParams]="{question_id: question.id}" class="action-button">
          <span class="material-icons">add_circle</span>
          Add Answer
        </a>
      </div>

      <div *ngIf="answers.length > 0" class="table-container">
        <table class="data-table">
          <thead class="table-header">
            <tr>
              <th scope="col" class="table-header-cell">ID</th>
              <th scope="col" class="table-header-cell">Label</th>
              <th scope="col" class="table-header-cell">Text</th>
              <th scope="col" class="table-header-cell">Valid Answer</th>
              <th scope="col" class="table-header-cell">Actions</th>
            </tr>
          </thead>
          <tbody class="table-body">
            <tr *ngFor="let answer of answers" class="table-row">
              <td class="table-cell">{{ answer.id }}</td>
              <td class="table-cell">{{ answer.label }}</td>
              <td class="table-cell">{{ answer.text }}</td>
              <td class="table-cell">{{ answer.valid_answer ? 'Yes' : 'No' }}</td>
              <td class="table-cell">
                <div class="actions-container">
                  <a [routerLink]="['/admin/answers', answer.id]" class="view-button">View</a>
                  <a [routerLink]="['/admin/answers/edit', answer.id]" class="edit-button">Edit</a>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
