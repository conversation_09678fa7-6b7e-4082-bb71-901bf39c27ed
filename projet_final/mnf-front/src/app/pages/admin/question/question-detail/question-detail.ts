import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Question } from '../../../../models/admin/Question';
import { Answer } from '../../../../models/admin/Answer';
import { QuestionService } from '../../../../services/admin/question.service';
import { AnswerService } from '../../../../services/admin/answer.service';
import { ChapterService } from '../../../../services/admin/chapter.service';
import { NotificationService } from '../../../../services/notification.service';
import { Chapter } from '../../../../models/admin/Chapter';

@Component({
  selector: 'app-question-detail',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './question-detail.html',
  styleUrl: '../../list-container/list-container.css'
})
export class QuestionDetailComponent implements OnInit {
  question: Question | null = null;
  answers: Answer[] = [];
  chapter: Chapter | null = null;
  loading = true;
  error = '';

  constructor(
    private questionService: QuestionService,
    private answerService: AnswerService,
    private chapterService: ChapterService,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadQuestion(+id);
    } else {
      this.error = 'Question ID is required';
      this.loading = false;
    }
  }

  loadQuestion(id: number): void {
    this.loading = true;
    this.questionService.getQuestionById(id)
      .then(question => {
        if (question) {
          this.question = question;
          this.loadAnswers(id);
          if (question.chapter_id) {
            this.loadChapter(question.chapter_id);
          } else {
            this.loading = false;
          }
        } else {
          this.error = 'Question not found';
          this.loading = false;
        }
      })
      .catch(error => {
        this.error = 'Failed to load question. Please try again.';
        this.loading = false;
        console.error('Error loading question:', error);
      });
  }

  loadAnswers(questionId: number): void {
    this.answerService.getAnswersByQuestionId(questionId)
      .then(answers => {
        this.answers = answers;
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load answers. Please try again.';
        this.loading = false;
        console.error('Error loading answers:', error);
      });
  }

  loadChapter(chapterId: number): void {
    this.chapterService.getChapterById(chapterId)
      .then(chapter => {
        this.chapter = chapter;
        this.loading = false;
      })
      .catch(error => {
        console.error('Error loading chapter:', error);
        this.loading = false;
      });
  }

  deleteQuestion(): void {
    if (!this.question || !this.question.id) return;

    if (confirm('Are you sure you want to delete this question?')) {
      this.loading = true;
      this.questionService.deleteQuestion(this.question.id)
        .then(success => {
          if (success) {
            this.notificationService.success('Question deleted successfully');
            this.router.navigate(['/admin/dashboard/questions']);
          } else {
            this.error = 'Failed to delete question. Please try again.';
            this.notificationService.error(this.error);
            this.loading = false;
          }
        })
        .catch(error => {
          this.error = 'Failed to delete question. Please try again.';
          this.notificationService.error(this.error);
          this.loading = false;
          console.error('Error deleting question:', error);
        });
    }
  }
}
