import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Promotion } from '../../../../models/admin/Promotion';
import { PromotionService } from '../../../../services/admin/promotion.service';

@Component({
  selector: 'app-promotion-form',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './promotion-form.html',
})
export class PromotionFormComponent implements OnInit {
  promotion: Promotion = {
    name: ''
  };
  isEdit = false;
  loading = false;
  error = '';
  success = '';

  constructor(
    private promotionService: PromotionService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEdit = true;
      this.loadPromotion(+id);
    }
  }

  loadPromotion(id: number): void {
    this.loading = true;
    this.promotionService.getPromotionById(id)
      .then(promotion => {
        if (promotion) {
          this.promotion = promotion;
        } else {
          this.error = 'Promotion not found';
        }
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load promotion. Please try again.';
        this.loading = false;
        console.error('Error loading promotion:', error);
      });
  }

  savePromotion(): void {
    this.loading = true;
    this.error = '';
    this.success = '';

    const save = this.isEdit
      ? this.promotionService.updatePromotion(this.promotion.id!, this.promotion)
      : this.promotionService.createPromotion(this.promotion);

    save
      .then(result => {
        if (result) {
          this.success = `Promotion ${this.isEdit ? 'updated' : 'created'} successfully`;
          if (!this.isEdit) {
            this.promotion = { name: '' };
          }
          setTimeout(() => {
            this.router.navigate(['/admin/promotions']);
          }, 1500);
        } else {
          this.error = `Failed to ${this.isEdit ? 'update' : 'create'} promotion. Please try again.`;
        }
        this.loading = false;
      })
      .catch(error => {
        this.error = `Failed to ${this.isEdit ? 'update' : 'create'} promotion. Please try again.`;
        this.loading = false;
        console.error(`Error ${this.isEdit ? 'updating' : 'creating'} promotion:`, error);
      });
  }
}
