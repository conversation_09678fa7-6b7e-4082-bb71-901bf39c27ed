<div class="promotion-form">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-bold">{{ isEdit ? 'Edit' : 'Create' }} Promotion</h2>
    <a routerLink="/admin/promotions" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
      Back to List
    </a>
  </div>

  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <div *ngIf="success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
    {{ success }}
  </div>

  <div *ngIf="loading" class="text-center py-4">
    <p>Loading...</p>
  </div>

  <form *ngIf="!loading" (ngSubmit)="savePromotion()" #promotionForm="ngForm" class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
    <div class="mb-4">
      <label class="block text-gray-700 text-sm font-bold mb-2" for="name">
        Name
      </label>
      <input
        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
        id="name"
        type="text"
        name="name"
        [(ngModel)]="promotion.name"
        required
        #name="ngModel"
        placeholder="Promotion name">
      <div *ngIf="name.invalid && (name.dirty || name.touched)" class="text-red-500 text-xs italic">
        Name is required
      </div>
    </div>

    <div class="flex items-center justify-between">
      <button
        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        type="submit"
        [disabled]="promotionForm.invalid || loading">
        {{ isEdit ? 'Update' : 'Create' }} Promotion
      </button>
    </div>
  </form>
</div>
