<div class="list-container">
  <div class="list-header border-rad">
    <div class="header-left">
      <h2 class="list-title">
        <i class="material-icons header-icon">book</i>
        {{ isEditMode ? 'Edit Chapter' : 'Create Chapter' }}
      </h2>
    </div>
    <div class="header-right">
      <a routerLink="/admin/dashboard/chapters" class="add-button">
        <span class="material-icons add-icon">arrow_back</span>
        Back to List
      </a>
      <div class="container-behind"></div>
    </div>
  </div>

  <!-- Success message -->
  @if (success) {
  <div class="alert alert-success">{{ success }}</div>
  }

  <!-- Loading indicator -->
  @if (loading) {
  <div class="loading-container">
    <div class="loading-spinner"></div>
    <span class="loading-text" i18n="@@loadingChapter">Loading...</span>
  </div>
  }

  <form [formGroup]="chapterForm" (ngSubmit)="onSubmit()" class="list-content">
    <!-- Title field -->
    <div class="form-group">
      <label for="title" class="form-label" i18n="@@chapterTitleLabel">
        Title *
      </label>
      <input
        type="text"
        id="title"
        formControlName="title"
        class="form-control"
        [class.invalid]="chapterForm.get('title')?.invalid && chapterForm.get('title')?.touched"
      />
      @if (chapterForm.get('title')?.invalid &&
      chapterForm.get('title')?.touched) {
      <p class="error-message" i18n="@@titleRequiredError">
        Title is required and must be at least 3 characters.
      </p>
      }
    </div>

    <!-- Parent field -->
    <div class="form-group">
      <label for="parentName" class="form-label" i18n="@@chapters.parentLabel">
        Parent
      </label>
      <input
        type="text"
        id="parentName"
        formControlName="parentName"
        class="form-control"
        placeholder="Enter parent chapter name (optional)"
      />
      <small class="form-help-text">
        Leave empty if this is a root chapter
      </small>
    </div>

    <!-- Form actions -->
    <div class="actions-container">
      <button
        type="submit"
        [disabled]="chapterForm.invalid || loading"
        class="action-button submit-button"
        i18n="@@saveButton"
      >
        <span class="material-icons action-icon"
          >{{ isEditMode ? 'update' : 'add_circle' }}</span
        >
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </div>
  </form>
</div>
