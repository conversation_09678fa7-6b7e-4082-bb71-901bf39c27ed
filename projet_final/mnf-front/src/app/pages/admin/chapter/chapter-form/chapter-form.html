<div class="chapter-form-container">
  <div class="mb-6">
    <h1 class="text-3xl font-bold" i18n="@@chapterFormTitle">
      {{ isEditMode ? 'Edit Chapter' : 'Create Chapter' }}
    </h1>
    <p class="text-gray-400 mt-2" i18n="@@chapterFormSubtitle">
      {{ isEditMode ? 'Update the chapter information below' : 'Fill in the chapter information below' }}
    </p>
  </div>

  <!-- Error message -->
  @if (error) {
    <div class="bg-red-800 text-white p-4 rounded mb-4">
      {{ error }}
    </div>
  }

  <!-- Success message -->
  @if (success) {
    <div class="bg-green-800 text-white p-4 rounded mb-4">
      {{ success }}
    </div>
  }

  <!-- Loading indicator -->
  @if (loading) {
    <div class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      <span class="ml-3" i18n="@@loadingChapter">Loading...</span>
    </div>
  }

  <form [formGroup]="chapterForm" (ngSubmit)="onSubmit()" class="bg-gray-800 rounded-lg p-6 shadow-lg">
    <!-- Title field -->
    <div class="mb-4">
      <label for="title" class="block text-gray-300 text-sm font-bold mb-2" i18n="@@chapterTitleLabel">
        Title *
      </label>
      <input
        type="text"
        id="title"
        formControlName="title"
        class="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:outline-none focus:border-blue-500"
        [ngClass]="{'border-red-500': chapterForm.get('title')?.invalid && chapterForm.get('title')?.touched}"
      />
      @if (chapterForm.get('title')?.invalid && chapterForm.get('title')?.touched) {
        <p class="text-red-500 text-xs mt-1" i18n="@@titleRequiredError">
          Title is required and must be at least 3 characters.
        </p>
      }
    </div>

    <!-- Path field -->
    <div class="mb-4">
      <label for="path" class="block text-gray-300 text-sm font-bold mb-2" i18n="@@chapterPathLabel">
        Path *
      </label>
      <input
        type="text"
        id="path"
        formControlName="path"
        class="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:outline-none focus:border-blue-500"
        [ngClass]="{'border-red-500': chapterForm.get('path')?.invalid && chapterForm.get('path')?.touched}"
      />
      @if (chapterForm.get('path')?.invalid && chapterForm.get('path')?.touched) {
        <p class="text-red-500 text-xs mt-1" i18n="@@pathRequiredError">
          Path is required.
        </p>
      }
    </div>

    <!-- Parent Path field -->
    <div class="mb-4">
      <label for="parentPath" class="block text-gray-300 text-sm font-bold mb-2" i18n="@@parentPathLabel">
        Parent Path
      </label>
      <input
        type="text"
        id="parentPath"
        formControlName="parentPath"
        class="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:outline-none focus:border-blue-500"
      />
    </div>

    <!-- Content field -->
    <div class="mb-6">
      <label for="content" class="block text-gray-300 text-sm font-bold mb-2" i18n="@@contentLabel">
        Content
      </label>
      <textarea
        id="content"
        formControlName="content"
        rows="6"
        class="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:outline-none focus:border-blue-500"
      ></textarea>
    </div>

    <!-- Form actions -->
    <div class="flex justify-between">
      <button
        type="button"
        routerLink="/admin/chapters"
        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-200"
        i18n="@@cancelButton"
      >
        Cancel
      </button>
      <button
        type="submit"
        [disabled]="chapterForm.invalid || loading"
        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        i18n="@@saveButton"
      >
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </div>
  </form>
</div>
