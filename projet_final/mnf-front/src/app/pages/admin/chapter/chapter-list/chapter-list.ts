import { Component, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ChapterService } from '../../../../services/admin/chapter.service';
import { Chapter } from '../../../../models/admin/Chapter';

@Component({
  selector: 'app-chapter-list',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './chapter-list.html',
  styleUrl: './chapter-list.css'
})
export class ChapterListComponent implements OnInit {
  chapters: Chapter[] = [];
  loading = true;
  error = '';

  constructor(private chapterService: ChapterService) {}

  ngOnInit(): void {
    this.loadChapters();
  }

  loadChapters(): void {
    this.loading = true;
    this.error = '';

    this.chapterService.getChapters()
      .then(chapters => {
        this.chapters = chapters;
        this.loading = false;
      })
      .catch(err => {
        this.error = 'Failed to load chapters. Please try again.';
        this.loading = false;
        console.error('Error loading chapters:', err);
      });
  }

  deleteChapter(id: number): void {
    if (confirm('Are you sure you want to delete this chapter?')) {
      this.chapterService.deleteChapter(id)
        .then(success => {
          if (success) {
            this.chapters = this.chapters.filter(chapter => chapter.id !== id);
          } else {
            this.error = 'Failed to delete chapter. Please try again.';
          }
        })
        .catch(err => {
          this.error = 'Failed to delete chapter. Please try again.';
          console.error('Error deleting chapter:', err);
        });
    }
  }
}
