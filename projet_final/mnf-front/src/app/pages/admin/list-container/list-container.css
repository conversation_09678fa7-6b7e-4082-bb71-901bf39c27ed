.list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

/* List content */
.list-content {
  background-color: #cd7a88;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding: 4px;
}

/* Enhanced Table styles */
.table-container {
  background-color: #f0dddb;
  border-radius: 0.5rem;
}

/* Enhanced Header styles */
.list-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.header-left {
  display: flex;
  width: 100%;
  flex-direction: row;
  align-content: center;
  justify-content: space-between;
  background-color: #cd7a88;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.list-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #ffffff;
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
  padding-left: 20px;
}

.header-icon {
  margin-right: 0.75rem;
  color: #3b82f6;
  font-size: 1.5rem;
  display: none; /* Hide the icon */
}

.item-count {
  color: #9ca3af;
  font-size: 0.875rem;
}

.add-button {
  background-color: #cd7a88;
  color: white;
  text-wrap: nowrap;
  font-weight: 600;
  padding: 0.75rem 1.25rem;
  border-radius: 16px;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-left: 7px;
  height: 21px;
  z-index: 11;
}

.header-right {
  display: flex;
  position: relative;
  align-items: center;
  gap: 1rem;
}

/* Search styles */
.search-container {
  position: relative;
  justify-content: center;
  align-content: center;
  margin-right: 20px;
}

.search-input {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  color: var(--text-color);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  width: 250px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #cd7a88;
  box-shadow: 0 0 0 2px rgba(205, 122, 136, 0.3);
}

.search-input::placeholder {
  color: #bb9797;
}
.container-behind {
  position: absolute;
  width: 30px;
  height: 30px;
  bottom: -6px;
  background-color: #cd7a88;
  overflow: hidden;
}

.container-behind::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 30px;
  background: #fffaf3;
  border-bottom-left-radius: 70%;
  z-index: 10;
}

.add-button:hover {
  background-color: var(--secondary-dark-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.add-icon {
  margin-right: 0.5rem;
}

/* Enhanced Error message */
.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.error-message-container {
  background-color: #991b1b;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.error-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

/* Enhanced Loading indicator */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
  background-color: #1f2937;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.loading-spinner {
  height: 4rem;
  width: 4rem;
  border-radius: 50%;
  border-top: 3px solid #3b82f6;
  border-bottom: 3px solid #3b82f6;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #d1d5db;
  font-size: 1.125rem;
  font-weight: 500;
}

/* Enhanced Empty state */
.empty-state {
  background-color: #cd7a88;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.empty-icon {
  font-size: 3rem;
  color: #fff;
  margin-bottom: 1.5rem;
}

.empty-state-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 0.5rem;
}

.empty-state-subtext {
  font-size: 1.125rem;
  color: #e5e7eb;
  margin-bottom: 2rem;
}

.empty-state-button {
  display: inline-flex;
  align-items: center;
  background-color: #3b82f6;
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.empty-state-button:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.empty-state-button i {
  margin-right: 0.5rem;
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

h2 {
  margin-top: 12px;
  margin-bottom: 12px;
}

.table-header-cell {
  padding: 1rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: black;
  border-bottom: 2px solid #cd7a88;
}

.header-cell-content {
  align-items: center;
  text-align: center;
  justify-content: space-between;
}

.sort-icon {
  color: black;
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

.actions-header,
.delete-header,
.edit-header,
.checkbox-header {
  text-align: center;
}

.table-body {
  background-color: #f0dddb;
}

.table-row {
  border-bottom: 1px solid #cd7a88;
  transition: all 0.2s ease;
  background-color: #f0dddb;
}

.table-row:nth-child(even) {
  background-color: #f8e8e6;
}

.table-row:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.selected-row {
  background-color: #cd7a8875 !important;
}

.selected-row:nth-child(even) {
  background-color: #cd7a8857 !important;
}

.select-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #cd7a88;
  border: 1px solid #f0dddb;
  border-radius: 3px;
}

/* Custom row styles for different tabs */
.table-row-interns {
  border-left: 3px solid #3b82f6;
}

.table-row-promotions {
  border-left: 3px solid #8b5cf6;
}

.table-row-chapters {
  border-left: 3px solid #10b981;
}

.table-row-questions {
  border-left: 3px solid #f59e0b;
}

.table-row-answers {
  border-left: 3px solid #ef4444;
}

.table-cell {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  color: black;
  text-align: center;
  vertical-align: middle;
}

.highlight-cell {
  font-weight: 600;
}

.id-badge {
  display: inline-block;
  background-color: #f0dddb;
  color: black;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid #cd7a88;
}

.date-value {
  color: black;
  font-style: italic;
}

.first_name-value,
.last_name-value,
.name-value,
.title-value,
.text-value {
  font-weight: 500;
  color: black;
}

.actions-cell,
.delete-cell,
.edit-cell,
.checkbox-cell {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

/* Enhanced Action buttons */
.actions-container {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  width: 36px;
  height: 36px;
}

.action-icon {
  margin-right: 0;
}

.view-button {
  background-color: #1e40af;
  color: white;
}

.view-button:hover {
  background-color: #1d4ed8;
}

.edit-button {
  color: #cd7a88;
  background-color: transparent;
  border: none;
}

.edit-button:hover {
  color: white;
  background-color: #cd7a88;
}

.delete-button {
  color: #cd7a88;
  border: none;
  background-color: transparent;
  cursor: pointer;
  justify-content: center;
}

.delete-button:hover {
  color: #864752;
}

/* This line is no longer needed since we set margin-right: 0 for all action-icons */

/* Enhanced Pagination */
.pagination-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  background-color: #f0dddb;
  padding: 1rem 1.5rem;
  border-top: 1px solid #cd7a88;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  margin-bottom: 0;
  gap: 1rem;
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

.pagination-info {
  color: black;
  font-size: 0.875rem;
  font-weight: 500;
}

.enhanced-pagination {
  display: flex;
  align-items: center;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1rem;
}

.page-size-selector label {
  color: black;
  font-size: 0.875rem;
  white-space: nowrap;
}

.page-size-selector select {
  background-color: white;
  color: black;
  border: 1px solid #cd7a88;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 70px;
}

.page-size-selector select:hover {
  border-color: #cd7a88;
}

.page-size-selector select:focus {
  outline: none;
  border-color: #cd7a88;
  box-shadow: 0 0 0 2px rgba(205, 122, 136, 0.3);
}

/* Form styles */
.form-label {
  display: block;
  font-weight: 500;
  color: var(--secondary-dark-color);
  text-align: end;
}

.form-group {
  background-color: var(--card-background);
  display: grid;
  grid-template-columns: 2fr 3fr 1fr;
  padding: 20px;
  align-items: center;
  gap: 20px;
}

.form-group:nth-child(even) {
  background-color: #f8e8e6;
}

.form-group:first-child {
  border-radius: 5px 5px 0 0;
}

.form-group:last-child {
  border-radius: 0 0 5px 5px;
}

.form-control {
  padding: 0.75rem;
  border: 2px solid #cd7a88;
  border-radius: 0.25rem;
  background-color: white;
  color: var(--text-color);
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #cd7a88;
  box-shadow: 0 0 0 2px rgba(205, 122, 136, 0.3);
}

.form-control.invalid {
  border-color: #ef4444;
}

.submit-button {
  background-color: var(--background-color);
  margin-top: 5px;
  color: var(--secondary-color);
  font-weight: 600;
  font-size: 16px;
  padding: 10px;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  width: auto !important;
  height: auto !important;
}

.submit-button .action-icon {
  margin-right: 5px;
}

.submit-button:hover {
  background-color: var(--secondary-dark-color);
  color: white;
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mat-mdc-select {
  width: auto;
}

/* Alert messages */
.alert {
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

.alert-error {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  color: #b91c1c;
}

.alert-success {
  background-color: #d1fae5;
  border: 1px solid #10b981;
  color: #047857;
}

/* Pagination component styles */
.pagination-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  border: 1px solid #cd7a88;
  background-color: white;
  color: black;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.25rem;
}

.pagination-button:hover {
  background-color: #f0dddb;
  border-color: #cd7a88;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-page-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  border: 1px solid #cd7a88;
  background-color: white;
  color: black;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.25rem;
  margin: 0 0.125rem;
}

.pagination-page-button:hover {
  background-color: #f0dddb;
  border-color: #cd7a88;
}

.pagination-page-button-active {
  background-color: #cd7a88;
  color: white;
  border-color: #cd7a88;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
