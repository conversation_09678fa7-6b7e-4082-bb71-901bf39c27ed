import { Component, OnInit, ViewChild, AfterViewInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { RouterLink, Router, ActivatedRoute } from "@angular/router";
import { PaginationComponent } from "../../../components/shared/pagination/pagination";
import { InternListComponent } from "../../../components/admin/intern/intern-list/intern-list";
import { PromotionListComponent } from "../../../components/admin/promotion/promotion-list/promotion-list";
import { ChapterListComponent } from "../../../components/admin/chapter/chapter-list/chapter-list";
import { QuestionListComponent } from "../../../components/admin/question/question-list/question-list";
import { AnswerListComponent } from "../../../components/admin/answer/answer-list/answer-list";
import { AdminListComponent } from "../../../components/admin/admin/admin-list/admin-list";
import { UserListComponent } from "../../../components/admin/user/user-list/user-list";
import { FavoriteButtonComponent } from "../../../components/user/favorites/favorite-button";
import { FavoritesListComponent } from "../../../components/admin/favorites/favorites-list/favorites-list";
import { InternService } from "../../../services/admin/intern.service";
import { PromotionService } from "../../../services/admin/promotion.service";
import { ChapterService } from "../../../services/admin/chapter.service";
import { QuestionService } from "../../../services/admin/question.service";
import { AnswerService } from "../../../services/admin/answer.service";
import { AdminService } from "../../../services/admin/admin.service";
import { UserService } from "../../../services/admin/user.service";
import { FavoritesService } from "../../../services/favorites/favorites.service";
import { AuthService } from "../../../services/auth.service";
import { NotificationService } from "../../../services/notification.service";

interface TableColumn {
  key: string;
  label: string;
  format?: string;
}

@Component({
  selector: "app-list-container",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterLink,
    PaginationComponent,
    InternListComponent,
    PromotionListComponent,
    ChapterListComponent,
    QuestionListComponent,
    AnswerListComponent,
    AdminListComponent,
    UserListComponent,
    FavoriteButtonComponent,
    FavoritesListComponent,
  ],
  templateUrl: "./list-container.html",
  styleUrl: "./list-container.css",
})
export class ListContainerComponent implements OnInit, AfterViewInit {
  activeTab: string = "interns";

  // Common UI properties
  loading: boolean = true;
  error: string = "";
  items: any[] = [];
  selectedItems: number[] = [];

  // Make Math available in the template
  Math = Math;

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 10;
  totalItems: number = 0;
  totalPages: number = 0;

  // ViewChild references to list components
  @ViewChild("internList") internList?: InternListComponent;
  @ViewChild("promotionList") promotionList?: PromotionListComponent;
  @ViewChild("chapterList") chapterList?: ChapterListComponent;
  @ViewChild("questionList") questionList?: QuestionListComponent;
  @ViewChild("answerList") answerList?: AnswerListComponent;
  @ViewChild("adminList") adminList?: AdminListComponent;
  @ViewChild("userList") userList?: UserListComponent;
  @ViewChild("favoritesList") favoritesList?: FavoritesListComponent;

  // Search term for filtering
  searchTerm: string = "";

  constructor(
    private internService: InternService,
    private promotionService: PromotionService,
    private chapterService: ChapterService,
    private questionService: QuestionService,
    private answerService: AnswerService,
    private adminService: AdminService,
    private userService: UserService,
    private favoritesService: FavoritesService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    // Reset dataLoaded flag when component is initialized
    this.dataLoaded = false;

    // Determine the active tab based on the current route
    const url = this.router.url;
    const urlParts = url.split("/");

    if (urlParts.length >= 4) {
      const tab = urlParts[3];
      this.activeTab = tab;
    } else {
      this.activeTab = "interns";
    }
    // Reset pagination
    this.currentPage = 1;
  }

  ngAfterViewInit(): void {
    // Load data after view is initialized, with a small delay to ensure the view is ready
    setTimeout(() => {
      this.loadData();
    }, 0);
  }

  setActiveTab(tab: string): void {
    // Only reset dataLoaded if the tab actually changes
    if (this.activeTab !== tab) {
      this.activeTab = tab;
      // Reset pagination when changing tabs
      this.currentPage = 1;
      // Reset dataLoaded flag to ensure data is loaded for the new tab
      this.dataLoaded = false;
      // Data will be loaded in ngAfterViewInit
    }
  }

  // Track if data is already loaded for the current tab
  private dataLoaded = false;

  loadData(): void {
    // If data is already loaded for this tab, don't reload it
    if (this.dataLoaded) {
      console.log(
        `Data already loaded for tab ${this.activeTab}, skipping loadData()`
      );
      return;
    }

    console.log(
      `Loading data for tab ${this.activeTab}, currentPage: ${this.currentPage}, pageSize: ${this.pageSize}`
    );
    this.loading = true;
    this.error = "";
    // Don't reset items array here, only set loading state
    this.dataLoaded = true;

    switch (this.activeTab) {
      case "interns":
        // First get the total count from the /total endpoint
        this.internService
          .getInternsTotal()
          .then((total) => {
            this.totalItems = total;
            this.totalPages = Math.ceil(total / this.pageSize);

            // Then get the paginated data
            // Convert from 1-based (UI) to 0-based (API) pagination
            const apiPageInterns = this.currentPage - 1;
            return this.internService.getInternsPaginated(
              this.searchTerm,
              apiPageInterns,
              this.pageSize
            );
          })
          .then((result) => {
            this.items = result.content;
            this.totalItems = result.totalElements;
            this.totalPages = result.totalPages;
            this.loading = false;
          })
          .catch((error) => {
            this.error = "Failed to load interns. Please try again.";
            this.loading = false;
            this.dataLoaded = false;
            console.error("Error loading interns:", error);
          });
        break;

      case "promotions":
        // First get the total count from the /total endpoint
        this.promotionService
          .getPromotionsTotal()
          .then((total) => {
            this.totalItems = total;
            this.totalPages = Math.ceil(total / this.pageSize);

            // Then get the paginated data
            // Convert from 1-based (UI) to 0-based (API) pagination
            const apiPagePromotions = this.currentPage - 1;
            return this.promotionService.getPromotions(
              this.searchTerm,
              apiPagePromotions,
              this.pageSize
            );
          })
          .then((result) => {
            this.items = result.content;
            this.totalItems = result.totalElements;
            this.totalPages = result.totalPages;
            this.loading = false;
          })
          .catch((error) => {
            this.error = "Failed to load promotions. Please try again.";
            this.loading = false;
            this.dataLoaded = false;
            console.error("Error loading promotions:", error);
          });
        break;

      case "chapters":
        // Convert from 1-based (UI) to 0-based (API) pagination
        const apiPageChapters = this.currentPage - 1;

        // Get paginated data and use totalElements from the response
        console.log(
          `Loading chapters for page ${this.currentPage} (API page ${apiPageChapters}), pageSize: ${this.pageSize}, search: "${this.searchTerm}"`
        );
        this.chapterService
          .getChaptersPaginated(this.searchTerm, apiPageChapters, this.pageSize)
          .then((result) => {
            console.log(
              `Received ${result.content.length} chapters for page ${this.currentPage}:`,
              result.content
            );
            console.log('First chapter parentName:', result.content[0]?.parentName);

            // Log the items array before updating it
            console.log(
              `Before update: items array has ${this.items.length} items`
            );

            this.items = result.content;
            this.totalItems = result.totalElements;
            this.totalPages = result.totalPages;

            // Log the items array after updating it
            console.log(
              `After update: items array has ${this.items.length} items:`,
              this.items
            );
            console.log(
              `Updated items array (${this.items.length} items), totalItems: ${this.totalItems}, totalPages: ${this.totalPages}`
            );

            this.loading = false;
          })
          .catch((error) => {
            this.error = "Failed to load chapters. Please try again.";
            this.loading = false;
            this.dataLoaded = false;
            console.error("Error loading chapters:", error);
          });
        break;

      case "questions":
        // First get the total count from the /total endpoint
        this.questionService
          .getQuestionsTotal()
          .then((total) => {
            this.totalItems = total;
            this.totalPages = Math.ceil(total / this.pageSize);
            console.log(`Questions total count from /total endpoint: ${total}`);

            // Then get the paginated data
            // Convert from 1-based (UI) to 0-based (API) pagination
            const apiPageQuestions = this.currentPage - 1;
            return this.questionService.getQuestions(
              this.searchTerm,
              apiPageQuestions,
              this.pageSize
            );
          })
          .then((result) => {
            this.items = result.content;
            // Don't overwrite totalItems and totalPages with values from paginated response
            // to ensure we show the correct total count from the /total endpoint
            this.loading = false;
          })
          .catch((error) => {
            this.error = "Failed to load questions. Please try again.";
            this.loading = false;
            this.dataLoaded = false;
            console.error("Error loading questions:", error);
          });
        break;

      case "answers":
        // First get the total count from the /total endpoint
        this.answerService
          .getAnswersTotal()
          .then((total) => {
            this.totalItems = total;
            this.totalPages = Math.ceil(total / this.pageSize);
            console.log(`Answers total count from /total endpoint: ${total}`);

            // Then get the paginated data
            // Convert from 1-based (UI) to 0-based (API) pagination
            const apiPageAnswers = this.currentPage - 1;
            return this.answerService.getAnswers(
              this.searchTerm,
              apiPageAnswers,
              this.pageSize
            );
          })
          .then((result) => {
            this.items = result.content;
            // Don't overwrite totalItems and totalPages with values from paginated response
            // to ensure we show the correct total count from the /total endpoint
            this.loading = false;
          })
          .catch((error) => {
            this.error = "Failed to load answers. Please try again.";
            this.loading = false;
            this.dataLoaded = false;
            console.error("Error loading answers:", error);
          });
        break;

      case "admins":
        this.adminService
          .getAdmins()
          .then((admins) => {
            this.items = admins;
            this.calculatePagination();
            this.loading = false;
          })
          .catch((error) => {
            this.error = "Failed to load admins. Please try again.";
            this.loading = false;
            this.dataLoaded = false;
            console.error("Error loading admins:", error);
          });
        break;

      case "users":
        this.userService
          .getUsers()
          .then((users) => {
            this.items = users;
            this.calculatePagination();
            this.loading = false;
          })
          .catch((error) => {
            this.error = "Failed to load users. Please try again.";
            this.loading = false;
            this.dataLoaded = false;
            console.error("Error loading users:", error);
          });
        break;

      case "favorites":
        const currentUser = this.authService.currentUserValue;
        if (currentUser) {
          this.favoritesService
            .getFavorites(currentUser.id)
            .then((favorites) => {
              this.items = favorites;
              this.calculatePagination();
              this.loading = false;
            })
            .catch((error) => {
              this.error = "Failed to load favorites. Please try again.";
              this.loading = false;
              this.dataLoaded = false;
              console.error("Error loading favorites:", error);
            });
        } else {
          this.error = "You must be logged in to view favorites.";
          this.loading = false;
          this.dataLoaded = false;
        }
        break;
    }
  }

  calculatePagination(): void {
    this.totalItems = this.items.length;
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
  }

  get paginatedItems(): any[] {
    // For tabs that use server-side pagination with paginated API responses, return items directly
    if (
      this.activeTab === "promotions" ||
      this.activeTab === "questions" ||
      this.activeTab === "answers" ||
      this.activeTab === "interns" ||
      this.activeTab === "chapters"
    ) {
      return this.items;
    }

    // For all other tabs, apply client-side pagination
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    const paginatedItems = this.items.slice(startIndex, endIndex);
    console.log(
      `paginatedItems getter for client-side paginated tab ${this.activeTab}, currentPage: ${this.currentPage}, slicing ${this.items.length} items from ${startIndex} to ${endIndex}, returning ${paginatedItems.length} items`
    );
    return paginatedItems;
  }

  onPageChange(page: number): void {
    console.log(`Page changed from ${this.currentPage} to ${page}`);
    this.currentPage = page;
    // Reload data when page changes
    this.dataLoaded = false;
    console.log(`Setting dataLoaded to false and calling loadData()`);

    // Add more detailed logging for debugging
    console.log(
      `onPageChange: activeTab=${this.activeTab}, currentPage=${this.currentPage}, pageSize=${this.pageSize}, searchTerm="${this.searchTerm}"`
    );

    this.loadData();
  }

  // Methods to update pagination from child components
  updatePagination(totalItems: number, totalPages: number): void {
    this.totalItems = totalItems;
    this.totalPages = totalPages;
  }

  // Handle search term changes
  onSearchChange(term: string): void {
    this.searchTerm = term;
    // Reset to first page when search term changes
    this.currentPage = 1;
    // Reload data when search term changes
    this.dataLoaded = false;
    this.loadData();
  }

  // Dynamic UI methods
  getActiveTabTitle(): string {
    switch (this.activeTab) {
      case "interns":
        return "Interns";
      case "promotions":
        return "Promotions";
      case "chapters":
        return "Chapters";
      case "questions":
        return "Questions";
      case "answers":
        return "Answers";
      case "admins":
        return "Admins";
      case "users":
        return "Users";
      case "favorites":
        return "Favorites";
      default:
        return "";
    }
  }

  getSingularTabName(): string {
    switch (this.activeTab) {
      case "interns":
        return "intern";
      case "promotions":
        return "promotion";
      case "chapters":
        return "chapter";
      case "questions":
        return "question";
      case "answers":
        return "answer";
      case "admins":
        return "admin";
      case "users":
        return "user";
      case "favorites":
        return "favorite";
      default:
        return "";
    }
  }

  getAddButtonText(): string {
    return `Ajouter`;
  }

  getAddButtonLink(): string {
    return `/admin/${this.activeTab}/create`;
  }

  getTableColumns(): TableColumn[] {
    switch (this.activeTab) {
      case "interns":
        return [
          { key: "id", label: "ID" },
          { key: "first_name", label: "First Name" },
          { key: "last_name", label: "Last Name" },
          { key: "arrival", label: "Arrival", format: "date" },
          { key: "formation_over", label: "Formation Over", format: "date" },
          { key: "promotion_name", label: "Promotion" },
        ];
      case "promotions":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
        ];
      case "chapters":
        return [
          { key: "id", label: "ID" },
          { key: "title", label: "Title" },
          { key: "parentName", label: "Parent" },
        ];
      case "questions":
        return [
          { key: "id", label: "ID" },
          { key: "title", label: "Title" },
          { key: "statement", label: "Question" },
        ];
      case "answers":
        return [
          { key: "id", label: "ID" },
          { key: "label", label: "Label" },
          { key: "text", label: "Answer" },
          { key: "valid_answer", label: "Valid Answer" },
          { key: "question_id", label: "Question ID" },
        ];
      case "admins":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "email", label: "Email" },
        ];
      case "users":
        return [
          { key: "id", label: "ID" },
          { key: "name", label: "Name" },
          { key: "email", label: "Email" },
          { key: "role", label: "Role" },
        ];
      case "favorites":
        return [
          { key: "id", label: "ID" },
          { key: "favoriteUserName", label: "Name" },
          { key: "favoriteUserEmail", label: "Email" },
        ];
      default:
        return [];
    }
  }

  getItemProperty(item: any, key: string): any {
    if (key === "promotion_id" && this.activeTab === "interns") {
      return this.getPromotionName(item.promotion_id);
    }
    if (key === "valid_answer" && this.activeTab === "answers") {
      return item[key] ? "Yes" : "No";
    }
    if (key === "parentName" && this.activeTab === "chapters") {
      return item[key] || "-";
    }
    return item[key];
  }

  getPromotionName(promotionId: number | null): string {
    if (promotionId === null) return "None";
    return "AAAAAAAAAAAAAAAAAAAAA";
  }

  getViewLink(): string {
    return `/admin/${this.activeTab}`;
  }

  getEditLink(): string {
    return `/admin/${this.activeTab}/edit`;
  }

  getHeaderIcon(): string {
    switch (this.activeTab) {
      case "interns":
        return "fas fa-user-graduate";
      case "promotions":
        return "fas fa-users";
      case "chapters":
        return "fas fa-book";
      case "questions":
        return "fas fa-question-circle";
      case "answers":
        return "fas fa-comment-dots";
      case "admins":
        return "fas fa-user-shield";
      case "users":
        return "fas fa-user";
      case "favorites":
        return "fas fa-star";
      default:
        return "fas fa-list";
    }
  }

  onPageSizeChange(): void {
    // Convert pageSize to a number to ensure proper calculations
    this.pageSize = Number(this.pageSize);
    this.currentPage = 1; // Reset to first page when changing page size
    this.calculatePagination();

    // Reset dataLoaded flag to ensure data is reloaded
    this.dataLoaded = false;

    // Reload data to ensure child components are updated with the new page size
    this.loadData();
  }

  // Toggle item selection
  toggleSelection(id: number): void {
    const index = this.selectedItems.indexOf(id);
    if (index === -1) {
      this.selectedItems.push(id);
    } else {
      this.selectedItems.splice(index, 1);
    }
  }

  // Check if an item is selected
  isSelected(id: number): boolean {
    return this.selectedItems.includes(id);
  }

  // Delete selected items
  deleteSelectedItems(): void {
    if (this.selectedItems.length === 0) {
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete ${
          this.selectedItems.length
        } ${this.getActiveTabTitle().toLowerCase()}?`
      )
    ) {
      return;
    }

    this.loading = true;

    // Handle promotions and answers separately due to different return type
    if (this.activeTab === "promotions" || this.activeTab === "answers") {
      const deletePromises: Promise<{
        success: boolean;
        errorMessage?: string;
      }>[] = [];

      for (const id of this.selectedItems) {
        if (this.activeTab === "promotions") {
          deletePromises.push(this.promotionService.deletePromotion(id));
        } else if (this.activeTab === "answers") {
          deletePromises.push(this.answerService.deleteAnswer(id));
        }
      }

      Promise.all(deletePromises)
        .then((results) => {
          const failedResults = results.filter((result) => !result.success);

          if (failedResults.length === 0) {
            // All deletions were successful
            this.dataLoaded = false;
            this.items = this.items.filter(
              (item) => !this.selectedItems.includes(item.id)
            );
            this.selectedItems = [];
            this.calculatePagination();
            this.notificationService.success(
              `${this.getActiveTabTitle()} deleted successfully`
            );
          } else {
            // Some deletions failed
            const errorMessages = failedResults
              .map((result) => result.errorMessage)
              .filter((message): message is string => !!message);

            // If there are constraint violations, show them
            if (errorMessages.length > 0) {
              // Use the first error message (they're likely all the same for constraint violations)
              this.error = errorMessages[0];
              this.notificationService.error(this.error);
            } else {
              this.error = `Failed to delete some ${this.getActiveTabTitle().toLowerCase()}. Please try again.`;
              this.notificationService.error(this.error);
            }
          }
          this.loading = false;
        })
        .catch((error) => {
          this.error = `Failed to delete ${this.getActiveTabTitle().toLowerCase()}. Please try again.`;
          this.notificationService.error(this.error);
          this.loading = false;
          console.error(
            `Error deleting ${this.getActiveTabTitle().toLowerCase()}:`,
            error
          );
        });

      return; // Exit early since we've handled these types separately
    }

    // Handle other types of items
    const deletePromises: Promise<boolean>[] = [];

    for (const id of this.selectedItems) {
      switch (this.activeTab) {
        case "interns":
          deletePromises.push(this.internService.deleteIntern(id));
          break;
        case "chapters":
          deletePromises.push(this.chapterService.deleteChapter(id));
          break;
        case "questions":
          deletePromises.push(this.questionService.deleteQuestion(id));
          break;
          // Answers are now handled separately above
          break;
        case "admins":
          deletePromises.push(this.adminService.deleteAdmin(id));
          break;
        case "users":
          deletePromises.push(this.userService.deleteUser(id));
          break;
        case "favorites":
          deletePromises.push(this.favoritesService.removeFavorite(id));
          break;
      }
    }

    Promise.all(deletePromises)
      .then((results) => {
        const allSuccessful = results.every((result) => result === true);
        if (allSuccessful) {
          // Reset dataLoaded flag to ensure data is reloaded next time
          this.dataLoaded = false;

          // Update items list by filtering out deleted items
          this.items = this.items.filter(
            (item) => !this.selectedItems.includes(item.id)
          );
          this.selectedItems = [];
          this.calculatePagination();
          this.notificationService.success(
            `${this.getActiveTabTitle()} deleted successfully`
          );
        } else {
          this.error = `Failed to delete some ${this.getActiveTabTitle().toLowerCase()}. Please try again.`;
          this.notificationService.error(this.error);
        }
        this.loading = false;
      })
      .catch((error) => {
        this.error = `Failed to delete ${this.getActiveTabTitle().toLowerCase()}. Please try again.`;
        this.notificationService.error(this.error);
        this.loading = false;
        console.error(
          `Error deleting ${this.getActiveTabTitle().toLowerCase()}:`,
          error
        );
      });
  }

  // Edit item
  editItem(id: number): void {
    this.router.navigate([this.getEditLink(), id]);
  }

  deleteItem(id: number): void {
    if (
      !confirm(
        `Are you sure you want to delete this ${this.getSingularTabName()}?`
      )
    ) {
      return;
    }

    this.loading = true;

    switch (this.activeTab) {
      case "interns":
        this.internService
          .deleteIntern(id)
          .then((success) => {
            if (success) {
              // Reset dataLoaded flag to ensure data is reloaded next time
              this.dataLoaded = false;
              this.items = this.items.filter((item) => item.id !== id);
              this.calculatePagination();
              this.notificationService.success("Intern deleted successfully");
            } else {
              this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
              this.notificationService.error(this.error);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
            this.notificationService.error(this.error);
            this.loading = false;
            console.error(
              `Error deleting ${this.getSingularTabName()}:`,
              error
            );
          });
        break;

      case "promotions":
        this.promotionService
          .deletePromotion(id)
          .then((result) => {
            if (result.success) {
              // Reset dataLoaded flag to ensure data is reloaded next time
              this.dataLoaded = false;
              this.items = this.items.filter((item) => item.id !== id);
              this.calculatePagination();
              this.notificationService.success(
                "Promotion deleted successfully"
              );
            } else {
              this.error =
                result.errorMessage ||
                `Failed to delete ${this.getSingularTabName()}. Please try again.`;
              this.notificationService.error(this.error);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
            this.notificationService.error(this.error);
            this.loading = false;
            console.error(
              `Error deleting ${this.getSingularTabName()}:`,
              error
            );
          });
        break;

      case "chapters":
        this.chapterService
          .deleteChapter(id)
          .then((success) => {
            if (success) {
              // Reset dataLoaded flag to ensure data is reloaded next time
              this.dataLoaded = false;
              this.items = this.items.filter((item) => item.id !== id);
              this.calculatePagination();
              this.notificationService.success("Chapter deleted successfully");
            } else {
              this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
              this.notificationService.error(this.error);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
            this.notificationService.error(this.error);
            this.loading = false;
            console.error(
              `Error deleting ${this.getSingularTabName()}:`,
              error
            );
          });
        break;

      case "questions":
        this.questionService
          .deleteQuestion(id)
          .then((success) => {
            if (success) {
              // Reset dataLoaded flag to ensure data is reloaded next time
              this.dataLoaded = false;
              this.items = this.items.filter((item) => item.id !== id);
              this.calculatePagination();
              this.notificationService.success("Question deleted successfully");
            } else {
              this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
              this.notificationService.error(this.error);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
            this.notificationService.error(this.error);
            this.loading = false;
            console.error(
              `Error deleting ${this.getSingularTabName()}:`,
              error
            );
          });
        break;

      case "answers":
        this.answerService
          .deleteAnswer(id)
          .then((result) => {
            if (result.success) {
              // Reset dataLoaded flag to ensure data is reloaded next time
              this.dataLoaded = false;
              this.items = this.items.filter((item) => item.id !== id);
              this.calculatePagination();
              this.notificationService.success("Answer deleted successfully");
            } else {
              this.error =
                result.errorMessage ||
                `Failed to delete ${this.getSingularTabName()}. Please try again.`;
              this.notificationService.error(this.error);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
            this.notificationService.error(this.error);
            this.loading = false;
            console.error(
              `Error deleting ${this.getSingularTabName()}:`,
              error
            );
          });
        break;

      case "admins":
        this.adminService
          .deleteAdmin(id)
          .then((success) => {
            if (success) {
              // Reset dataLoaded flag to ensure data is reloaded next time
              this.dataLoaded = false;
              this.items = this.items.filter((item) => item.id !== id);
              this.calculatePagination();
              this.notificationService.success("Admin deleted successfully");
            } else {
              this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
              this.notificationService.error(this.error);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
            this.notificationService.error(this.error);
            this.loading = false;
            console.error(
              `Error deleting ${this.getSingularTabName()}:`,
              error
            );
          });
        break;

      case "users":
        this.userService
          .deleteUser(id)
          .then((success) => {
            if (success) {
              // Reset dataLoaded flag to ensure data is reloaded next time
              this.dataLoaded = false;
              this.items = this.items.filter((item) => item.id !== id);
              this.calculatePagination();
              this.notificationService.success("User deleted successfully");
            } else {
              this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
              this.notificationService.error(this.error);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
            this.notificationService.error(this.error);
            this.loading = false;
            console.error(
              `Error deleting ${this.getSingularTabName()}:`,
              error
            );
          });
        break;

      case "favorites":
        this.favoritesService
          .removeFavorite(id)
          .then((success) => {
            if (success) {
              // Reset dataLoaded flag to ensure data is reloaded next time
              this.dataLoaded = false;
              this.items = this.items.filter((item) => item.id !== id);
              this.calculatePagination();
              this.notificationService.success("Favorite deleted successfully");
            } else {
              this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
              this.notificationService.error(this.error);
            }
            this.loading = false;
          })
          .catch((error) => {
            this.error = `Failed to delete ${this.getSingularTabName()}. Please try again.`;
            this.notificationService.error(this.error);
            this.loading = false;
            console.error(
              `Error deleting ${this.getSingularTabName()}:`,
              error
            );
          });
        break;

      default:
        this.loading = false;
        break;
    }
  }
}
