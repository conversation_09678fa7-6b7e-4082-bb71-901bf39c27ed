<div class="list-container">
  <div class="list-header border-rad">
    <div class="header-left">
      <h2 class="list-title">
        <i class="material-icons header-icon">book</i>
        {{ isEditMode ? 'Edit Admin' : 'Create Admin' }}
      </h2>
    </div>
    <div class="header-right">
      <a routerLink="/admin/dashboard/admins" class="add-button">
        <span class="material-icons add-icon">arrow_back</span>
        Back to List
      </a>
      <div class="container-behind"></div>
    </div>
  </div>

  <!-- Error message -->
  @if (error) {
  <div class="alert alert-error">{{ error }}</div>
  }

  <!-- Success message -->
  @if (success) {
  <div class="alert alert-success">{{ success }}</div>
  }

  <!-- Loading indicator -->
  @if (loading) {
  <div class="loading-container">
    <div class="loading-spinner"></div>
    <span class="loading-text" i18n="@@loadingAdmin">Loading...</span>
  </div>
  }

  <form [formGroup]="adminForm" (ngSubmit)="onSubmit()" class="list-content">
    <!-- Name field -->
    <div class="form-group">
      <label for="name" class="form-label" i18n="@@adminNameLabel">
        Name *
      </label>
      <input
        type="text"
        id="name"
        formControlName="name"
        class="form-control"
        [class.invalid]="adminForm.get('name')?.invalid && adminForm.get('name')?.touched"
      />
      @if (adminForm.get('name')?.invalid && adminForm.get('name')?.touched) {
      <p class="error-message" i18n="@@nameRequiredError">
        Name is required and must be at least 3 characters.
      </p>
      }
    </div>

    <!-- Email field -->
    <div class="form-group">
      <label for="email" class="form-label" i18n="@@adminEmailLabel">
        Email *
      </label>
      <input
        type="text"
        id="email"
        formControlName="email"
        class="form-control"
        [class.invalid]="adminForm.get('email')?.invalid && adminForm.get('email')?.touched"
      />
      @if (adminForm.get('email')?.invalid && adminForm.get('email')?.touched) {
      <p class="error-message" i18n="@@pathRequiredError">Email is required.</p>
      }
    </div>

    <!-- Form actions -->
    <div class="actions-container">
      <button
        type="submit"
        [disabled]="adminForm.invalid || loading"
        class="action-button submit-button"
        i18n="@@saveButton"
      >
        <span class="material-icons action-icon"
          >{{ isEditMode ? 'update' : 'add_circle' }}</span
        >
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </div>
  </form>
</div>
