<div class="list-container">
  <div class="list-header border-rad">
    <div class="header-left">
      <h2 class="list-title">
        <i class="material-icons header-icon">question_answer</i>
        {{ isEdit ? 'Edit' : 'Create' }} Answer
      </h2>
    </div>
    <div class="header-right">
      <a routerLink="/admin/dashboard/answers" class="add-button">
        <span class="material-icons add-icon">arrow_back</span>
        Back to List
      </a>
      <div class="container-behind"></div>
    </div>
  </div>

  <div *ngIf="success" class="alert alert-success">{{ success }}</div>
  <div *ngIf="error" class="alert alert-error">{{ error }}</div>

  <div *ngIf="loading" class="loading-container">
    <div class="loading-spinner"></div>
    <span class="loading-text">Loading...</span>
  </div>

  <form
    *ngIf="!loading"
    (ngSubmit)="saveAnswer()"
    #answerForm="ngForm"
    class="list-content"
  >
    <div class="form-group">
      <label class="form-label" for="label">Label</label>
      <input
        class="form-control"
        [class.invalid]="label.invalid && (label.dirty || label.touched)"
        id="label"
        type="text"
        name="label"
        [(ngModel)]="answer.label"
        required
        #label="ngModel"
        placeholder="Answer label (e.g., A, B, C, D)"
      />
      <div
        *ngIf="label.invalid && (label.dirty || label.touched)"
        class="error-message"
      >
        Label is required
      </div>
    </div>

    <div class="form-group">
      <label class="form-label" for="text">Text</label>
      <textarea
        class="form-control"
        [class.invalid]="text.invalid && (text.dirty || text.touched)"
        id="text"
        name="text"
        [(ngModel)]="answer.text"
        required
        #text="ngModel"
        placeholder="Answer text"
        rows="4"
      ></textarea>
      <div
        *ngIf="text.invalid && (text.dirty || text.touched)"
        class="error-message"
      >
        Text is required
      </div>
    </div>

    <div class="form-group">
      <label class="form-label" for="valid_answer">Valid Answer</label>
      <div class="checkbox-container">
        <input
          class="form-checkbox"
          id="valid_answer"
          type="checkbox"
          name="valid_answer"
          [(ngModel)]="answer.valid_answer"
          #valid_answer="ngModel"
        />
        <span class="checkbox-label">This is the correct answer</span>
      </div>
    </div>

    <div class="form-group">
      <label class="form-label" for="question_id">Question</label>
      <select
        class="form-control"
        [class.invalid]="question_id.invalid && (question_id.dirty || question_id.touched)"
        id="question_id"
        name="question_id"
        [(ngModel)]="answer.question_id"
        (change)="onQuestionSelected()"
        required
        #question_id="ngModel"
      >
        <option [ngValue]="null" disabled>Select a question</option>
        <option *ngFor="let question of questions" [ngValue]="question.id">
          {{ question.title ? question.title : 'Question #' + question.id }}{{ question.statement ? ' - ' + (question.statement.length > 50 ? question.statement.substring(0, 50) + '...' : question.statement) : '' }}
        </option>
      </select>
      <div
        *ngIf="question_id.invalid && (question_id.dirty || question_id.touched)"
        class="error-message"
      >
        Question is required
      </div>
    </div>

    <div class="actions-container">
      <button
        class="action-button submit-button"
        type="submit"
        [disabled]="answerForm.invalid || loading"
      >
        <span class="material-icons action-icon">{{ isEdit ? 'update' : 'add_circle' }}</span>
        {{ isEdit ? 'Update' : 'Create' }} Answer
      </button>
    </div>
  </form>
</div>
