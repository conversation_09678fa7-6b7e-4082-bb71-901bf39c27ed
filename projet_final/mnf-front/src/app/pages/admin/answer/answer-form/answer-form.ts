import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Answer } from '../../../../models/admin/Answer';
import { Question } from '../../../../models/admin/Question';
import { AnswerService } from '../../../../services/admin/answer.service';
import { QuestionService } from '../../../../services/admin/question.service';
import { NotificationService } from '../../../../services/notification.service';

@Component({
  selector: 'app-answer-form',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './answer-form.html',
  styleUrl: '../../list-container/list-container.css'
})
export class AnswerFormComponent implements OnInit {
  answer: Answer = {
    label: '',
    text: '',
    valid_answer: false,
    question_id: 0
  };
  questions: Question[] = [];
  isEdit = false;
  loading = false;
  error = '';
  success = '';

  constructor(
    private answerService: AnswerService,
    private questionService: QuestionService,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadQuestions();

    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEdit = true;
      this.loadAnswer(+id);
    }
  }

  loadQuestions(): void {
    this.loading = true;
    // Load all questions for the dropdown (no pagination for simplicity)
    this.questionService.getQuestions('', 0, 1000)
      .then(result => {
        this.questions = result.content;
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load questions. Please try again.';
        this.notificationService.error(this.error);
        this.loading = false;
        console.error('Error loading questions:', error);
      });
  }

  loadAnswer(id: number): void {
    this.loading = true;
    this.answerService.getAnswerById(id)
      .then(answer => {
        if (answer) {
          this.answer = answer;
        } else {
          this.error = 'Answer not found';
          this.notificationService.error('Answer not found');
        }
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load answer. Please try again.';
        this.notificationService.error('Failed to load answer. Please try again.');
        this.loading = false;
        console.error('Error loading answer:', error);
      });
  }

  onQuestionSelected(): void {
    if (this.answer.question_id) {
      // Call the route with the selected questionId
      this.questionService.getQuestionById(this.answer.question_id)
        .then(question => {
          if (!question) {
            this.error = 'Failed to load question details. Please try again.';
            this.notificationService.error(this.error);
          }
        })
        .catch(error => {
          this.error = 'Failed to load question details. Please try again.';
          this.notificationService.error(this.error);
          console.error('Error loading question details:', error);
        });
    }
  }

  saveAnswer(): void {
    this.loading = true;
    this.error = '';
    this.success = '';

    const save = this.isEdit
      ? this.answerService.updateAnswer(this.answer.id!, this.answer)
      : this.answerService.createAnswer(this.answer);

    save
      .then(result => {
        if (result) {
          const successMessage = `Answer ${this.isEdit ? 'updated' : 'created'} successfully`;
          this.success = successMessage;
          this.notificationService.success(successMessage);

          if (!this.isEdit) {
            // Reset form for new entry
            this.answer = {
              label: '',
              text: '',
              valid_answer: false,
              question_id: this.answer.question_id // Keep the same question selected
            };
          }

          // Navigate back to the list
          this.router.navigate(['/admin/dashboard/answers']);
        } else {
          throw new Error('Failed to save answer');
        }
        this.loading = false;
      })
      .catch(error => {
        const errorMessage = `Failed to ${this.isEdit ? 'update' : 'create'} answer. Please try again.`;
        this.error = errorMessage;
        this.notificationService.error(errorMessage);
        this.loading = false;
        console.error(`Error ${this.isEdit ? 'updating' : 'creating'} answer:`, error);
      });
  }
}
