import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-answer-list',
  standalone: true,
  imports: [CommonModule, RouterLink],
  template: `
    <div class="p-4">
      <h1 class="text-3xl font-bold mb-6" i18n="@@answersTitle">Answers</h1>
      <p class="text-gray-300 mb-4" i18n="@@answersPlaceholder">
        This is a placeholder for the Answers list. The actual component will be implemented soon.
      </p>
      <a routerLink="/admin" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200" i18n="@@backToAdmin">
        Back to Admin Dashboard
      </a>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class AnswerListComponent {
  // This is a placeholder component
}
