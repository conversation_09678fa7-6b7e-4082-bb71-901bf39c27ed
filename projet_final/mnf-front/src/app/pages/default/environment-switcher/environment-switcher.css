.environment-switcher-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.environment-card {
  margin-bottom: 20px;
  background-color: var(--card-background);
  color: var(--text-color);
}

.current-environment {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 4px;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.current-environment h3 {
  color: var(--primary-color);
  margin-top: 0;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
}

.current-environment p {
  margin: 8px 0;
}

.environment-selector {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 4px;
  background-color: var(--surface-color);
}

/* Style for mat-select dropdown */
::ng-deep .mat-mdc-select-panel {
  background-color: var(--mat-select-bg) !important;
  border: 1px solid var(--border-color);
}

::ng-deep .mat-mdc-option {
  color: var(--text-color) !important;
}

::ng-deep .mat-mdc-option:hover:not(.mat-option-disabled) {
  background-color: var(--mat-select-option-hover) !important;
}

@media (min-width: 600px) {
  .environment-selector {
    flex-direction: row;
    align-items: center;
  }

  .environment-selector mat-form-field {
    flex: 1;
    margin-right: 10px;
  }

  .environment-selector mat-form-field ::ng-deep .mat-mdc-form-field-focus-overlay {
    background-color: var(--surface-color);
  }

  .environment-selector mat-form-field ::ng-deep .mdc-text-field--filled {
    background-color: var(--mat-input-bg);
  }
}

.api-test {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.api-test h3 {
  color: var(--primary-color);
  margin-top: 0;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
}

.api-test h4 {
  color: var(--primary-color);
  margin-top: 15px;
  margin-bottom: 10px;
}

.loading {
  display: flex;
  align-items: center;
  margin: 10px 0;
  padding: 10px;
  border-radius: 4px;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
}

.loading span {
  margin-left: 10px;
  color: var(--text-color);
}

/* Ensure spinner color adapts to theme */
.loading mat-spinner ::ng-deep circle {
  stroke: var(--primary-color) !important;
}

/* Button styling for dark mode */
button.mat-mdc-raised-button.mat-primary {
  background-color: var(--primary-color);
  color: white;
}

button.mat-mdc-raised-button.mat-accent {
  background-color: var(--secondary-color);
  color: white;
}

button.mat-mdc-raised-button:disabled {
  background-color: var(--border-color);
  color: var(--text-color);
  opacity: 0.5;
}

.error {
  margin: 10px 0;
  padding: 10px;
  border-radius: 4px;
  background-color: var(--surface-color);
  border: 1px solid var(--error-color);
  color: var(--error-color);
}

.users, .no-users {
  margin-top: 15px;
  padding: 15px;
  border-radius: 4px;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
}

.users mat-list-item {
  border-bottom: 1px solid var(--border-color);
}

.users mat-list-item:last-child {
  border-bottom: none;
}

.users mat-list-item ::ng-deep .mdc-list-item__content {
  color: var(--text-color);
}

.note {
  font-style: italic;
  color: var(--text-color);
  opacity: 0.7;
  margin: 10px;
}
