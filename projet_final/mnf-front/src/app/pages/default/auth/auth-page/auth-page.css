/* Import list container styles */
@import "../../../admin/list-container/list-container.css";

/* Custom styles for auth page component */
:host {
  display: block;
}

/* Slider Component Styles */
.crud-tabs-container {
  width: 100%;
  padding: 10px 0;
  margin-bottom: 15px;
}

.slider-container {
  margin-left: auto;
  margin-right: auto;
  width: 70%;
  padding: 10px 0;
}

.slider-track {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 55%;
  max-width: 450px;
  margin: 0 auto;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 25px;
  border: 2px solid #cd7a88;
  padding: 0 12px;
}

.slider-ball {
  position: absolute;
  height: 40px;
  background-color: #cd7a88;
  border: 2px solid #cd7a88;
  border-radius: 20px; /* Half of the height for a perfect pill shape */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: left 0.3s ease-in-out, width 0.3s ease-in-out;
  z-index: 1;
  top: calc(50% - 2px);
  margin-top: -20px; /* Half of the height to center vertically */
  left: 0; /* Default position to avoid being too close to the right */
}

.slider-item {
  position: relative;
  z-index: 2;
  font-size: 14px;
  padding: 8px 16px; /* Increased horizontal padding */
  margin: 0 2px; /* Added small margin between items */
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-align: center;
  font-weight: 400;
}

.slider-item.active {
  color: #ffffff;
  font-weight: 500;
}

.slider-item:hover {
  color: var(--secondary-dark-color);
  translate: 2px;
}

/* Override list container styles for auth page */
.list-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 1.5rem;
}

.header-left {
  display: flex;
  flex-direction: column;
  width: 75%;
  background-color: #cd7a88;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 1rem;
  text-align: center;
}

.header-right {
  display: flex;
  position: relative;
}

.container-behind {
  position: absolute;
  width: 30px;
  height: 30px;
  bottom: -4.5px;
  background-color: #cd7a88;
  overflow: hidden;
}

.container-behind::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 30px;
  background: #fffaf3;
  border-bottom-left-radius: 70%;
  z-index: 10;
}

.list-title {
  justify-content: center;
}

.form-subtitle {
  display: block;
  text-align: center;
  margin-left: 0;
}

/* Styling for the form container */

.list-content {
  background-color: #cd7a88;
  border-radius: 10px;
  padding: 4px;
}

.form-container {
  background-color: #f0dddb;
  border-radius: 0.5rem;
  padding: 1rem;
}

.auth-form-container {
  padding: 0.5rem 0;
  max-width: 400px;
  margin: 0 auto;
}

/* Custom styling for form fields */
::ng-deep .mat-mdc-form-field {
  width: 100%;
}

::ng-deep
  .mat-mdc-form-field.mat-form-field-appearance-fill
  .mat-mdc-text-field-wrapper {
  background-color: white;
  border: 1px solid #cd7a88;
  border-radius: 0.25rem;
}

::ng-deep .mat-mdc-form-field .mat-mdc-form-field-flex {
  padding: 0.375rem 0.5rem;
  display: flex;
  align-items: center;
}

::ng-deep .mat-mdc-form-field .mat-mdc-form-field-infix {
  padding: 0.375rem 0;
  width: auto;
}

/* Style for the label */
::ng-deep .mat-mdc-form-field .mat-mdc-floating-label,
::ng-deep .mat-mdc-form-field .mat-mdc-input-element {
  color: black;
  font-size: 14px;
}

/* Style for the icons */
::ng-deep .mat-mdc-form-field .mat-icon {
  color: #cd7a88;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  height: 20px;
  width: 20px;
}

/* Style for buttons */
::ng-deep .mat-mdc-raised-button.mat-primary {
  background-color: #cd7a88;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  padding: 0.5rem 1rem;
  height: auto;
  min-width: 150px;
}

::ng-deep .mat-mdc-raised-button.mat-primary:hover {
  background-color: #cd8a88;
}

/* Style for action buttons container */
.actions-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

/* Add some responsive adjustments */
@media (max-width: 640px) {
  .list-container {
    padding: 0.5rem;
  }
}
