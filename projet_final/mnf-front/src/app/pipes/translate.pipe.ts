import { Pipe, PipeTransform } from '@angular/core';
import { TranslationService } from '../services/translation.service';

@Pipe({
  name: 'translate',
  standalone: true,
  pure: false // Make the pipe impure so it updates when the language changes
})
export class TranslatePipe implements PipeTransform {
  constructor(private translationService: TranslationService) {}

  transform(key: string, params?: any): string {
    // Check if the key contains parameters
    if (key && key.includes('|')) {
      const [baseKey, paramsString] = key.split('|');
      try {
        // Parse the parameters from the string
        const paramsObj = JSON.parse(paramsString);
        // Get the translation
        let translation = this.translationService.translate(baseKey);

        // Replace placeholders with parameter values
        Object.keys(paramsObj).forEach(paramKey => {
          translation = translation.replace(`{{${paramKey}}}`, paramsObj[paramKey]);
        });

        return translation;
      } catch (e) {
        console.error('Error parsing translation parameters:', e);
        return this.translationService.translate(key);
      }
    }

    // If no parameters, just translate the key
    return this.translationService.translate(key);
  }
}
