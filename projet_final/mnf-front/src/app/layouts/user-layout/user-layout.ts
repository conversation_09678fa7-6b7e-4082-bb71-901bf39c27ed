import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MaterialModules } from '../../material/material';
import { CommonModule } from '@angular/common';
import { Header } from '../../components/user/layouts/header/header';
import { Footer } from '../../components/user/layouts/footer/footer';

@Component({
  selector: 'app-user-layout',
  imports: [RouterOutlet, MaterialModules, CommonModule, Header, Footer],
  templateUrl: './user-layout.html',
  styleUrl: './user-layout.css'
})
export class UserLayout {
  // Navigation links for the header
  navLinks = [
    { label: 'Home', path: '/home' },
    { label: 'Messages', path: '/messaging' },
    { label: 'About', path: '/about' },
    { label: 'Contact', path: '/contact' }
  ];
}
