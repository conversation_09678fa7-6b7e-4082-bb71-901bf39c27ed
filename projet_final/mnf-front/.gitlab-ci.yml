stages:
  - build
  - deploy

variables:
  AWS_DEFAULT_REGION: "eu-west-3"
  S3_BUCKET: "mnf-training"
  INDEX_DIR: "dist/mnf-front/browser"

build:
  stage: build
  image: node:24.3.0-alpine3.22
  script:
    - npm ci
    - npm run build
  artifacts:
    paths:
      - dist/mnf-front/browser
  only:
    - main
  

deploy_to_s3:
  stage: deploy
  image:
    name: amazon/aws-cli:latest
    entrypoint: [""]
  needs: [build]
  script:
    - aws configure set aws_access_key_id "$AWS_ACCESS_KEY_ID" --debug
    - aws configure set aws_secret_access_key "$AWS_SECRET_ACCESS_KEY" --debug
    - aws configure set default.region "$AWS_DEFAULT_REGION" --debug
    - aws s3 cp $INDEX_DIR s3://mnf-training --recursive
  only:
    - main