INSERT INTO promotion(id, name) VALUES (1,'<PERSON><PERSON><PERSON>rier 2011');
INSERT INTO promotion(id, name) VALUES (2,'Mars 2011');
INSERT INTO promotion(id, name) VALUES (3,'Avril 2011');
INSERT INTO promotion(id, name) VALUES (4,'Février 2012');
INSERT INTO promotion(id, name) VALUES (5,'Mars 2012');
INSERT INTO promotion(id, name) VALUES (6,'Avril 2012');
INSERT INTO promotion(id, name) VALUES (7,'Mai 2012');
INSERT INTO promotion(id, name) VALUES (8,'Février 2013');
INSERT INTO promotion(id, name) VALUES (9,'Mars 2013');
INSERT INTO promotion(id, name) VALUES (10,'Avril 2013');
INSERT INTO promotion(id, name) VALUES (11,'Mai 2013');
INSERT INTO promotion(id, name) VALUES (12,'Février 2014');
INSERT INTO promotion(id, name) VALUES (13,'Mars 2014');
INSERT INTO promotion(id, name) VALUES (14,'Avril 2014');
INSERT INTO promotion(id, name) VALUES (15,'Mai 2014');
INSERT INTO promotion(id, name) VALUES (16,'Février 2015');
INSERT INTO promotion(id, name) VALUES (17,'Mars 2015');
INSERT INTO promotion(id, name) VALUES (18,'Avril 2015');
INSERT INTO promotion(id, name) VALUES (19,'Mai 2015');
INSERT INTO promotion(id, name) VALUES (20,'Février 2016');
INSERT INTO promotion(id, name) VALUES (21,'Mars 2016');
INSERT INTO promotion(id, name) VALUES (22,'Avril 2016');
INSERT INTO promotion(id, name) VALUES (23,'Mai 2016');
INSERT INTO promotion(id, name) VALUES (24,'Février 2017');
INSERT INTO promotion(id, name) VALUES (25,'Mars 2017');
INSERT INTO promotion(id, name) VALUES (26,'Avril 2017');
INSERT INTO promotion(id, name) VALUES (27,'Mai 2017');
INSERT INTO promotion(id, name) VALUES (28,'Février 2018');
INSERT INTO promotion(id, name) VALUES (29,'Mars 2018');
INSERT INTO promotion(id, name) VALUES (30,'Avril 2018');
INSERT INTO promotion(id, name) VALUES (31,'Mai 2018');
INSERT INTO promotion(id, name) VALUES (32,'Février 2019');
INSERT INTO promotion(id, name) VALUES (33,'Mars 2019');
INSERT INTO promotion(id, name) VALUES (34,'Avril 2019');
INSERT INTO promotion(id, name) VALUES (35,'Mai 2019');
INSERT INTO promotion(id, name) VALUES (36,'Février 2020');
INSERT INTO promotion(id, name) VALUES (37,'Mars 2020');
INSERT INTO promotion(id, name) VALUES (38,'Avril 2020');
INSERT INTO promotion(id, name) VALUES (39,'Mai 2020');
INSERT INTO promotion(id, name) VALUES (40,'Février 2021');
INSERT INTO promotion(id, name) VALUES (41,'Mars 2021');
INSERT INTO promotion(id, name) VALUES (42,'Avril 2021');
INSERT INTO promotion(id, name) VALUES (43,'Mai 2021');
INSERT INTO promotion(id, name) VALUES (44,'Février 2022');
INSERT INTO promotion(id, name) VALUES (45,'Mars 2022');
INSERT INTO promotion(id, name) VALUES (46,'Avril 2022');
INSERT INTO promotion(id, name) VALUES (47,'Mai 2022');

INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Abel','Boulle',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Abélard','Grandjean',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Abelin','Bourguignon',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Abraham','Delannoy',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Achille','Charbonnier',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Adam','Bousquet',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Adélie','Bellegarde',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Adélie','Lavaud',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Adélie','Rouzet',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Adrienne','Geiger',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Adrienne','Mazet',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Agnès','Courtial',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Alain','Delcroix',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Alberte','Dimont',1);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Alceste','Marchant',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Alceste','Serre',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Alexandre','Bethune',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Alfred','Lacan',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Alix','Arsenault',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Alix','Gardet',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Amand','Baschet',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Amand','Silvestre',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Amanda','Morel',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Anaïs','Monteil',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Andrée','Philidor',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Angèle','Beauchamp',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Angèle','Cochet',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Angeline','Cuvier',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Anne-Laure','Bertillon',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Anne-Marie','Brochard',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Anne-Sophie','Berger',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Annick','Desmarais',2);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Annick','Gérald',3);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Annick','Picard',3);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Antoine','Berger',3);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ariane','Magnier',3);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Armel','Ardouin',3);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Armelle','Ouvrard',3);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Arsène','Simon',3);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Arsène','Trouvé',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Aubin','Guilloux',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Aude','Besson',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Audrey','Bélanger',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Augustin','Chaney',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Augustine','Niel',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Aurélia','Allard',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Axelle','Chuquet',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Axelle','Thiers',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Aymeric','Moreau',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Barbe','Droz',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Barthélemy','Gavreau',4);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bastien','Arceneaux',5);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bélise','Courbis',5);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bélise','Cousteau',5);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bénédicte','Chauve',5);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bénédicte','Delon',5);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bénédicte','Jullien',5);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bénédicte','Moineau',5);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Benoît','Benett',5);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Benoît','Jaubert',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bérangère','Lavigne',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bérengère','Bittencourt',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bérengère','Boudet',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bérénice','Jauffret',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Berthe','Affré',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bethsabée','Balzac',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bethsabée','Duval',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Blaise','Brochard',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Blaise','Colbert',6);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Blanche','Baumé',7);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Brice','Bacque',7);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Brice','Doisneau',7);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Brigitte','Barthet',7);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Bruno','LaFromboise',7);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Camille','Maitre',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Camille','Périer',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Capucine','Celice',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Capucine','Haillet',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Carmen','Berthelot',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Caro','Deslys',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Caro','Dujardin',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Cécile','Beaulieu',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Cécile','Gérard',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Céleste','Charbonneau',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Charlène','Pueyrredón',8);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Charlotte','Édouard',9);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Chloé','Trémaux',9);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Christelle','Touchard',9);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Christophe','Peltier',9);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Christopher','Deschanel',9);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Clara','Lebas',9);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Claude','Deniau',9);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Claude','Genest',9);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Claudia','Dimont',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Claudia','Hector',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Claudie','Leroux',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Claudine','Reverdin',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Clélia','Pascal',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Clélia','Philidor',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Clélia','Vandame',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Clélie','Verninac',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Clotilde','Mace',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Colin','Abadie',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Côme','Bourguignon',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Constance','Jeannin',10);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Coralie','LeTonnelier',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Cyril','Gérin',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Cyrille','Léger',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Damien','Gérin',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Danielle','Pierlot',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Dany','Boutin',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Dany','Toussaint',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('David','Gagnon',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('David','Prudhomme',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Davy','Auch',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Débora','Bouchard',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Déborah','Lemaigre',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Denis','Cuvillier',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Dimitri','Naudé',11);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Dolorès','Chapuis',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Dominique','Boudreaux',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Dominique','DAmboise',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Dominique','Poincaré',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Dorothée','Brasseur',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Édith','Berger',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Égide','Bachelet',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Égide','Millet',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Élie','Dujardin',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Élie','Gaudin',12);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Élise','Bouthillier',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Elise','Lussier',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Élisée','Vaillancourt',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Éloi','Charbonneau',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Éloïse','Laframboise',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Emilie','Chagnon',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Emma','Leclère',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Emmanuel','Choquet',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Esther','Thibodeau',13);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Estienne','Neri',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Estienne','Philippon',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Étienne','Garnier',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Eugène','Bourdon',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Eulalie','Allais',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Eulalie','Devilliers',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Éva','Sharpe',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Évelyne','Landry',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Évelyne','Robillard',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Évrard','Pleimelding',14);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Fabien','Laflèche',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Félicité','Charbonneau',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Félix','Larousse',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Fernand','Auger',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Fernand','Laurent',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Fernand','Loup',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Fiona','Thiers',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Flore','Berengar',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Florentin','Boudier',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('France','Matthieu',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('France','Rémy',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Franck','Cordonnier',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Frédéric','Rossignol',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaby','Bassot',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaby','Beaumont',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaël','Bittencourt',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaëlle','Pueyrredón',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaétan','Alard',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaétan','Duchamp',15);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaëtane','Charbonneau',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaëtane','Donnet',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gaspard','Peletier',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gauthier','Deshaies',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gauthier','Dupont',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gautier','Brunelle',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gautier','Manaudou',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Geneviève','Beauvilliers',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gérard','Chabert',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Germain','Loup',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gérôme','Pichard',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gervais','Chauve',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gervais','Loup',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gervais','Poussin',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gervaise','Didier',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gervaise','Gachet',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ghislain','Baumé',16);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ghyslaine','Clérico',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ghyslaine','Leroux',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gilbert','Gérard',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Giselle','Clérico',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Godefroy','Beaugendre',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Godeleine','Crozier',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Guillaume','Jacquemoud',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Guillaume','Montgomery',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Guillemette','Duclos',17);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gustave','Pierlot',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Gwenaël','Courbis',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Hector','Chapelle',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Hector','Kléber',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Hector','Laurent',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Héloïse','Badeaux',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Henriette','Bissonnette',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Henriette','Chaney',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Henriette','Jégou',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Henriette','Subercaseaux',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Hippolyte','Deloffre',18);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Hippolyte','Lazard',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Honorine','Barthélemy',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Hubert','Halphen',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Huguette','Garreau',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ignace','Laframboise',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Igor','Chauveau',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Igor','Subercaseaux',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Inès','Pierrat',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ingrid','Toit',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Isaac','Chevotet',19);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ivanna','Hachette',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jasmin','Auclair',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jasmin','Parmentier',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jasmine','Alard',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jasmine','Rousseau',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Baptiste','Pleimelding',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Charles','Beaux',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Charles','Moreau',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Christophe','Kaplan',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Claude','Saunier',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-François','Suchet',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Jacques','Schaeffer',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Jacques','Suchet',20);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Joël','Couvreur',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Luc','Barrault',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Luc','Baschet',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Luc','Maret',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Marc','Arceneaux',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Marc','Charbonnier',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Marc','Rigal',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Marie','Moreau',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Michel','Cazal',21);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Michel','Michaux',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Noël','Botrel',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Paul','Berlioz',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Philippe','Giraud',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Philippe','Jacquinot',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jean-Yves','Landry',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jeanne','Périer',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jérémie','Thiers',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jérôme','Roatta',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jessica','Ménard',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Joël','LeMahieu',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Joëlle','Dujardin',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jordan','Delon',22);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Josée','Bonnel',23);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Josée','Houdin',23);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Joséphine','Jacquard',23);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Josette','Lemaître',23);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Josselin','Bertillon',23);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Josselin','Choffard',23);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Josselin','Loup',23);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Josselin','Malet',23);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Josseline','Lucy',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Jules','Longchambon',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Julia','Bozonnet',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Juliette','Mignard',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Justin','Dumont',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Justin','Gaubert',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Justin','Sylvestre',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Kevin','Azéma',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Killian','Bonnet',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ladislas','Rousseau',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Laëtitia','Regnard',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lara','Beaumanoir',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Laurence','Flandin',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Laurent','Beauvau',24);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Laurette','Cuvier',25);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Léa','Beaumont',25);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Léa','Lémery',25);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Léo','LeMahieu',25);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Léonard','Boudier',25);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Léonard','Morel',25);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Léopold','Portier',25);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Linda','Auch',25);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lisa','Féret',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lisette','Blanchard',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lorraine','Bain',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lorraine','Fresnel',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lou','Auguste',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lou','Mallet',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Louis','Solé',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Louise','Gaume',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Loup','Alméras',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Luc','Clérico',26);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Luc','Saunier',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lucas','Berthelot',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lucas','Rémy',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lucille','Girard',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lucrèce','Carbonneau',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lucrèce','Raoult',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lucrèce','Rousseau',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ludovic','Balzac',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ludovic','Deslys',27);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ludovic','Dior',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lydie','Saint-Pierre',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Lydie','Vaugrenard',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maël','Bazalgette',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maël','Silvestre',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maéva','Charpentier',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maéva','Mallet',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Manon','Leclair',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marc-Antoine','Bachelet',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Margot','Gounelle',28);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marianne','Subercaseaux',29);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Ange','Berger',29);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Ange','Côté',29);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Claire','Boulle',29);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Claude','Ponce',29);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Françoise','Messier',29);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-José','Vaugeois',29);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Laure','Gauthier',30);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Laure','Leroux',30);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Noëlle','Jacquet',30);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Noëlle','Landry',30);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Paule','Boissieu',30);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marie-Thérèse','Beaugendre',30);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marielle','Maurice',30);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marina','Bélanger',31);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marinette','Chagnon',31);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marion','Delaunay',31);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marius','Jaccoud',31);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marthe','Brassard',31);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marthe','Brousseau',31);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Marthe','Girault',31);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Martin','Chausson',31);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maryvonne','Boissieu',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maryvonne','Carbonneau',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Mathéo','DAboville',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Matthias','Abbadie',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maud','Blanchet',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Max','Larousse',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maxime','Parmentier',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Maxime','Vaugeois',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Médard','Niel',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Mégane','Asselineau',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Mélanie','Cazenave',32);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Mélissa','Durand',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Mélodie','Béliveau',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Mélodie','Carrel',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Michaël','Maurice',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Michel','Vernier',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Michèle','Bourseiller',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Michelle','Villiers',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Mickaël','Crépin',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Milo','Aubert',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Myriam','Dufour',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Myriam','Loupe',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nadia','Lalande',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nadine','Dembélé',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Napoléon','Gigot',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Natanaël','Loupe',33);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nathan','Blaise',34);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nathan','Boudet',34);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nathan','Duchemin',34);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nathanaël','Gallois',34);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nicolas','Cahun',34);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nicolas','LaFromboise',34);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nikita','Beaugendre',35);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Nikita','Spanghero',35);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ninon','Leroux',35);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Noé','Hérisson',35);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Noémi','Allais',35);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Océane','Demaret',35);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Odette','Berthelot',35);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Odile','Toit',35);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Odile','Gainsbourg',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Olivier','Gainsbourg',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Olivier','Nicollier',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Olivier','Solé',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ophélie','Rigal',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Pascal','Thibault',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Pascale','Blanc',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Patrice','Azaïs',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Patrice','Beauvilliers',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Patricia','Delaunay',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Patrick','Pascal',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Paulin','Delannoy',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Pauline','Fétique',36);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Peggy','Lavaud',37);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Philibert','Carrell',37);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Philibert','Verley',37);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Philippe','Gueguen',37);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Pierre-Marie','Cartier',37);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Pierre-Marie','Delafose',37);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Pierre-Marie','Dieulafoy',37);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Porthos','Seyrès',37);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Quentin','Gaumont',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Quentin','Gérard',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Quentin','Manoury',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rachelle','Berthelot',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Raphaël','Gainsbourg',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rebecca','Bittencourt',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rebecca','Duclos',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Régine','Demaret',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Régis','Duret',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Régis','Marais',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rémi','Béliveau',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rémi','Durand',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Remi','Haillet',38);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rémi','Joubert',39);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Remy','Leroux',39);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('René','Moreau',39);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('René','Vallée',39);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Richard','Sartre',39);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roberte','Lucy',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roberte','Moineau',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roch','Bozonnet',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roger','Duret',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rolande','Allaire',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rolande','Bourdon',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Romane','Marchal',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roméo','Carré',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Romuald','Seyrès',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rosalie','Bassot',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rosalie','Boulet',40);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Rose','Clair',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roseline','Ancel',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roseline','Bureau',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roseline','Chabert',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roxane','Ouvrard',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Roxane','Pomeroy',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sabine','Crevier',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sabine','Desjardins',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sacha','Bourcier',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sacha','Delafosse',41);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sacha','Frère',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Samuel','Belyea',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Samuel','Carrel',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sandrine','Hennequin',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sandrine','Maret',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sara','Auguste',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sarah','Villiers',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sarah','Leavitt',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sarah','Silvestre',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sébastien','Barrande',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Ségolène','Verley',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Séverin','Appell',42);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Silvain','Boudet',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Simon','Bourbeau',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Simon','Toutain',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Solange','Bourguignon',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sophie','Vérany',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Stéphane','Fouquet',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Stéphane','Vaugrenard',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Stéphanie','Alméras',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Stéphanie','Gainsbourg',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Suzanne','Demaret',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Suzanne','Pleimelding',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sylvaine','Dior',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sylvaine','Plantier',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sylvia','Vérany',43);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sylviane','Plantier',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sylvie','Édouard',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Sylvie','Rousseau',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Théodore','Beauvau',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Thibaud','Ouvrard',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Thibault','Levett',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Thibaut','Gouin',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Thibaut','Raoult',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Thibaut','Rochefort',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Timothé','Gavreau',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Timothé','Grinda',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Timothé','Jaubert',44);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Tiphaine','Pasteur',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Tobie','Kaplan',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Tristan','Caillat',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Valentin','Dieulafoy',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Valérie','Cuvillier',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Valéry','Jacquard',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Vanessa','Landry',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Vanessa','Manoury',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Victoire','Coulomb',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Victoire','Delacroix',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Victoria','Beaulne',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Victoria','Bescond',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Victoria','Delisle',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Viviane','Brian',45);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Wilfrid','Delafose',46);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Wilfried','Barbet',46);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('William','Bonnet',46);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('William','Léger',46);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('William','Saint-Yves',46);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Xavier','Bonnet',46);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Xavier','Dimont',46);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Xavier','Trudeau',46);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Yolande','Lecerf',47);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Yseult','Boudet',47);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Yvette','Barrault',47);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Yvette','Paquin',47);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Yvette','Wathelet',47);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Yvon','Battier',47);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Yvon','Cartier',47);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Yvonne','Devereux',47);
INSERT INTO intern(first_name, last_name, promotion_id) VALUES ('Zacharie','Laframboise',47);

INSERT INTO chapter (id, name, parent_path) VALUES(145, '"FINALIZE()"', '/OCA/CH 1 Java Building Blocks/DESTROYING OBJECTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(178, '[OCA] Développer avec des classes et méthodes abstraites', '/OCP Exam Objectives/Conception avancée de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(181, '[OCA] Développer avec le mot-clé final', '/OCP Exam Objectives/Conception avancée de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(325, '[OPT] Aperçu des classes de Stream de base', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/');
INSERT INTO chapter (id, name, parent_path) VALUES(527, '[OPT] Bonne pratique des classes', '/OCA Exam Objectives/Structure d''une classe Java/Gérer les classes dans un fichier/');
INSERT INTO chapter (id, name, parent_path) VALUES(589, '[OPT] Compiler et executer manuellement un paquet', '/OCA Exam Objectives/L''organisation en paquets de Java/Créer un nouveau paquet /');
INSERT INTO chapter (id, name, parent_path) VALUES(223, '[opt] Complexité et notation O', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(590, '[OPT] Comprendre le principe des classpath', '/OCA Exam Objectives/L''organisation en paquets de Java/Créer un nouveau paquet /');
INSERT INTO chapter (id, name, parent_path) VALUES(578, '[OPT] Comprendre les différences avec un passage par référence dans les autres langages', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Maîtriser le passage de paramètre par valeur/');
INSERT INTO chapter (id, name, parent_path) VALUES(188, '[OPT] Le Pattern Builder', '/OCP Exam Objectives/Conception de Classes Java/Implémenter les Design Patterns/');
INSERT INTO chapter (id, name, parent_path) VALUES(191, '[OPT] Le Pattern Factory', '/OCP Exam Objectives/Conception de Classes Java/Implémenter les Design Patterns/');
INSERT INTO chapter (id, name, parent_path) VALUES(231, '[OPT] Méthodes par défaut', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/Utiliser les interfaces intégrées dans le package java.util.fonction/');
INSERT INTO chapter (id, name, parent_path) VALUES(385, '[opt] Récapitulaitf des différentes méthodes de l''objet File et NIO2', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/');
INSERT INTO chapter (id, name, parent_path) VALUES(275, '[opt] récapitulatif', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(218, '[opt] Récapitulatif de merge() et des méthodes compute', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/Maps/');
INSERT INTO chapter (id, name, parent_path) VALUES(393, '[OPT] Récapitulatif des classes de Stream', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/');
INSERT INTO chapter (id, name, parent_path) VALUES(281, '[OPT] Récapitulatif des Exceptions à connaître', '/OCP Exam Objectives/Exceptions et Assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(369, '[OPT] Utiliser l''encodage des caractères', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Les Classes de Stream/');
INSERT INTO chapter (id, name, parent_path) VALUES(396, '[OPT] Version historique', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données depuis la console/');
INSERT INTO chapter (id, name, parent_path) VALUES(492, 'Accéder aux arguments de l''exécution dans la méthode main', '/OCA Exam Objectives/L''exécution en Java/Comprendre le processus d''exécution/');
INSERT INTO chapter (id, name, parent_path) VALUES(365, 'Accés au métadonnées via des vues', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/');
INSERT INTO chapter (id, name, parent_path) VALUES(273, 'Activer les assertions', '/OCP Exam Objectives/Exceptions et Assertions/Tester l''invariance avec les assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(786, 'Affichage de données et directives', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(383, 'Afficher le contenu d''un fichier avec la méthode lines', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/');
INSERT INTO chapter (id, name, parent_path) VALUES(491, 'Ajouter des arguments à l''exécution', '/OCA Exam Objectives/L''exécution en Java/Comprendre le processus d''exécution/');
INSERT INTO chapter (id, name, parent_path) VALUES(770, 'Ajouter un fichier - git add', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(808, 'Ajouter un tag', '/Modifier un projet/Identifier une version du projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(437, 'Amélioration des performances', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/Exécuter des tâches en parallèle/');
INSERT INTO chapter (id, name, parent_path) VALUES(151, 'Annotation-driven développement avec Spring', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(773, 'Annuler mes modifications non validées', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(775, 'Annuler mes modifications validées', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(322, 'Aperçu et récapitulatif des arguments à connaître', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/');
INSERT INTO chapter (id, name, parent_path) VALUES(241, 'API Stream de Java', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(568, 'Appeler un constructeur', '/OCA Exam Objectives/Cycle de vie des objets/Les constructeurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(563, 'Appeler une méthode statique', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer le mot clé static /');
INSERT INTO chapter (id, name, parent_path) VALUES(552, 'Appliquer des modificateurs d''accès', '/OCA Exam Objectives/Les méthodes et l''encapsulation/');
INSERT INTO chapter (id, name, parent_path) VALUES(551, 'Appliquer le mot clé static ', '/OCA Exam Objectives/Les méthodes et l''encapsulation/');
INSERT INTO chapter (id, name, parent_path) VALUES(554, 'Appliquer les principes de l''encapsulation dans une classe', '/OCA Exam Objectives/Les méthodes et l''encapsulation/');
INSERT INTO chapter (id, name, parent_path) VALUES(843, 'Apport et nouveautés', '/Vue.JS version 3/');
INSERT INTO chapter (id, name, parent_path) VALUES(721, 'ArithmeticException', '/OCA Exam Objectives/Les exceptions/Connaître les exceptions communes/');
INSERT INTO chapter (id, name, parent_path) VALUES(722, 'ArrayIndexOutOfBoundsException', '/OCA Exam Objectives/Les exceptions/Connaître les exceptions communes/');
INSERT INTO chapter (id, name, parent_path) VALUES(405, 'Arrêter un thread Executor simple', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/Les threads Executor simples/');
INSERT INTO chapter (id, name, parent_path) VALUES(154, 'Aspect-oriented programming', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(410, 'Attendre les résultats des threads', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/');
INSERT INTO chapter (id, name, parent_path) VALUES(412, 'Augmenter la concurence en augmentant le pool de threads', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/');
INSERT INTO chapter (id, name, parent_path) VALUES(853, 'Autres directives (facultatif)', '/Affichage de données et directives/Les directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(501, 'Avantages de Java', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(432, 'Bases de données relatives', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/');
INSERT INTO chapter (id, name, parent_path) VALUES(87, 'BE ABLE TO DETERMINE THE OUTPUT OF CODE "ARRAYLST"', '/OCA EXAM ESSENTIALS/CHAPITRE 3: CORE JAVA APIs/');
INSERT INTO chapter (id, name, parent_path) VALUES(83, 'BE ABLE TO DETERMINE THE OUTPUT OF CODE USING "STRING"', '/OCA EXAM ESSENTIALS/CHAPITRE 3: CORE JAVA APIs/');
INSERT INTO chapter (id, name, parent_path) VALUES(84, 'BE ABLE TO DETERMINE THE OUTPUT OF CODE USING "STRINGBUILDER"', '/OCA EXAM ESSENTIALS/CHAPITRE 3: CORE JAVA APIs/');
INSERT INTO chapter (id, name, parent_path) VALUES(86, 'BE ABLE TO DETERMINE THE OUTPUT OF CODE USING ARRAYS', '/OCA EXAM ESSENTIALS/CHAPITRE 3: CORE JAVA APIs/');
INSERT INTO chapter (id, name, parent_path) VALUES(70, 'BE ABLE TO DETERMINE WHERE VARIABLES GO INTO AND OUT OF SCOPE', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(90, 'BE ABLE TO IDENTIFY CORRECT AND INCORRECT METHOD DECLARATIONS', '/OCA EXAM ESSENTIALS/CHAPITRE 4 : METHODS AND ENCAPSULATION/');
INSERT INTO chapter (id, name, parent_path) VALUES(69, 'BE ABLE TO IDENTIFY LEGAL AND ILLEGAL DECLARATIONS AND INITIALIZATION', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(68, 'BE ABLE TO RECOGNIZE A CONTRUCTOR', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(71, 'BE ABLE TO RECOGNIZE MISPLACED STATEMENTS IN A CLASS', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(95, 'BE ABLE TO RECOGNIZE WHEN A CLASS IS PROPERLY ENCAPSULATED', '/OCA EXAM ESSENTIALS/CHAPITRE 4 : METHODS AND ENCAPSULATION/');
INSERT INTO chapter (id, name, parent_path) VALUES(76, 'BE ABLE TO RECOGNIZE WHICH OPERATORS ARE ASSOCIATED WITH WHICH DATA TYPES', '/OCA EXAM ESSENTIALS/CHAPITRE 2 : OPERATORS AND STATEMENTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(102, 'BE ABLE TO WRITE CODE THAT CREATES AND EXTENDS ABSTRACT CLASSES', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(103, 'BE ABLE TO WRITE CODE THAT CREATES, EXTENDS, AND IMPLEMENTS INTERFACES', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(98, 'BE ABLE TO WRITE CODE THAT EXTENDS OTHER CLASSES', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(104, 'BE ABLE TO WRITE CODE THAT USES DEFAULT AND STATIC INTERFACE METHODS', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(75, 'BE ABLE TO WRITE CODE THAT USES JAVA OPERATORS', '/OCA EXAM ESSENTIALS/CHAPITRE 2 : OPERATORS AND STATEMENTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(78, 'BE ABLE TO WRITE CODE THATUSES PARENTHESES TO OVERRIDE OPERATOR PRECEDENCE', '/OCA EXAM ESSENTIALS/CHAPITRE 2 : OPERATORS AND STATEMENTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(66, 'BE ABLE TO WRITECODE USING A MAIN() METHOD', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(126, 'BENEFITS OF JAVA', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(33, 'Bienvenue dans Java', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(427, 'Blocking queues', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/Les types de collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(754, 'Bonnes pratiques', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(376, 'Breadth-First Search', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/Concepts de navigation/');
INSERT INTO chapter (id, name, parent_path) VALUES(24, 'Buffer and Stream', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(755, 'C''est quoi Git ?', '/Les bases/');
INSERT INTO chapter (id, name, parent_path) VALUES(516, 'Cas 1 : Caractère dans le nom (début/tout le mot)', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/Connaître les règles de nommage/');
INSERT INTO chapter (id, name, parent_path) VALUES(530, 'Cas 1 : Variable locale', '/OCA Exam Objectives/Portée des variables/Connaître la portée des variables selon leur type/');
INSERT INTO chapter (id, name, parent_path) VALUES(544, 'Cas 2 : Appel d''une méthode sur une variable de type primitif', '/OCA Exam Objectives/Types références/Savoir différencier types références et types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(517, 'Cas 2 : Nom réservé par un mot clé', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/Connaître les règles de nommage/');
INSERT INTO chapter (id, name, parent_path) VALUES(531, 'Cas 2 : Variable d''instance', '/OCA Exam Objectives/Portée des variables/Connaître la portée des variables selon leur type/');
INSERT INTO chapter (id, name, parent_path) VALUES(546, 'Cas 3 : Différence de nommage dans les types ', '/OCA Exam Objectives/Types références/Savoir différencier types références et types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(532, 'Cas 3 : Variable de classe', '/OCA Exam Objectives/Portée des variables/Connaître la portée des variables selon leur type/');
INSERT INTO chapter (id, name, parent_path) VALUES(635, 'Cas des varargs dans les méthodes', '/OCA Exam Objectives/Les Arrays en Java/Maîtriser les Varargs/');
INSERT INTO chapter (id, name, parent_path) VALUES(58, 'CH 1 Java Building Blocks', '/OCA/');
INSERT INTO chapter (id, name, parent_path) VALUES(59, 'CH 2 Operators and Statements', '/OCA/');
INSERT INTO chapter (id, name, parent_path) VALUES(60, 'CH 3 Core Java APIs', '/OCA/');
INSERT INTO chapter (id, name, parent_path) VALUES(61, 'CH 4 Methods and Encapsulation', '/OCA/');
INSERT INTO chapter (id, name, parent_path) VALUES(62, 'CH 5 Class Design', '/OCA/');
INSERT INTO chapter (id, name, parent_path) VALUES(63, 'CH 6 Exceptions', '/OCA/');
INSERT INTO chapter (id, name, parent_path) VALUES(56, 'CH 9 NIO.2', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(9, 'CH1 Advanced Class Design', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(57, 'CH10 JDBC', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(10, 'CH2 Design Patterns and Principles', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(11, 'CH3 Generics and Collections', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(12, 'CH4 Functional Programming', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(13, 'CH5 Dates, Strings, and Localization', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(14, 'CH6 Exceptions and Assertions', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(15, 'CH7 Concurrency', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(16, 'CH8 IO', '/OCP/');
INSERT INTO chapter (id, name, parent_path) VALUES(802, 'Changer de branche - git checkout', '/Modifier un projet/Travailler avec les branches/');
INSERT INTO chapter (id, name, parent_path) VALUES(34, 'Chapitre 1 : Bienvenue dans Java', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(72, 'CHAPITRE 1 : JAVA BUILDING BLOCKS', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(43, 'Chapitre 10 : Exceptions', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(44, 'Chapitre 11 : Modules', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(45, 'Chapitre 12 : Fondamentaux de Java', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(46, 'Chapitre 13 : Annotations', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(47, 'Chapitre 14 : Génériques et Collections', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(48, 'Chapitre 15 : Programmation fonctionnelle', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(49, 'Chapitre 16 : Exceptions, Assertions et Localization', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(50, 'Chapitre 17 : Applications modulaires', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(51, 'Chapitre 18 : Concurrence', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(52, 'Chapitre 19 : I/O', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(35, 'Chapitre 2 : Les bases de Java', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(74, 'CHAPITRE 2 : OPERATORS AND STATEMENTS', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(53, 'Chapitre 20 : NIO.2', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(54, 'Chapitre 21 : JDBC', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(55, 'Chapitre 22 : Sécurité', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(36, 'Chapitre 3 : Les opérateurs', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(82, 'CHAPITRE 3: CORE JAVA APIs', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(89, 'CHAPITRE 4 : METHODS AND ENCAPSULATION', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(37, 'Chapitre 4 : Prendre des décisions', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(38, 'Chapitre 5 : APIs de base de Java', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(97, 'CHAPITRE 5 : CLASS DESIGN', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(107, 'CHAPITRE 6 : EXCEPTIONS', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(39, 'Chapitre 6 : Lambda et interfaces fonctionnelles', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(40, 'Chapitre 7 : Méthodes et encapsulation', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(41, 'Chapitre 8 : Design d''une classe', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(42, 'Chapitre 9 : Design avancé d''une classe', '/OCP 11/');
INSERT INTO chapter (id, name, parent_path) VALUES(714, 'Checked exceptions', '/OCA Exam Objectives/Les exceptions/Connaître les différents types d''exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(258, 'Chercher des données avec les méthodes allMatch(), anyMatch() et noneMatch()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(256, 'Chercher des données avec les méthodes findAny() et findFirst()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(255, 'Chercher des données avec les méthodes min() et max()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(381, 'Chercher un dossier', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/');
INSERT INTO chapter (id, name, parent_path) VALUES(30, 'Child Process', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(723, 'ClassCastException', '/OCA Exam Objectives/Les exceptions/Connaître les exceptions communes/');
INSERT INTO chapter (id, name, parent_path) VALUES(733, 'Classes abstraites et interfaces', '/OCA Exam Objectives/L''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(190, 'Classes imbriquées statiques', '/OCP Exam Objectives/Conception avancée de Classes Java/Créer des classes imbriquées/');
INSERT INTO chapter (id, name, parent_path) VALUES(189, 'Classes internes anonymes', '/OCP Exam Objectives/Conception avancée de Classes Java/Créer des classes imbriquées/');
INSERT INTO chapter (id, name, parent_path) VALUES(187, 'Classes internes locales', '/OCP Exam Objectives/Conception avancée de Classes Java/Créer des classes imbriquées/');
INSERT INTO chapter (id, name, parent_path) VALUES(184, 'Classes internes membres', '/OCP Exam Objectives/Conception avancée de Classes Java/Créer des classes imbriquées/');
INSERT INTO chapter (id, name, parent_path) VALUES(115, 'CLASSES VS. FICHIERS', '/OCA/CH 1 Java Building Blocks/Comprendre la structure d''une classe Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(131, 'CODE FORMATTING ON THE EXAM', '/OCA/CH 1 Java Building Blocks/UNDERSTANDING PACKAGE DECLARATIONS AND IMPORTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(443, 'Combiner un résultat avec la méthode collect()', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/Les réductions parallèles/');
INSERT INTO chapter (id, name, parent_path) VALUES(442, 'Combiner un résultat avec la méthode reduce()', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/Les réductions parallèles/');
INSERT INTO chapter (id, name, parent_path) VALUES(852, 'Commencer un projet avec Vue CLI', '/Mettre en ligne une application avec Vue/');
INSERT INTO chapter (id, name, parent_path) VALUES(826, 'Comment créer une instance en Vue.JS ?', '/Notion de composant Vue.JS/');
INSERT INTO chapter (id, name, parent_path) VALUES(822, 'Comment éviter les conflits', '/Bonnes pratiques/');
INSERT INTO chapter (id, name, parent_path) VALUES(756, 'Comment installer Git', '/Les bases/');
INSERT INTO chapter (id, name, parent_path) VALUES(823, 'Comment résoudre les conflits', '/Bonnes pratiques/');
INSERT INTO chapter (id, name, parent_path) VALUES(114, 'COMMENTS', '/OCA/CH 1 Java Building Blocks/Comprendre la structure d''une classe Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(829, 'Communication entre les composants: émission', '/Notion de composant Vue.JS/');
INSERT INTO chapter (id, name, parent_path) VALUES(828, 'Communication entre les composants: props', '/Notion de composant Vue.JS/');
INSERT INTO chapter (id, name, parent_path) VALUES(740, 'Comparaison entre classe abstraite et interface', '/OCA Exam Objectives/L''héritage en Java/Classes abstraites et interfaces/');
INSERT INTO chapter (id, name, parent_path) VALUES(717, 'Comparaison entre les exceptions', '/OCA Exam Objectives/Les exceptions/Connaître les différents types d''exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(657, 'Comparaison String et StringBuilder', '/OCA Exam Objectives/Les API de base de Java/Stocker les données et les manipuler avec des String et des StringBuilder/');
INSERT INTO chapter (id, name, parent_path) VALUES(222, 'Comparaisons entre Listes, Sets, Maps et Queues', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(487, 'Compiler un programme Java en ligne de commande', '/OCA Exam Objectives/L''exécution en Java/Comprendre le processus de compilation/');
INSERT INTO chapter (id, name, parent_path) VALUES(389, 'Comprendre ce qu''est un thread', '/OCP Exam Objectives/Concurrence en Java/Les threads/');
INSERT INTO chapter (id, name, parent_path) VALUES(484, 'Comprendre chaque élément constitutif de main', '/OCA Exam Objectives/L''exécution en Java/Connaître la méthode main et sa syntaxe/');
INSERT INTO chapter (id, name, parent_path) VALUES(619, 'Comprendre l''égibilité d''un objet au garbage collector', '/OCA Exam Objectives/Cycle de vie des objets/Le processus de suppression/');
INSERT INTO chapter (id, name, parent_path) VALUES(649, 'Comprendre l''intérêt de l''héritage des méthodes', '/OCA Exam Objectives/L''héritage en Java/La notion d''héritage/');
INSERT INTO chapter (id, name, parent_path) VALUES(597, 'Comprendre l''origine de la robustesse', '/OCA Exam Objectives/Avantages de Java/Connaître les spécificités de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(599, 'Comprendre l''origine de la sécurité ', '/OCA Exam Objectives/Avantages de Java/Connaître les spécificités de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(598, 'Comprendre l''origine de la simplicité', '/OCA Exam Objectives/Avantages de Java/Connaître les spécificités de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(588, 'Comprendre l''usage des noms de paquets', '/OCA Exam Objectives/L''organisation en paquets de Java/Créer un nouveau paquet /');
INSERT INTO chapter (id, name, parent_path) VALUES(726, 'Comprendre la différence entre type de référence et type d''objet', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(736, 'Comprendre la notion d''interface', '/OCA Exam Objectives/L''héritage en Java/Classes abstraites et interfaces/');
INSERT INTO chapter (id, name, parent_path) VALUES(734, 'Comprendre la notion de classe abstraite', '/OCA Exam Objectives/L''héritage en Java/Classes abstraites et interfaces/');
INSERT INTO chapter (id, name, parent_path) VALUES(735, 'Comprendre la notion de classe concrète', '/OCA Exam Objectives/L''héritage en Java/Classes abstraites et interfaces/');
INSERT INTO chapter (id, name, parent_path) VALUES(744, 'Comprendre la notion de polymorphisme', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(246, 'Comprendre la pipeline d''un Stream', '/OCP Exam Objectives/API Stream de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(64, 'Comprendre la structure d''une classe Java', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(621, 'Comprendre la suppression d''un objet', '/OCA Exam Objectives/Cycle de vie des objets/Le processus de suppression/');
INSERT INTO chapter (id, name, parent_path) VALUES(600, 'Comprendre le fonctionnement des blocs d''initialisation', '/OCA Exam Objectives/Cycle de vie des objets/Le processus d''initialisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(601, 'Comprendre le fonctionnement des blocs d''initialisation statiques', '/OCA Exam Objectives/Cycle de vie des objets/Le processus d''initialisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(565, 'Comprendre le fonctionnement du constructeur par défaut', '/OCA Exam Objectives/Cycle de vie des objets/Les constructeurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(486, 'Comprendre le fonctionnement et l''utilité de base de la JVM', '/OCA Exam Objectives/L''exécution en Java/Comprendre le processus de compilation/');
INSERT INTO chapter (id, name, parent_path) VALUES(646, 'Comprendre le principe d''héritage simple et multiple', '/OCA Exam Objectives/L''héritage en Java/La notion d''héritage/');
INSERT INTO chapter (id, name, parent_path) VALUES(489, 'Comprendre le processus d''exécution', '/OCA Exam Objectives/L''exécution en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(606, 'Comprendre le processus d''initialisation d''un objet', '/OCA Exam Objectives/Cycle de vie des objets/Le processus d''initialisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(485, 'Comprendre le processus de compilation', '/OCA Exam Objectives/L''exécution en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(302, 'Comprendre le système de fichier', '/OCP Exam Objectives/Les fondamentaux de Java IO/');
INSERT INTO chapter (id, name, parent_path) VALUES(350, 'Comprendre les attributs de fichier (metadata)', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/');
INSERT INTO chapter (id, name, parent_path) VALUES(713, 'Comprendre les cas où une méthode est virtuelle', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(333, 'Comprendre les chemins symboliques', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(609, 'Comprendre les différences entre post unary et pre unary', '/OCA Exam Objectives/Utiliser les opérateurs/Différencier les différents opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(638, 'Comprendre les expressions conditionnelles', '/OCA Exam Objectives/Les structures de contrôle/');
INSERT INTO chapter (id, name, parent_path) VALUES(320, 'Comprendre les Stream IO', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/');
INSERT INTO chapter (id, name, parent_path) VALUES(367, 'Comprendre les vues', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/Accés au métadonnées via des vues/');
INSERT INTO chapter (id, name, parent_path) VALUES(175, 'Conception avancée de Classes Java', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(172, 'Conception de Classes Java', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(372, 'Concepts de navigation', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/');
INSERT INTO chapter (id, name, parent_path) VALUES(308, 'Conceptualiser le système de fichier avec l''objet File', '/OCP Exam Objectives/Les fondamentaux de Java IO/');
INSERT INTO chapter (id, name, parent_path) VALUES(386, 'Concurrence en Java', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(824, 'Configuration', '/Les bases/');
INSERT INTO chapter (id, name, parent_path) VALUES(148, 'Configuration des beans', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(593, 'Connaître et comprendre l''encapsulation', '/OCA Exam Objectives/Avantages de Java/Connaître les caractéristiques de la POO/');
INSERT INTO chapter (id, name, parent_path) VALUES(595, 'Connaître et comprendre l''organisation en classe', '/OCA Exam Objectives/Avantages de Java/Connaître les caractéristiques de la POO/');
INSERT INTO chapter (id, name, parent_path) VALUES(669, 'Connaître et maîtriser les méthodes des dates', '/OCA Exam Objectives/Les API de base de Java/Savoir modifier une date/');
INSERT INTO chapter (id, name, parent_path) VALUES(660, 'Connaître la LocalDate', '/OCA Exam Objectives/Les API de base de Java/Connaître les objets du package java.time/');
INSERT INTO chapter (id, name, parent_path) VALUES(662, 'Connaître la LocalDateTime', '/OCA Exam Objectives/Les API de base de Java/Connaître les objets du package java.time/');
INSERT INTO chapter (id, name, parent_path) VALUES(664, 'Connaître la LocalTime', '/OCA Exam Objectives/Les API de base de Java/Connaître les objets du package java.time/');
INSERT INTO chapter (id, name, parent_path) VALUES(482, 'Connaître la méthode main et sa syntaxe', '/OCA Exam Objectives/L''exécution en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(611, 'Connaître la notion de short circuit', '/OCA Exam Objectives/Utiliser les opérateurs/Différencier les différents opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(529, 'Connaître la portée des variables selon leur type', '/OCA Exam Objectives/Portée des variables/');
INSERT INTO chapter (id, name, parent_path) VALUES(614, 'connaître la priorité des opérateurs', '/OCA Exam Objectives/Utiliser les opérateurs/Maîtriser l''ordre des opérations/');
INSERT INTO chapter (id, name, parent_path) VALUES(582, 'Connaitre la syntaxe d''un import', '/OCA Exam Objectives/L''organisation en paquets de Java/Importer des paquets externes/');
INSERT INTO chapter (id, name, parent_path) VALUES(483, 'Connaître la syntaxe de la méthode main', '/OCA Exam Objectives/L''exécution en Java/Connaître la méthode main et sa syntaxe/');
INSERT INTO chapter (id, name, parent_path) VALUES(745, 'Connaître la syntaxe et la réalisation de l''héritage d''une classe', '/OCA Exam Objectives/L''héritage en Java/La mise en place de l''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(666, 'Connaître la syntaxe et la réalisation de l''héritage d''une méthode', '/OCA Exam Objectives/L''héritage en Java/La mise en place de l''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(671, 'Connaître la syntaxe et la réalisation de l''héritage d''une variable', '/OCA Exam Objectives/L''héritage en Java/La mise en place de l''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(652, 'Connaître le fonctionnement du StringPool', '/OCA Exam Objectives/Les API de base de Java/Stocker les données et les manipuler avec des String et des StringBuilder/');
INSERT INTO chapter (id, name, parent_path) VALUES(596, 'Connaître le principe d''indépendance de plateforme', '/OCA Exam Objectives/Avantages de Java/Connaître les spécificités de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(539, 'Connaître le principe de référencement', '/OCA Exam Objectives/Types références/Maîtriser les types référençant un objet/');
INSERT INTO chapter (id, name, parent_path) VALUES(692, 'Connaître les améliorations du compilateur pour le constructeur', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(708, 'Connaître les avantages de la gestion d''exception', '/OCA Exam Objectives/Les exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(508, 'Connaître les bonnes pratiques de nommage', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(591, 'Connaître les caractéristiques de la POO', '/OCA Exam Objectives/Avantages de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(651, 'Connaître les caractéristiques de String', '/OCA Exam Objectives/Les API de base de Java/Stocker les données et les manipuler avec des String et des StringBuilder/');
INSERT INTO chapter (id, name, parent_path) VALUES(550, 'Connaître les char et les booléens', '/OCA Exam Objectives/Topologie des variables/Les types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(576, 'Connaître les conséquence d''un passage par valeur', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Maîtriser le passage de paramètre par valeur/');
INSERT INTO chapter (id, name, parent_path) VALUES(685, 'Connaître les différences avec les Arrays', '/OCA Exam Objectives/Les API de base de Java/Maîtriser les ArrayLists/');
INSERT INTO chapter (id, name, parent_path) VALUES(553, 'Connaître les différences entre chaque type primitifs', '/OCA Exam Objectives/Topologie des variables/Les types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(665, 'Connaître les différences entre les 3 classes', '/OCA Exam Objectives/Les API de base de Java/Connaître les objets du package java.time/');
INSERT INTO chapter (id, name, parent_path) VALUES(679, 'Connaître les différences entre les boucles', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les boucles/');
INSERT INTO chapter (id, name, parent_path) VALUES(573, 'Connaître les différences entre les différents modificateurs d''accès', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer des modificateurs d''accès/');
INSERT INTO chapter (id, name, parent_path) VALUES(705, 'Connaître les différents types d''exceptions', '/OCA Exam Objectives/Les exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(607, 'Connaître les différents types d''opérateurs', '/OCA Exam Objectives/Utiliser les opérateurs/Différencier les différents opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(707, 'Connaître les effets de try-catch et l''impact sur le flux du programme', '/OCA Exam Objectives/Les exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(711, 'Connaître les exceptions communes', '/OCA Exam Objectives/Les exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(629, 'Connaître les façons de déclarer un tableau à multiples dimensions', '/OCA Exam Objectives/Les Arrays en Java/Savoir créer un tableau à multiples dimensions/');
INSERT INTO chapter (id, name, parent_path) VALUES(626, 'Connaître les façons de déclarer un tableau à une dimension', '/OCA Exam Objectives/Les Arrays en Java/Savoir créer un tableau à une dimension/');
INSERT INTO chapter (id, name, parent_path) VALUES(543, 'Connaître les initialisations par défaut', '/OCA Exam Objectives/Types références/Savoir différencier types références et types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(674, 'Connaître les méthode de formatage de date', '/OCA Exam Objectives/Les API de base de Java/Savoir formater une date/');
INSERT INTO chapter (id, name, parent_path) VALUES(680, 'Connaître les méthodes élémentaires des arraylist', '/OCA Exam Objectives/Les API de base de Java/Maîtriser les ArrayLists/');
INSERT INTO chapter (id, name, parent_path) VALUES(656, 'Connaître les méthodes sur les StringBuilder', '/OCA Exam Objectives/Les API de base de Java/Stocker les données et les manipuler avec des String et des StringBuilder/');
INSERT INTO chapter (id, name, parent_path) VALUES(653, 'Connaître les méthodes sur les Strings', '/OCA Exam Objectives/Les API de base de Java/Stocker les données et les manipuler avec des String et des StringBuilder/');
INSERT INTO chapter (id, name, parent_path) VALUES(639, 'Connaître les objets du package java.time', '/OCA Exam Objectives/Les API de base de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(608, 'Connaître les opérateurs arithmétiques', '/OCA Exam Objectives/Utiliser les opérateurs/Différencier les différents opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(612, 'Connaître les opérateurs d''assignation', '/OCA Exam Objectives/Utiliser les opérateurs/Différencier les différents opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(613, 'Connaître les opérateurs de relation', '/OCA Exam Objectives/Utiliser les opérateurs/Différencier les différents opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(610, 'Connaître les opérateurs logiques', '/OCA Exam Objectives/Utiliser les opérateurs/Différencier les différents opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(507, 'Connaître les règles de nommage', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(592, 'Connaître les spécificités de Java', '/OCA Exam Objectives/Avantages de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(703, 'Connaître les structures autorisant chaque structure de contrôle avancée', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les structures de contrôles avancées/');
INSERT INTO chapter (id, name, parent_path) VALUES(686, 'Connaître les types de données supportés', '/OCA Exam Objectives/Les structures de contrôle/Utiliser une structure switch/');
INSERT INTO chapter (id, name, parent_path) VALUES(548, 'Connaître les types décimaux', '/OCA Exam Objectives/Topologie des variables/Les types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(545, 'Connaître les types entiers', '/OCA Exam Objectives/Topologie des variables/Les types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(459, 'Connexion', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/');
INSERT INTO chapter (id, name, parent_path) VALUES(132, 'CONSTRUCTORS', '/OCA/CH 1 Java Building Blocks/CREATING OBJECTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(428, 'Construire des applications de BDD avec JDBC', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(300, 'Construire et charger un Resources Bundle pour chaque paramètre régional dans une application', '/OCP Exam Objectives/Localisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(219, 'Consumer et BiConsumer', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/Utiliser les interfaces intégrées dans le package java.util.fonction/');
INSERT INTO chapter (id, name, parent_path) VALUES(25, 'Control Flow', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(130, 'CREATING A NEW PACKAGE', '/OCA/CH 1 Java Building Blocks/UNDERSTANDING PACKAGE DECLARATIONS AND IMPORTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(119, 'CREATING OBJECTS', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(118, 'CREATING OBJECTS', '/OCA/CH 1 Java Building Blocks/Comprendre la structure d''une classe Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(182, 'Créer des classes imbriquées', '/OCP Exam Objectives/Conception avancée de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(251, 'Créer des exceptions personnalisées', '/OCP Exam Objectives/Exceptions et Assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(259, 'Créer des ressources à fermeture automatique', '/OCP Exam Objectives/Exceptions et Assertions/Utiliser l''instruction try-with-resources/');
INSERT INTO chapter (id, name, parent_path) VALUES(287, 'Créer des streams manipulant des types primitifs', '/OCP Exam Objectives/API Stream de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(434, 'Créer des streams parallèles', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/');
INSERT INTO chapter (id, name, parent_path) VALUES(394, 'Créer des threads de travail pour éxecuter des tâches de manière concurrente', '/OCP Exam Objectives/Concurrence en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(710, 'Créer et appeler une méthode qui jette une exception', '/OCA Exam Objectives/Les exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(301, 'Créer et lire un fichier Properties', '/OCP Exam Objectives/Localisation/Construire et charger un Resources Bundle pour chaque paramètre régional dans une application/');
INSERT INTO chapter (id, name, parent_path) VALUES(670, 'Créer et utiliser des boucles do while', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les boucles/');
INSERT INTO chapter (id, name, parent_path) VALUES(676, 'Créer et utiliser des boucles for classique', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les boucles/');
INSERT INTO chapter (id, name, parent_path) VALUES(673, 'Créer et utiliser des boucles for each', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les boucles/');
INSERT INTO chapter (id, name, parent_path) VALUES(693, 'Créer et utiliser des boucles imbriquées', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les structures de contrôles avancées/');
INSERT INTO chapter (id, name, parent_path) VALUES(198, 'Créer et utiliser des interfaces fonctionnelles', '/OCP Exam Objectives/Conception avancée de Classes Java/Créer et utiliser des interfaces fonctionnelles avec des Lambda/');
INSERT INTO chapter (id, name, parent_path) VALUES(197, 'Créer et utiliser des interfaces fonctionnelles avec des Lambda', '/OCP Exam Objectives/Conception avancée de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(298, 'Créer et utiliser l''objet Local', '/OCP Exam Objectives/Localisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(663, 'Créer et utiliser les boucles while', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les boucles/');
INSERT INTO chapter (id, name, parent_path) VALUES(206, 'Créer et utiliser les objets de Collections', '/OCP Exam Objectives/Généricité et Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(202, 'Créer et utiliser une classe générique', '/OCP Exam Objectives/Généricité et Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(549, 'Créer les méthodes', '/OCA Exam Objectives/Les méthodes et l''encapsulation/');
INSERT INTO chapter (id, name, parent_path) VALUES(334, 'Créer un chemin relatif entre deux fichiers avec la méthode relativize()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(332, 'Créer un nouveau chemin avec subpath()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(580, 'Créer un nouveau paquet ', '/OCA Exam Objectives/L''organisation en paquets de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(261, 'Créer un objet avec la méthode reduce()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(310, 'Créer un objet File', '/OCP Exam Objectives/Les fondamentaux de Java IO/Conceptualiser le système de fichier avec l''objet File/');
INSERT INTO chapter (id, name, parent_path) VALUES(317, 'Créer un objet Path', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/La classe Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(342, 'Créer un ou plusieurs dossier avec les méthodes createDirectory() et createDirectories()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(750, 'Créer un projet', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(760, 'Créer un projet à partir de rien - git init', '/Créer un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(654, 'Créer un StringBuilder', '/OCA Exam Objectives/Les API de base de Java/Stocker les données et les manipuler avec des String et des StringBuilder/');
INSERT INTO chapter (id, name, parent_path) VALUES(399, 'Créer un thread', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/');
INSERT INTO chapter (id, name, parent_path) VALUES(274, 'Créer une assertion', '/OCP Exam Objectives/Exceptions et Assertions/Tester l''invariance avec les assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(797, 'Créer une branche - git branch', '/Modifier un projet/Travailler avec les branches/');
INSERT INTO chapter (id, name, parent_path) VALUES(303, 'Créer une classe de configuration', '/OCP Exam Objectives/Localisation/Construire et charger un Resources Bundle pour chaque paramètre régional dans une application/');
INSERT INTO chapter (id, name, parent_path) VALUES(262, 'Créer une collection à partir d''un stream avec la méthode collect()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(743, 'Créer une constante avec le mot-clé final', '/OCA Exam Objectives/Topologie des variables/Les différentes sortes de variables dans une classe/');
INSERT INTO chapter (id, name, parent_path) VALUES(286, 'Créer une date', '/OCP Exam Objectives/Utiliser l''API Date-Time de Java SE 8/');
INSERT INTO chapter (id, name, parent_path) VALUES(741, 'Créer une date avec la méthode of()', '/OCA Exam Objectives/Les API de base de Java/Savoir créer un objet date/');
INSERT INTO chapter (id, name, parent_path) VALUES(150, 'Customisation des beans et des définitions', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(827, 'Cycle de vie d''un composant et hooks', '/Notion de composant Vue.JS/');
INSERT INTO chapter (id, name, parent_path) VALUES(499, 'Cycle de vie des objets', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(463, 'DataSource', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/Connexion/Récupérer un connexion à la base de données/');
INSERT INTO chapter (id, name, parent_path) VALUES(453, 'DeadLock', '/OCP Exam Objectives/Concurrence en Java/Les problèmes de concurrence potentiels/');
INSERT INTO chapter (id, name, parent_path) VALUES(850, 'Déclaration du routage d''une application', '/Single Page : Vue Router/');
INSERT INTO chapter (id, name, parent_path) VALUES(111, 'DECLARE METHODS THAT DECLARE EXCEPTIONS', '/OCA EXAM ESSENTIALS/CHAPITRE 6 : EXCEPTIONS/');
INSERT INTO chapter (id, name, parent_path) VALUES(566, 'Déclarer une constante de classe (à supprimer)', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer le mot clé static /');
INSERT INTO chapter (id, name, parent_path) VALUES(195, 'Déclarer, implémenter et étendre des interfaces et utilisation de l''annotation @Override', '/OCP Exam Objectives/Conception avancée de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(121, 'DECLARING AND INITIALIZING VARIABLES', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(140, 'DECLARING MULTIPLE VARIABLES', '/OCA/CH 1 Java Building Blocks/DECLARING AND INITIALIZING VARIABLES/');
INSERT INTO chapter (id, name, parent_path) VALUES(848, 'Déclenchement de mutation par les actions', '/Introduction à VueX/Les composants de VueX/');
INSERT INTO chapter (id, name, parent_path) VALUES(345, 'Déplacer un ficher avec la méthode move()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(851, 'Déploiement d''une application en Vue.JS', '/Mettre en ligne une application avec Vue/');
INSERT INTO chapter (id, name, parent_path) VALUES(375, 'Depth-First Search', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/Concepts de navigation/');
INSERT INTO chapter (id, name, parent_path) VALUES(289, 'Depuis un autre stream avec les méthodes de mapping primitives', '/OCP Exam Objectives/API Stream de Java/Créer des streams manipulant des types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(125, 'DESTROYING OBJECTS', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(304, 'Déterminer la ressource utilisée grâce à l''objet Local', '/OCP Exam Objectives/Localisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(727, 'Déterminer quand le casting est nécessaire', '/OCA Exam Objectives/L''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(243, 'Développer avec la classe Optionnal', '/OCP Exam Objectives/API Stream de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(242, 'Développer en utilisant les versions primitives des interfaces fonctionnelles', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(29, 'Diagnostic', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(636, 'Différence avec un Array', '/OCA Exam Objectives/Les Arrays en Java/Maîtriser les Varargs/');
INSERT INTO chapter (id, name, parent_path) VALUES(232, 'Différence entre Comparable et Comparator', '/OCP Exam Objectives/Généricité et Collections/Utiliser les interfaces de comparaison/');
INSERT INTO chapter (id, name, parent_path) VALUES(192, 'Différencier les classes imbriquées', '/OCP Exam Objectives/Conception avancée de Classes Java/Créer des classes imbriquées/');
INSERT INTO chapter (id, name, parent_path) VALUES(602, 'Différencier les différents opérateurs', '/OCA Exam Objectives/Utiliser les opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(108, 'DIFFERENTIATE BETWEEN CHECKED AND UNCHECKED EXCEPTIONS', '/OCA EXAM ESSENTIALS/CHAPITRE 6 : EXCEPTIONS/');
INSERT INTO chapter (id, name, parent_path) VALUES(120, 'DISTINGUISHING BETWEEN OBJECT REFERENCES AND PRIMITIVES', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(764, 'Dossier de travail', '/Les zones de travail/');
INSERT INTO chapter (id, name, parent_path) VALUES(462, 'Driver', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/Connexion/Récupérer un connexion à la base de données/');
INSERT INTO chapter (id, name, parent_path) VALUES(344, 'Dupliquer un fichier avec les méthodes copy(path, path), copy(in,path) et copy(path, out)', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(647, 'Ecrire des expressions lambda simples', '/OCA Exam Objectives/Les API de base de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(373, 'Ecrire et lire les données entre la mémoire et le disque avec la Sérialisation', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/');
INSERT INTO chapter (id, name, parent_path) VALUES(562, 'Écrire un constructeur valide', '/OCA Exam Objectives/Cycle de vie des objets/Les constructeurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(348, 'Ecrire un fichier en créant un object bufferedWritter() a partir d''un object path', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(716, 'Error', '/OCA Exam Objectives/Les exceptions/Connaître les différents types d''exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(94, 'EVALUATE CODE INVOLVING CONSTRUCTORS', '/OCA EXAM ESSENTIALS/CHAPITRE 4 : METHODS AND ENCAPSULATION/');
INSERT INTO chapter (id, name, parent_path) VALUES(380, 'Eviter les chemins infinis', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/Naviguer avec la méthode walk()/');
INSERT INTO chapter (id, name, parent_path) VALUES(439, 'Eviter les opérations stateful', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/Exécuter des tâches en parallèle/');
INSERT INTO chapter (id, name, parent_path) VALUES(249, 'Exceptions et Assertions', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(408, 'Exécuter de nouvelles tâches/Threads', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/');
INSERT INTO chapter (id, name, parent_path) VALUES(435, 'Exécuter des tâches en parallèle', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/');
INSERT INTO chapter (id, name, parent_path) VALUES(490, 'Exécuter un programme Java en ligne de commande', '/OCA Exam Objectives/L''exécution en Java/Comprendre le processus d''exécution/');
INSERT INTO chapter (id, name, parent_path) VALUES(488, 'Exécuter un programme Java en ligne de commande [mauvais endroit, à supprimer]', '/OCA Exam Objectives/L''exécution en Java/Comprendre le processus de compilation/');
INSERT INTO chapter (id, name, parent_path) VALUES(466, 'Executer un statement', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/Statement/');
INSERT INTO chapter (id, name, parent_path) VALUES(406, 'Exécuter une nouvelle tâche/Thread', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/');
INSERT INTO chapter (id, name, parent_path) VALUES(441, 'Executer une tâche basée sur un ordre précis', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/Les réductions parallèles/');
INSERT INTO chapter (id, name, parent_path) VALUES(659, 'expression ternaire', '/OCA Exam Objectives/Les structures de contrôle/Comprendre les expressions conditionnelles/');
INSERT INTO chapter (id, name, parent_path) VALUES(525, 'Faire et reconnaître la Javadoc', '/OCA Exam Objectives/Structure d''une classe Java/Maîtriser les commentaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(21, 'Features ES11', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(18, 'Features ES6', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(19, 'Features ES7 & ES8', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(20, 'Features ES9 - ES10', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(472, 'Fermer toutes les ressources', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/');
INSERT INTO chapter (id, name, parent_path) VALUES(330, 'Fermer un Stream avec la méthode close()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Manipuler un Stream/');
INSERT INTO chapter (id, name, parent_path) VALUES(113, 'FIELDS AND METHODS', '/OCA/CH 1 Java Building Blocks/Comprendre la structure d''une classe Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(26, 'File System', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(276, 'Filtrer avec la méthode filter()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations intermédiaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(719, 'finally', '/OCA Exam Objectives/Les exceptions/Connaître les effets de try-catch et l''impact sur le flux du programme/');
INSERT INTO chapter (id, name, parent_path) VALUES(384, 'Formatter des représentations d''objets avec les classes PrintStream et PrintWriter', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/');
INSERT INTO chapter (id, name, parent_path) VALUES(305, 'Formatter en fonction de la Locale', '/OCP Exam Objectives/Localisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(407, 'Formatter l''affichage avec les méthodes format() et printf()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données depuis la console/');
INSERT INTO chapter (id, name, parent_path) VALUES(309, 'Formatter les Dates et Times', '/OCP Exam Objectives/Localisation/Formatter en fonction de la Locale/');
INSERT INTO chapter (id, name, parent_path) VALUES(306, 'Formatter les nombres', '/OCP Exam Objectives/Localisation/Formatter en fonction de la Locale/');
INSERT INTO chapter (id, name, parent_path) VALUES(224, 'Function et BiFunction', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/Utiliser les interfaces intégrées dans le package java.util.fonction/');
INSERT INTO chapter (id, name, parent_path) VALUES(803, 'Fusionner deux branches', '/Modifier un projet/Travailler avec les branches/');
INSERT INTO chapter (id, name, parent_path) VALUES(336, 'Fusionner deux chemins avec la méthode resolve()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(144, 'GARBAGE COLLECTION', '/OCA/CH 1 Java Building Blocks/DESTROYING OBJECTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(200, 'Généricité et Collections', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(364, 'Gérer le propriétaire du fichier avec les méthodes getOwner() et setOwner()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/');
INSERT INTO chapter (id, name, parent_path) VALUES(521, 'Gérer les classes dans un fichier', '/OCA Exam Objectives/Structure d''une classe Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(587, 'Gérer les conflits de nom multi import', '/OCA Exam Objectives/L''organisation en paquets de Java/Importer des paquets externes/');
INSERT INTO chapter (id, name, parent_path) VALUES(473, 'Gérer les exceptions', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/');
INSERT INTO chapter (id, name, parent_path) VALUES(363, 'Gérer les modifications de fichier avec les méthodes getLastModifierTime() et setLastModifiedTime()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/');
INSERT INTO chapter (id, name, parent_path) VALUES(17, 'Gestion de l''asynchrone', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(444, 'Gestion des processus simultanés', '/OCP Exam Objectives/Concurrence en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(818, 'git blame', '/Récupérer les modifications/Lister les modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(788, 'git checkout', '/Modifier un projet/Annuler mes modifications non validées/');
INSERT INTO chapter (id, name, parent_path) VALUES(815, 'git cherry-pick', '/Récupérer les modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(795, 'git commit --amend', '/Modifier un projet/Modifier des commits/');
INSERT INTO chapter (id, name, parent_path) VALUES(782, 'git diff', '/Modifier un projet/Voir mes modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(811, 'git fetch', '/Récupérer les modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(817, 'git log', '/Récupérer les modifications/Lister les modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(804, 'git merge', '/Modifier un projet/Travailler avec les branches/Fusionner deux branches/');
INSERT INTO chapter (id, name, parent_path) VALUES(813, 'git merge', '/Récupérer les modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(812, 'git pull', '/Récupérer les modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(805, 'git rebase', '/Modifier un projet/Travailler avec les branches/Fusionner deux branches/');
INSERT INTO chapter (id, name, parent_path) VALUES(814, 'git rebase', '/Récupérer les modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(796, 'git rebase -i', '/Modifier un projet/Modifier des commits/');
INSERT INTO chapter (id, name, parent_path) VALUES(790, 'git reset', '/Modifier un projet/Annuler mes modifications validées/');
INSERT INTO chapter (id, name, parent_path) VALUES(787, 'git restore', '/Modifier un projet/Annuler mes modifications non validées/');
INSERT INTO chapter (id, name, parent_path) VALUES(793, 'git revert', '/Modifier un projet/Annuler mes modifications validées/');
INSERT INTO chapter (id, name, parent_path) VALUES(783, 'git stash', '/Modifier un projet/Mettre mes modifications en attente/');
INSERT INTO chapter (id, name, parent_path) VALUES(781, 'git status', '/Modifier un projet/Voir mes modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(821, 'Gitflow', '/Bonnes pratiques/');
INSERT INTO chapter (id, name, parent_path) VALUES(792, 'hard', '/Modifier un projet/Annuler mes modifications validées/git reset/');
INSERT INTO chapter (id, name, parent_path) VALUES(768, 'HEAD', '/Les zones de travail/');
INSERT INTO chapter (id, name, parent_path) VALUES(684, 'Héritage et polymorphisme', '/OCA Exam Objectives/L''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(449, 'Identifier les bases de données relatives', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Bases de données relatives/');
INSERT INTO chapter (id, name, parent_path) VALUES(240, 'Identifier les interfaces à utiliser', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/Utiliser les interfaces intégrées dans le package java.util.fonction/');
INSERT INTO chapter (id, name, parent_path) VALUES(779, 'Identifier une version du projet', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(141, 'IDENTIFIERS', '/OCA/CH 1 Java Building Blocks/DECLARING AND INITIALIZING VARIABLES/');
INSERT INTO chapter (id, name, parent_path) VALUES(91, 'IDENTIFY WHEN A METHOD OR FIELD IS ACCESSIBLE', '/OCA EXAM ESSENTIALS/CHAPITRE 4 : METHODS AND ENCAPSULATION/');
INSERT INTO chapter (id, name, parent_path) VALUES(110, 'IDENTIFY WHETHER AN EXCEPTION IS THROWN BY THE PROGRAMMER OR THE JVM', '/OCA EXAM ESSENTIALS/CHAPITRE 6 : EXCEPTIONS/');
INSERT INTO chapter (id, name, parent_path) VALUES(658, 'if - if/else', '/OCA Exam Objectives/Les structures de contrôle/Comprendre les expressions conditionnelles/');
INSERT INTO chapter (id, name, parent_path) VALUES(725, 'IllegalArgumentException', '/OCA Exam Objectives/Les exceptions/Connaître les exceptions communes/');
INSERT INTO chapter (id, name, parent_path) VALUES(737, 'Implémentation d''une interface', '/OCA Exam Objectives/L''héritage en Java/Classes abstraites et interfaces/');
INSERT INTO chapter (id, name, parent_path) VALUES(738, 'Implémentation de plusieurs interfaces', '/OCA Exam Objectives/L''héritage en Java/Classes abstraites et interfaces/');
INSERT INTO chapter (id, name, parent_path) VALUES(173, 'Implémenter l''encapsulation', '/OCP Exam Objectives/Conception de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(174, 'Implémenter l''héritage', '/OCP Exam Objectives/Conception de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(374, 'Implémenter l''interface Serializable', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Ecrire et lire les données entre la mémoire et le disque avec la Sérialisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(177, 'Implémenter la composition', '/OCP Exam Objectives/Conception de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(179, 'Implémenter le polymorphisme', '/OCP Exam Objectives/Conception de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(183, 'Implémenter les Design Patterns', '/OCP Exam Objectives/Conception de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(742, 'Implémenter une interface fonctionnelle', '/OCA Exam Objectives/Les API de base de Java/Ecrire des expressions lambda simples/');
INSERT INTO chapter (id, name, parent_path) VALUES(579, 'Importer des paquets externes', '/OCA Exam Objectives/L''organisation en paquets de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(149, 'Injection de dépendances', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(143, 'INSTANCE AND CLASS VARIABLES', '/OCA/CH 1 Java Building Blocks/UNDERSTANDING DEFAULT INITIALIZATION OF VARIABLES/');
INSERT INTO chapter (id, name, parent_path) VALUES(134, 'INSTANCE INITIALIZER', '/OCA/CH 1 Java Building Blocks/CREATING OBJECTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(135, 'INSTANCE INITIALIZER BLOCKS', '/OCA/CH 1 Java Building Blocks/CREATING OBJECTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(321, 'Interactions entre Paths et Files', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/');
INSERT INTO chapter (id, name, parent_path) VALUES(210, 'Interfaces fonctionnelles intégrées à Java', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(423, 'Introduction', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(452, 'Introduction', '/OCP Exam Objectives/Concurrence en Java/Les problèmes de concurrence potentiels/');
INSERT INTO chapter (id, name, parent_path) VALUES(415, 'Introduction', '/OCP Exam Objectives/Concurrence en Java/Synchroniser les accès aux données/');
INSERT INTO chapter (id, name, parent_path) VALUES(446, 'Introduction', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Bases de données relatives/');
INSERT INTO chapter (id, name, parent_path) VALUES(458, 'Introduction', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/');
INSERT INTO chapter (id, name, parent_path) VALUES(316, 'Introduction', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/La classe Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(146, 'Introduction à Spring Framework', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(840, 'Introduction à VueX', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(426, 'Introduction et liste des types', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/Les types de collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(355, 'isDirectory()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/Lire la nature de l''élement pointé par le path/');
INSERT INTO chapter (id, name, parent_path) VALUES(358, 'isRegularFile()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/Lire la nature de l''élement pointé par le path/');
INSERT INTO chapter (id, name, parent_path) VALUES(359, 'isSymbolicLink()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/Lire la nature de l''élement pointé par le path/');
INSERT INTO chapter (id, name, parent_path) VALUES(239, 'Itérer avec forEach()', '/OCP Exam Objectives/Généricité et Collections/Manipuler une collection avec les expressions Lambda/');
INSERT INTO chapter (id, name, parent_path) VALUES(260, 'Itérer dans un stream avec la méthode forEach()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(312, 'Java I-O de fichiers (NIO.2)', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(152, 'Java-based container configuration', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(460, 'JDBC URL', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/Connexion/');
INSERT INTO chapter (id, name, parent_path) VALUES(139, 'KEY DIFFERENCES', '/OCA/CH 1 Java Building Blocks/DISTINGUISHING BETWEEN OBJECT REFERENCES AND PRIMITIVES/');
INSERT INTO chapter (id, name, parent_path) VALUES(73, 'KNOW HOW TO IDENTIFY WHEN AN OBJECTIS ELIGIBLE FOR GARBAGE COLLECTION', '/OCA EXAM ESSENTIALS/CHAPITRE 1 : JAVA BUILDING BLOCKS/');
INSERT INTO chapter (id, name, parent_path) VALUES(481, 'L''exécution en Java', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(512, 'L''héritage en Java', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(398, 'L''interface Callable', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/');
INSERT INTO chapter (id, name, parent_path) VALUES(397, 'L''interface Runnable', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/');
INSERT INTO chapter (id, name, parent_path) VALUES(323, 'L''object Path', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/');
INSERT INTO chapter (id, name, parent_path) VALUES(500, 'L''organisation en paquets de Java', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(445, 'La classe CyclicBarrier', '/OCP Exam Objectives/Concurrence en Java/Gestion des processus simultanés/');
INSERT INTO chapter (id, name, parent_path) VALUES(315, 'La classe Path', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/');
INSERT INTO chapter (id, name, parent_path) VALUES(448, 'La classe RecursiveTask', '/OCP Exam Objectives/Concurrence en Java/Gestion des processus simultanés/');
INSERT INTO chapter (id, name, parent_path) VALUES(392, 'La concurence des threads', '/OCP Exam Objectives/Concurrence en Java/Les threads/');
INSERT INTO chapter (id, name, parent_path) VALUES(214, 'La méthode merge()', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/Maps/');
INSERT INTO chapter (id, name, parent_path) VALUES(213, 'La méthode putIf()', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/Maps/');
INSERT INTO chapter (id, name, parent_path) VALUES(378, 'La méthode walk()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/Naviguer avec la méthode walk()/');
INSERT INTO chapter (id, name, parent_path) VALUES(661, 'La mise en place de l''héritage en Java', '/OCA Exam Objectives/L''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(642, 'La notion d''héritage', '/OCA Exam Objectives/L''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(268, 'Le collector toMap()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/Créer une collection à partir d''un stream avec la méthode collect()/Utiliser collect() avec les Collectors/');
INSERT INTO chapter (id, name, parent_path) VALUES(557, 'Le corps d''une méthode', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Créer les méthodes/');
INSERT INTO chapter (id, name, parent_path) VALUES(420, 'Le coût de la synchronisation', '/OCP Exam Objectives/Concurrence en Java/Synchroniser les accès aux données/');
INSERT INTO chapter (id, name, parent_path) VALUES(402, 'Le ExecutorService', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/');
INSERT INTO chapter (id, name, parent_path) VALUES(447, 'Le framework Fork-Join', '/OCP Exam Objectives/Concurrence en Java/Gestion des processus simultanés/');
INSERT INTO chapter (id, name, parent_path) VALUES(186, 'Le Pattern Immutabilité', '/OCP Exam Objectives/Conception de Classes Java/Implémenter les Design Patterns/');
INSERT INTO chapter (id, name, parent_path) VALUES(185, 'Le Pattern Singleton', '/OCP Exam Objectives/Conception de Classes Java/Implémenter les Design Patterns/');
INSERT INTO chapter (id, name, parent_path) VALUES(594, 'Le processus d''initialisation', '/OCA Exam Objectives/Cycle de vie des objets/');
INSERT INTO chapter (id, name, parent_path) VALUES(615, 'Le processus de suppression', '/OCA Exam Objectives/Cycle de vie des objets/');
INSERT INTO chapter (id, name, parent_path) VALUES(833, 'Le rendu conditionnel v-if, v-else, v-else-if et v-show', '/Affichage de données et directives/Les directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(834, 'Le rendu multiple avec v-for', '/Affichage de données et directives/Les directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(836, 'Le rendu stylistique et dynamique avec v-bind', '/Affichage de données et directives/Les directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(846, 'Le store de VueX', '/Introduction à VueX/Les composants de VueX/');
INSERT INTO chapter (id, name, parent_path) VALUES(263, 'Le try-with-resources', '/OCP Exam Objectives/Exceptions et Assertions/Utiliser l''instruction try-with-resources/');
INSERT INTO chapter (id, name, parent_path) VALUES(509, 'Les API de base de Java', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(506, 'Les Arrays en Java', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(749, 'Les bases', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(244, 'Les bases', '/OCP Exam Objectives/API Stream de Java/Développer avec la classe Optionnal/');
INSERT INTO chapter (id, name, parent_path) VALUES(2, 'Les bases de Vue.JS', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(418, 'Les blocs synchronisés', '/OCP Exam Objectives/Concurrence en Java/Synchroniser les accès aux données/');
INSERT INTO chapter (id, name, parent_path) VALUES(245, 'Les chaines d''instruction', '/OCP Exam Objectives/API Stream de Java/Développer avec la classe Optionnal/');
INSERT INTO chapter (id, name, parent_path) VALUES(417, 'Les classes atomiques', '/OCP Exam Objectives/Concurrence en Java/Synchroniser les accès aux données/');
INSERT INTO chapter (id, name, parent_path) VALUES(352, 'Les Classes de Stream', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/');
INSERT INTO chapter (id, name, parent_path) VALUES(422, 'Les collections concurrentes', '/OCP Exam Objectives/Concurrence en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(474, 'Les collections concurrentes', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/Les types de collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(430, 'Les collections CopyOnWrite', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/Les types de collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(429, 'Les collections skipList', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/Les types de collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(431, 'Les collections synchronisées', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/Les types de collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(267, 'Les collectors basiques joining() et averaging()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/Créer une collection à partir d''un stream avec la méthode collect()/Utiliser collect() avec les Collectors/');
INSERT INTO chapter (id, name, parent_path) VALUES(845, 'Les composants de VueX', '/Introduction à VueX/');
INSERT INTO chapter (id, name, parent_path) VALUES(559, 'Les constructeurs', '/OCA Exam Objectives/Cycle de vie des objets/');
INSERT INTO chapter (id, name, parent_path) VALUES(528, 'Les différentes sortes de variables dans une classe', '/OCA Exam Objectives/Topologie des variables/');
INSERT INTO chapter (id, name, parent_path) VALUES(832, 'Les directives', '/Affichage de données et directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(830, 'Les éléments d''une instance de Vue', '/Affichage de données et directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(424, 'Les erreurs de cohérence dans la mémoire', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(514, 'Les exceptions', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(265, 'Les exceptions supprimées', '/OCP Exam Objectives/Exceptions et Assertions/Utiliser l''instruction try-with-resources/');
INSERT INTO chapter (id, name, parent_path) VALUES(299, 'Les fondamentaux de Java IO', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(837, 'Les formulaires et v-model', '/Affichage de données et directives/Les directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(455, 'Les interfaces JDBC, leur utilités et relations', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/');
INSERT INTO chapter (id, name, parent_path) VALUES(212, 'Les méthode de base', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/Maps/');
INSERT INTO chapter (id, name, parent_path) VALUES(216, 'Les méthodes computeIfPresent() et computeIfAbsent()', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/Maps/');
INSERT INTO chapter (id, name, parent_path) VALUES(498, 'Les méthodes et l''encapsulation', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(270, 'Les méthodes groupingBy(), partitioningBy() et mapping()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/Créer une collection à partir d''un stream avec la méthode collect()/Utiliser collect() avec les Collectors/');
INSERT INTO chapter (id, name, parent_path) VALUES(419, 'Les méthodes synchronisées', '/OCP Exam Objectives/Concurrence en Java/Synchroniser les accès aux données/');
INSERT INTO chapter (id, name, parent_path) VALUES(438, 'Les opérations indépendantes', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/Exécuter des tâches en parallèle/');
INSERT INTO chapter (id, name, parent_path) VALUES(291, 'Les Optionals avec les streams primitifs', '/OCP Exam Objectives/API Stream de Java/Créer des streams manipulant des types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(556, 'Les paramètres d''une méthode', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Créer les méthodes/');
INSERT INTO chapter (id, name, parent_path) VALUES(450, 'Les problèmes de concurrence potentiels', '/OCP Exam Objectives/Concurrence en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(252, 'Les réductions', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(440, 'Les réductions parallèles', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/');
INSERT INTO chapter (id, name, parent_path) VALUES(433, 'Les streams parallèles', '/OCP Exam Objectives/Concurrence en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(503, 'Les structures de contrôle', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(388, 'Les threads', '/OCP Exam Objectives/Concurrence en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(403, 'Les threads Executor simples', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/');
INSERT INTO chapter (id, name, parent_path) VALUES(425, 'Les types de collections concurrentes', '/OCP Exam Objectives/Concurrence en Java/Les collections concurrentes/');
INSERT INTO chapter (id, name, parent_path) VALUES(390, 'Les types de thread', '/OCP Exam Objectives/Concurrence en Java/Les threads/');
INSERT INTO chapter (id, name, parent_path) VALUES(288, 'Les types et les méthodes de base ', '/OCP Exam Objectives/API Stream de Java/Créer des streams manipulant des types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(542, 'Les types primitifs', '/OCA Exam Objectives/Topologie des variables/');
INSERT INTO chapter (id, name, parent_path) VALUES(751, 'Les zones de travail', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(757, 'Linux', '/Les bases/Comment installer Git/');
INSERT INTO chapter (id, name, parent_path) VALUES(400, 'Lire et écrire avec les méthodes reader() et writer()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données depuis la console/');
INSERT INTO chapter (id, name, parent_path) VALUES(313, 'Lire et écrire des données', '/OCP Exam Objectives/Les fondamentaux de Java IO/');
INSERT INTO chapter (id, name, parent_path) VALUES(395, 'Lire et écrire des données depuis la console', '/OCP Exam Objectives/Les fondamentaux de Java IO/');
INSERT INTO chapter (id, name, parent_path) VALUES(353, 'Lire la nature de l''élement pointé par le path', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/');
INSERT INTO chapter (id, name, parent_path) VALUES(362, 'Lire la taille d''un fichier avec la méthode size()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/');
INSERT INTO chapter (id, name, parent_path) VALUES(368, 'Lire les attributs', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/Accés au métadonnées via des vues/');
INSERT INTO chapter (id, name, parent_path) VALUES(347, 'Lire un fichier en créant un object bufferedReader() a partir d''un object path', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(421, 'Lire un mot de passe ou données confidentielles avec la méthode readPassword()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données depuis la console/');
INSERT INTO chapter (id, name, parent_path) VALUES(349, 'Lire un objet Files avec la méthode readAllLines()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(416, 'Lire une simple ligne avec la méthode readLine()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données depuis la console/');
INSERT INTO chapter (id, name, parent_path) VALUES(382, 'Lister le contenu d''un dossier avec la méthode List()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/');
INSERT INTO chapter (id, name, parent_path) VALUES(816, 'Lister les modifications', '/Récupérer les modifications/');
INSERT INTO chapter (id, name, parent_path) VALUES(806, 'Lister les tags - git tag', '/Modifier un projet/Identifier une version du projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(207, 'Listes', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(456, 'LiveLock', '/OCP Exam Objectives/Concurrence en Java/Les problèmes de concurrence potentiels/');
INSERT INTO chapter (id, name, parent_path) VALUES(142, 'LOCAL VARIABLES', '/OCA/CH 1 Java Building Blocks/UNDERSTANDING DEFAULT INITIALIZATION OF VARIABLES/');
INSERT INTO chapter (id, name, parent_path) VALUES(296, 'Localisation', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(759, 'Mac', '/Les bases/Comment installer Git/');
INSERT INTO chapter (id, name, parent_path) VALUES(689, 'Maîtriser l''autoboxing', '/OCA Exam Objectives/Les API de base de Java/Wrapper Classes/');
INSERT INTO chapter (id, name, parent_path) VALUES(668, 'Maîtriser l''immuabilité des dates', '/OCA Exam Objectives/Les API de base de Java/Savoir modifier une date/');
INSERT INTO chapter (id, name, parent_path) VALUES(505, 'Maîtriser l''initialisation de variable locale', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(515, 'Maîtriser l''initialisation multiple', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/Maîtriser l''initialisation de variable locale/');
INSERT INTO chapter (id, name, parent_path) VALUES(513, 'Maîtriser l''initialisation simple', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/Maîtriser l''initialisation de variable locale/');
INSERT INTO chapter (id, name, parent_path) VALUES(581, 'Maîtriser l''ordre des éléments dans un fichier', '/OCA Exam Objectives/L''organisation en paquets de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(603, 'Maîtriser l''ordre des opérations', '/OCA Exam Objectives/Utiliser les opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(504, 'Maîtriser la déclaration de variable locale', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(511, 'Maîtriser la déclaration multiple', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/Maîtriser la déclaration de variable locale/');
INSERT INTO chapter (id, name, parent_path) VALUES(510, 'Maîtriser la déclaration simple', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/Maîtriser la déclaration de variable locale/');
INSERT INTO chapter (id, name, parent_path) VALUES(624, 'Maîtriser la méthode finalize() et ses conséquences', '/OCA Exam Objectives/Cycle de vie des objets/');
INSERT INTO chapter (id, name, parent_path) VALUES(672, 'Maîtriser la modification avec les périodes', '/OCA Exam Objectives/Les API de base de Java/Savoir modifier une date/');
INSERT INTO chapter (id, name, parent_path) VALUES(702, 'Maîtriser la notion de predicat et savoir l''appliquer', '/OCA Exam Objectives/Les API de base de Java/Ecrire des expressions lambda simples/');
INSERT INTO chapter (id, name, parent_path) VALUES(605, 'Maîtriser la promotion numérique', '/OCA Exam Objectives/Utiliser les opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(682, 'Maîtriser la recherche avec les ArrayList', '/OCA Exam Objectives/Les API de base de Java/Maîtriser les ArrayLists/');
INSERT INTO chapter (id, name, parent_path) VALUES(633, 'Maîtriser la recherche dans un Array', '/OCA Exam Objectives/Les Arrays en Java/Maîtriser les opérations de base sur les Arrays/');
INSERT INTO chapter (id, name, parent_path) VALUES(518, 'Maîtriser le CamelCase pour les classes', '/OCA Exam Objectives/Règles génériques de déclaration et initialisation des variables en Java/Connaître les bonnes pratiques de nommage/');
INSERT INTO chapter (id, name, parent_path) VALUES(690, 'Maîtriser le fonctionnement de l''option default', '/OCA Exam Objectives/Les structures de contrôle/Utiliser une structure switch/');
INSERT INTO chapter (id, name, parent_path) VALUES(541, 'Maîtriser le null et son utilisation avec primitif/référence', '/OCA Exam Objectives/Types références/Savoir différencier types références et types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(555, 'Maîtriser le passage de paramètre par valeur', '/OCA Exam Objectives/Les méthodes et l''encapsulation/');
INSERT INTO chapter (id, name, parent_path) VALUES(688, 'Maîtriser le placement du mot clé break et les conséquences de son absence', '/OCA Exam Objectives/Les structures de contrôle/Utiliser une structure switch/');
INSERT INTO chapter (id, name, parent_path) VALUES(681, 'Maîtriser le tri avec les ArrayList', '/OCA Exam Objectives/Les API de base de Java/Maîtriser les ArrayLists/');
INSERT INTO chapter (id, name, parent_path) VALUES(632, 'Maîtriser le tri d''un Array', '/OCA Exam Objectives/Les Arrays en Java/Maîtriser les opérations de base sur les Arrays/');
INSERT INTO chapter (id, name, parent_path) VALUES(644, 'Maîtriser les ArrayLists', '/OCA Exam Objectives/Les API de base de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(520, 'Maîtriser les commentaires', '/OCA Exam Objectives/Structure d''une classe Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(696, 'Maîtriser les conversions Array <-> ArrayList', '/OCA Exam Objectives/Les API de base de Java/Wrapper Classes/');
INSERT INTO chapter (id, name, parent_path) VALUES(691, 'Maîtriser les conversions String -> Primitives', '/OCA Exam Objectives/Les API de base de Java/Wrapper Classes/');
INSERT INTO chapter (id, name, parent_path) VALUES(694, 'Maîtriser les conversions String -> Wrapper class', '/OCA Exam Objectives/Les API de base de Java/Wrapper Classes/');
INSERT INTO chapter (id, name, parent_path) VALUES(699, 'Maîtriser les différents types de syntaxe', '/OCA Exam Objectives/Les API de base de Java/Ecrire des expressions lambda simples/');
INSERT INTO chapter (id, name, parent_path) VALUES(712, 'Maîtriser les exceptions et l''héritage', '/OCA Exam Objectives/Les exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(706, 'Maîtriser les méthodes cachées', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(704, 'Maîtriser les méthodes finales', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(739, 'Maîtriser les méthodes par défaut des interfaces', '/OCA Exam Objectives/L''héritage en Java/Classes abstraites et interfaces/');
INSERT INTO chapter (id, name, parent_path) VALUES(709, 'Maîtriser les méthodes statiques cachées', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(623, 'Maîtriser les opérations de base sur les Arrays', '/OCA Exam Objectives/Les Arrays en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(535, 'Maîtriser les types référençant un objet', '/OCA Exam Objectives/Types références/');
INSERT INTO chapter (id, name, parent_path) VALUES(625, 'Maîtriser les Varargs', '/OCA Exam Objectives/Les Arrays en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(537, 'Manipuler les variables d''instance', '/OCA Exam Objectives/Types références/');
INSERT INTO chapter (id, name, parent_path) VALUES(328, 'Manipuler un Stream', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/');
INSERT INTO chapter (id, name, parent_path) VALUES(235, 'Manipuler une collection avec les expressions Lambda', '/OCP Exam Objectives/Généricité et Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(290, 'Manipuler une date', '/OCP Exam Objectives/Utiliser l''API Date-Time de Java SE 8/');
INSERT INTO chapter (id, name, parent_path) VALUES(209, 'Maps', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(153, 'Messagerie, Emailing, exécution de méthodes asynchrones et cache avec Spring', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(204, 'Méthode, classe et interface génériques', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser une classe générique/');
INSERT INTO chapter (id, name, parent_path) VALUES(841, 'Mettre en ligne une application avec Vue', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(772, 'Mettre mes modifications en attente', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(282, 'Mettre tous les éléments de plusieurs streams au même niveau avec la méthode flatMap()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations intermédiaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(401, 'Mettre un thread en pause', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/');
INSERT INTO chapter (id, name, parent_path) VALUES(238, 'Mise à jour des éléments d''une liste avec replaceAll()', '/OCP Exam Objectives/Généricité et Collections/Manipuler une collection avec les expressions Lambda/');
INSERT INTO chapter (id, name, parent_path) VALUES(4, 'Modèle MVVM', '/Les bases de Vue.JS/');
INSERT INTO chapter (id, name, parent_path) VALUES(847, 'Modification du store avec les mutations', '/Introduction à VueX/Les composants de VueX/');
INSERT INTO chapter (id, name, parent_path) VALUES(777, 'Modifier des commits', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(370, 'Modifier les attributs', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/Accés au métadonnées via des vues/');
INSERT INTO chapter (id, name, parent_path) VALUES(280, 'Modifier les éléments d''un stream avec la méthode map()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations intermédiaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(752, 'Modifier un projet', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(27, 'Module System', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(129, 'NAMING CONFLICTS', '/OCA/CH 1 Java Building Blocks/UNDERSTANDING PACKAGE DECLARATIONS AND IMPORTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(377, 'Naviguer avec la méthode walk()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Naviguer dans les fichiers via des streams/');
INSERT INTO chapter (id, name, parent_path) VALUES(371, 'Naviguer dans les fichiers via des streams', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/');
INSERT INTO chapter (id, name, parent_path) VALUES(471, 'Naviguer dans un ResultSet', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/ResultSet/');
INSERT INTO chapter (id, name, parent_path) VALUES(339, 'Naviguer dans un Stream vec les méthodes mark() et reset()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Manipuler un Stream/');
INSERT INTO chapter (id, name, parent_path) VALUES(413, 'Nettoyer le Buffer avec la méthode flush()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données depuis la console/');
INSERT INTO chapter (id, name, parent_path) VALUES(785, 'Nettoyer ma file d''attente', '/Modifier un projet/Mettre mes modifications en attente/');
INSERT INTO chapter (id, name, parent_path) VALUES(335, 'Nettoyer un Stream avec la méthode flush()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Manipuler un Stream/');
INSERT INTO chapter (id, name, parent_path) VALUES(22, 'Node CLI', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(23, 'Node.JS Events & Event Loop', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(819, 'Nommage des commits', '/Bonnes pratiques/');
INSERT INTO chapter (id, name, parent_path) VALUES(747, 'Notion de composant Vue.JS', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(720, 'NullPointerException', '/OCA Exam Objectives/Les exceptions/Connaître les exceptions communes/');
INSERT INTO chapter (id, name, parent_path) VALUES(724, 'NumberFormatException', '/OCA Exam Objectives/Les exceptions/Connaître les exceptions communes/');
INSERT INTO chapter (id, name, parent_path) VALUES(254, 'Obtenir le nombre d''éléments avec la méthode count()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/');
INSERT INTO chapter (id, name, parent_path) VALUES(5, 'OCA', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(65, 'OCA EXAM ESSENTIALS', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(480, 'OCA Exam Objectives', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(8, 'OCP', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(32, 'OCP 11', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(168, 'OCP Exam Objectives', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(136, 'ORDER OF INITIALIZATION', '/OCA/CH 1 Java Building Blocks/CREATING OBJECTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(124, 'ORDERING ELEMENTS IN A CLASS', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(307, 'Parser les nombres', '/OCP Exam Objectives/Localisation/Formatter en fonction de la Locale/');
INSERT INTO chapter (id, name, parent_path) VALUES(776, 'Partager mes modifications - git push', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(810, 'Partager un tag - git push --tags', '/Modifier un projet/Identifier une version du projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(351, 'Passer des octets avec la méthode skip()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Manipuler un Stream/');
INSERT INTO chapter (id, name, parent_path) VALUES(831, 'Passer des variables au template', '/Affichage de données et directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(748, 'Petits pas : Quelques connaissances en Javascript (ES6)', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(496, 'Portée des variables', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(220, 'Predicate et BiPredicate', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/Utiliser les interfaces intégrées dans le package java.util.fonction/');
INSERT INTO chapter (id, name, parent_path) VALUES(297, 'Prendre en compte les heures d''été', '/OCP Exam Objectives/Utiliser l''API Date-Time de Java SE 8/');
INSERT INTO chapter (id, name, parent_path) VALUES(411, 'Prévoir le lancement d''un thread', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/');
INSERT INTO chapter (id, name, parent_path) VALUES(137, 'PRIMITIVE TYPES', '/OCA/CH 1 Java Building Blocks/DISTINGUISHING BETWEEN OBJECT REFERENCES AND PRIMITIVES/');
INSERT INTO chapter (id, name, parent_path) VALUES(560, 'Principe de constructeur', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Créer les méthodes/');
INSERT INTO chapter (id, name, parent_path) VALUES(28, 'Process/OS', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(820, 'Pull requests', '/Bonnes pratiques/');
INSERT INTO chapter (id, name, parent_path) VALUES(3, 'Qu''est-ce que Vue.JS ?', '/Les bases de Vue.JS/');
INSERT INTO chapter (id, name, parent_path) VALUES(844, 'Qu''est-ce que VueX ?', '/Introduction à VueX/');
INSERT INTO chapter (id, name, parent_path) VALUES(825, 'Quelle est la forme d''un composant Vue.JS ?', '/Notion de composant Vue.JS/');
INSERT INTO chapter (id, name, parent_path) VALUES(221, 'Queues', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(457, 'Race Conditions', '/OCP Exam Objectives/Concurrence en Java/Les problèmes de concurrence potentiels/');
INSERT INTO chapter (id, name, parent_path) VALUES(133, 'READING AND WRITING OBJECT FIELDS', '/OCA/CH 1 Java Building Blocks/CREATING OBJECTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(272, 'Récapitulatif des Collectors', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/Créer une collection à partir d''un stream avec la méthode collect()/Utiliser collect() avec les Collectors/');
INSERT INTO chapter (id, name, parent_path) VALUES(807, 'Recherche un tag - git tag -l "v1.0.*"', '/Modifier un projet/Identifier une version du projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(230, 'Rechercher un élément dans une liste triée', '/OCP Exam Objectives/Généricité et Collections/Utiliser les interfaces de comparaison/');
INSERT INTO chapter (id, name, parent_path) VALUES(88, 'RECOGNIZE INVALID USES OF DATES AND TIMES', '/OCA EXAM ESSENTIALS/CHAPITRE 3: CORE JAVA APIs/');
INSERT INTO chapter (id, name, parent_path) VALUES(101, 'RECOGNIZE THE DIFFERENCE BETWEEN METHOD OVERRIDING AND METHOD OVERLOADING', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(92, 'RECOGNIZE VALID AND INVALID USES OF STATIC IMPORTS', '/OCA EXAM ESSENTIALS/CHAPITRE 4 : METHODS AND ENCAPSULATION/');
INSERT INTO chapter (id, name, parent_path) VALUES(106, 'RECOGNIZE VALID REFERENCE CASTING', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(112, 'RECOGNIZE WHEN TO USE THROW VERSUS THROWS', '/OCA EXAM ESSENTIALS/CHAPITRE 6 : EXCEPTIONS/');
INSERT INTO chapter (id, name, parent_path) VALUES(585, 'Reconnaître et supprimer les imports redondants', '/OCA Exam Objectives/L''organisation en paquets de Java/Importer des paquets externes/');
INSERT INTO chapter (id, name, parent_path) VALUES(586, 'Reconnaitre les conflits de nom multi import', '/OCA Exam Objectives/L''organisation en paquets de Java/Importer des paquets externes/');
INSERT INTO chapter (id, name, parent_path) VALUES(538, 'Reconnaître les variables d''instance', '/OCA Exam Objectives/Topologie des variables/Les différentes sortes de variables dans une classe/');
INSERT INTO chapter (id, name, parent_path) VALUES(534, 'Reconnaître les variables de classe', '/OCA Exam Objectives/Topologie des variables/Les différentes sortes de variables dans une classe/');
INSERT INTO chapter (id, name, parent_path) VALUES(533, 'Reconnaître les variables locales', '/OCA Exam Objectives/Topologie des variables/Les différentes sortes de variables dans une classe/');
INSERT INTO chapter (id, name, parent_path) VALUES(567, 'Reconnaître un appel valide d''une méthode statique et d''une méthode d''instance', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer le mot clé static /');
INSERT INTO chapter (id, name, parent_path) VALUES(522, 'Reconnaitre une méthode valide', '/OCA Exam Objectives/Structure d''une classe Java/Reconnaître une structure valide/');
INSERT INTO chapter (id, name, parent_path) VALUES(519, 'Reconnaître une structure valide', '/OCA Exam Objectives/Structure d''une classe Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(469, 'Récupérer et lire un ResultSet', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/ResultSet/');
INSERT INTO chapter (id, name, parent_path) VALUES(285, 'Récupérer les éléments d''un stream pour le débuggage avec la méthode peek()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations intermédiaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(753, 'Récupérer les modifications', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(784, 'Récupérer mes modifications en attente', '/Modifier un projet/Mettre mes modifications en attente/');
INSERT INTO chapter (id, name, parent_path) VALUES(461, 'Récupérer un connexion à la base de données', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/Connexion/');
INSERT INTO chapter (id, name, parent_path) VALUES(279, 'Récupérer un intervalle avec les méthodes limit() et skip()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations intermédiaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(762, 'Récupérer un projet existant - git clone', '/Créer un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(465, 'Récupérer un statement', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/Statement/');
INSERT INTO chapter (id, name, parent_path) VALUES(470, 'Récupérer une donnée dans un ResultSet', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/ResultSet/');
INSERT INTO chapter (id, name, parent_path) VALUES(196, 'Redéfinir la méthode equals()', '/OCP Exam Objectives/Conception de Classes Java/Redéfinir les méthodes de base de la classe Object/');
INSERT INTO chapter (id, name, parent_path) VALUES(201, 'Redéfinir la méthode hashCode()', '/OCP Exam Objectives/Conception de Classes Java/Redéfinir les méthodes de base de la classe Object/');
INSERT INTO chapter (id, name, parent_path) VALUES(203, 'Redéfinir la méthode toString()', '/OCP Exam Objectives/Conception de Classes Java/Redéfinir les méthodes de base de la classe Object/');
INSERT INTO chapter (id, name, parent_path) VALUES(193, 'Redéfinir les méthodes de base de la classe Object', '/OCP Exam Objectives/Conception de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(128, 'REDUNDANT IMPORTS', '/OCA/CH 1 Java Building Blocks/UNDERSTANDING PACKAGE DECLARATIONS AND IMPORTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(138, 'REFERENCE TYPES', '/OCA/CH 1 Java Building Blocks/DISTINGUISHING BETWEEN OBJECT REFERENCES AND PRIMITIVES/');
INSERT INTO chapter (id, name, parent_path) VALUES(540, 'Référencer un nouvel objet', '/OCA Exam Objectives/Types références/Maîtriser les types référençant un objet/');
INSERT INTO chapter (id, name, parent_path) VALUES(493, 'Règles génériques de déclaration et initialisation des variables en Java', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(269, 'Relancer une exception', '/OCP Exam Objectives/Exceptions et Assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(479, 'Relation entre equals() et toString()', '/OCP Exam Objectives/Conception de Classes Java/Redéfinir les méthodes de base de la classe Object/');
INSERT INTO chapter (id, name, parent_path) VALUES(292, 'Remplacer l''utilisation de plusieurs méthodes terminales basiques avec SummaryStatistics', '/OCP Exam Objectives/API Stream de Java/Créer des streams manipulant des types primitifs/');
INSERT INTO chapter (id, name, parent_path) VALUES(769, 'Repository central', '/Les zones de travail/');
INSERT INTO chapter (id, name, parent_path) VALUES(767, 'Repository local', '/Les zones de travail/');
INSERT INTO chapter (id, name, parent_path) VALUES(451, 'Requête SQL basique', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Bases de données relatives/');
INSERT INTO chapter (id, name, parent_path) VALUES(467, 'ResultSet', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/');
INSERT INTO chapter (id, name, parent_path) VALUES(695, 'Savoir ajouter un label', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les structures de contrôles avancées/');
INSERT INTO chapter (id, name, parent_path) VALUES(698, 'Savoir altérer le fonctionnement avec break et un label', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les structures de contrôles avancées/');
INSERT INTO chapter (id, name, parent_path) VALUES(700, 'Savoir altérer le fonctionnement avec continue et un label', '/OCA Exam Objectives/Les structures de contrôle/Utiliser les structures de contrôles avancées/');
INSERT INTO chapter (id, name, parent_path) VALUES(640, 'Savoir créer un objet date', '/OCA Exam Objectives/Les API de base de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(622, 'Savoir créer un tableau à multiples dimensions', '/OCA Exam Objectives/Les Arrays en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(620, 'Savoir créer un tableau à une dimension', '/OCA Exam Objectives/Les Arrays en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(667, 'Savoir créer une date avec month', '/OCA Exam Objectives/Les API de base de Java/Savoir créer un objet date/');
INSERT INTO chapter (id, name, parent_path) VALUES(677, 'Savoir déclarer un arraylist', '/OCA Exam Objectives/Les API de base de Java/Maîtriser les ArrayLists/');
INSERT INTO chapter (id, name, parent_path) VALUES(526, 'Savoir définir plusieurs classes dans un fichier', '/OCA Exam Objectives/Structure d''une classe Java/Gérer les classes dans un fichier/');
INSERT INTO chapter (id, name, parent_path) VALUES(536, 'Savoir différencier types références et types primitifs', '/OCA Exam Objectives/Types références/');
INSERT INTO chapter (id, name, parent_path) VALUES(524, 'Savoir faire des commentaires multilignes', '/OCA Exam Objectives/Structure d''une classe Java/Maîtriser les commentaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(523, 'Savoir faire des commentaires sur une ligne', '/OCA Exam Objectives/Structure d''une classe Java/Maîtriser les commentaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(643, 'Savoir formater une date', '/OCA Exam Objectives/Les API de base de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(630, 'Savoir initialiser un tableau à multiple dimension', '/OCA Exam Objectives/Les Arrays en Java/Savoir créer un tableau à multiples dimensions/');
INSERT INTO chapter (id, name, parent_path) VALUES(627, 'Savoir initialiser un tableau à une dimension', '/OCA Exam Objectives/Les Arrays en Java/Savoir créer un tableau à une dimension/');
INSERT INTO chapter (id, name, parent_path) VALUES(687, 'Savoir instancier une classe wrapper', '/OCA Exam Objectives/Les API de base de Java/Wrapper Classes/');
INSERT INTO chapter (id, name, parent_path) VALUES(547, 'Savoir lire une variable d''instance', '/OCA Exam Objectives/Types références/Manipuler les variables d''instance/');
INSERT INTO chapter (id, name, parent_path) VALUES(583, 'Savoir maîtriser le caractère *', '/OCA Exam Objectives/L''organisation en paquets de Java/Importer des paquets externes/');
INSERT INTO chapter (id, name, parent_path) VALUES(584, 'Savoir maîtriser les imports statiques', '/OCA Exam Objectives/L''organisation en paquets de Java/Importer des paquets externes/');
INSERT INTO chapter (id, name, parent_path) VALUES(577, 'Savoir modifier un objet passé en paramètre', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Maîtriser le passage de paramètre par valeur/');
INSERT INTO chapter (id, name, parent_path) VALUES(641, 'Savoir modifier une date', '/OCA Exam Objectives/Les API de base de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(675, 'Savoir parser une date', '/OCA Exam Objectives/Les API de base de Java/Savoir formater une date/');
INSERT INTO chapter (id, name, parent_path) VALUES(572, 'Savoir quand ne pas utiliser de mot clé', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer des modificateurs d''accès/');
INSERT INTO chapter (id, name, parent_path) VALUES(570, 'Savoir quand utiliser le mot clé private', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer des modificateurs d''accès/');
INSERT INTO chapter (id, name, parent_path) VALUES(571, 'Savoir quand utiliser le mot clé protected', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer des modificateurs d''accès/');
INSERT INTO chapter (id, name, parent_path) VALUES(569, 'Savoir quand utiliser le mot clé public', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer des modificateurs d''accès/');
INSERT INTO chapter (id, name, parent_path) VALUES(574, 'Savoir reconnaître et écrire un JavaBeans', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer les principes de l''encapsulation dans une classe/');
INSERT INTO chapter (id, name, parent_path) VALUES(575, 'Savoir reconnaître et écrire une classe immuable', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer les principes de l''encapsulation dans une classe/');
INSERT INTO chapter (id, name, parent_path) VALUES(697, 'Savoir réécrire une méthode', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(701, 'Savoir surcharger une méthode', '/OCA Exam Objectives/L''héritage en Java/Héritage et polymorphisme/');
INSERT INTO chapter (id, name, parent_path) VALUES(683, 'Savoir utiliser final pour créer une constante locale (à supprimer)', '/OCA Exam Objectives/Les structures de contrôle/Utiliser une structure switch/');
INSERT INTO chapter (id, name, parent_path) VALUES(678, 'Savoir utiliser un arraylist', '/OCA Exam Objectives/Les API de base de Java/Maîtriser les ArrayLists/');
INSERT INTO chapter (id, name, parent_path) VALUES(631, 'Savoir utiliser un tableau à multiple dimension', '/OCA Exam Objectives/Les Arrays en Java/Savoir créer un tableau à multiples dimensions/');
INSERT INTO chapter (id, name, parent_path) VALUES(628, 'Savoir utiliser un tableau à une dimension', '/OCA Exam Objectives/Les Arrays en Java/Savoir créer un tableau à une dimension/');
INSERT INTO chapter (id, name, parent_path) VALUES(379, 'Sérialiser et désérialiser un objet avec les classes ObjectOutputStream et ObjectInputStream', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Ecrire et lire les données entre la mémoire et le disque avec la Sérialisation/');
INSERT INTO chapter (id, name, parent_path) VALUES(208, 'Sets', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser les objets de Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(835, 'Simuler un événement avec v-on', '/Affichage de données et directives/Les directives/');
INSERT INTO chapter (id, name, parent_path) VALUES(838, 'Single Page : Vue Router', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(791, 'soft', '/Modifier un projet/Annuler mes modifications validées/git reset/');
INSERT INTO chapter (id, name, parent_path) VALUES(147, 'Spring Framework Basics', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(765, 'Staging area', '/Les zones de travail/');
INSERT INTO chapter (id, name, parent_path) VALUES(454, 'Starvation', '/OCP Exam Objectives/Concurrence en Java/Les problèmes de concurrence potentiels/');
INSERT INTO chapter (id, name, parent_path) VALUES(766, 'Stash', '/Les zones de travail/');
INSERT INTO chapter (id, name, parent_path) VALUES(93, 'STATE THE OUPUT OF CODE INVOLVING METHODS', '/OCA EXAM ESSENTIALS/CHAPITRE 4 : METHODS AND ENCAPSULATION/');
INSERT INTO chapter (id, name, parent_path) VALUES(464, 'Statement', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/');
INSERT INTO chapter (id, name, parent_path) VALUES(637, 'Stocker les données et les manipuler avec des String et des StringBuilder', '/OCA Exam Objectives/Les API de base de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(494, 'Structure d''une classe Java', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(729, 'super', '/OCA Exam Objectives/L''héritage en Java/Utiliser super et this pour accéder aux champs et constructeurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(731, 'super()', '/OCA Exam Objectives/L''héritage en Java/Utiliser super et this pour accéder aux champs et constructeurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(217, 'Supplier', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/Utiliser les interfaces intégrées dans le package java.util.fonction/');
INSERT INTO chapter (id, name, parent_path) VALUES(237, 'Suppression conditionnelle avec removeIf()', '/OCP Exam Objectives/Généricité et Collections/Manipuler une collection avec les expressions Lambda/');
INSERT INTO chapter (id, name, parent_path) VALUES(278, 'Supprimer les doublons avec la méthode distinct()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations intermédiaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(346, 'Supprimer un fichier avec les méthodes delete() et deleteIfExists()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(809, 'Supprimer un tag - git tag -d', '/Modifier un projet/Identifier une version du projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(798, 'Supprimer une branche', '/Modifier un projet/Travailler avec les branches/');
INSERT INTO chapter (id, name, parent_path) VALUES(799, 'Supprimer une branche mergée', '/Modifier un projet/Travailler avec les branches/Supprimer une branche/');
INSERT INTO chapter (id, name, parent_path) VALUES(800, 'Supprimer une branche non mergée', '/Modifier un projet/Travailler avec les branches/Supprimer une branche/');
INSERT INTO chapter (id, name, parent_path) VALUES(801, 'Supprimer une branche sur le repository central', '/Modifier un projet/Travailler avec les branches/Supprimer une branche/');
INSERT INTO chapter (id, name, parent_path) VALUES(561, 'Surcharge de constructeur', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Créer les méthodes/');
INSERT INTO chapter (id, name, parent_path) VALUES(414, 'Synchroniser les accès aux données', '/OCP Exam Objectives/Concurrence en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(475, 'Test', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(361, 'Tester l''acceccibilité d''un fichier avec la méthode isReadable() et isExecutable()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/');
INSERT INTO chapter (id, name, parent_path) VALUES(604, 'Tester l''égalité ', '/OCA Exam Objectives/Utiliser les opérateurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(271, 'Tester l''invariance avec les assertions', '/OCP Exam Objectives/Exceptions et Assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(730, 'this', '/OCA Exam Objectives/L''héritage en Java/Utiliser super et this pour accéder aux champs et constructeurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(732, 'this()', '/OCA Exam Objectives/L''héritage en Java/Utiliser super et this pour accéder aux champs et constructeurs/');
INSERT INTO chapter (id, name, parent_path) VALUES(495, 'Topologie des variables', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(337, 'Trasforme un chemin relatif vers un chemin absolu avec normalize() ', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(778, 'Travailler avec les branches', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(283, 'Trier les éléments d''un stream avec la méthode sorted()', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations intermédiaires/');
INSERT INTO chapter (id, name, parent_path) VALUES(229, 'Trier une liste', '/OCP Exam Objectives/Généricité et Collections/Utiliser les interfaces de comparaison/');
INSERT INTO chapter (id, name, parent_path) VALUES(718, 'try-catch', '/OCA Exam Objectives/Les exceptions/Connaître les effets de try-catch et l''impact sur le flux du programme/');
INSERT INTO chapter (id, name, parent_path) VALUES(468, 'Type de ResultSet', '/OCP Exam Objectives/Construire des applications de BDD avec JDBC/Les interfaces JDBC, leur utilités et relations/ResultSet/');
INSERT INTO chapter (id, name, parent_path) VALUES(558, 'Type de retour', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Créer les méthodes/');
INSERT INTO chapter (id, name, parent_path) VALUES(497, 'Types références', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(226, 'UnaryOperator et BinaryOperator', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/Utiliser les interfaces intégrées dans le package java.util.fonction/');
INSERT INTO chapter (id, name, parent_path) VALUES(715, 'Unchecked exceptions', '/OCA Exam Objectives/Les exceptions/Connaître les différents types d''exceptions/');
INSERT INTO chapter (id, name, parent_path) VALUES(79, 'UNDERSTAND "IF" AND "SWITCH" DECISION CONTROL STATEMENTS', '/OCA EXAM ESSENTIALS/CHAPITRE 2 : OPERATORS AND STATEMENTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(81, 'UNDERSTAND HOW "BREAK" AND "CONTINUE" CAN CHANGE FLOW CONTROL', '/OCA EXAM ESSENTIALS/CHAPITRE 2 : OPERATORS AND STATEMENTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(77, 'UNDERSTAND JAVA OPERATOR PRECEDENCE', '/OCA EXAM ESSENTIALS/CHAPITRE 2 : OPERATORS AND STATEMENTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(80, 'UNDERSTAND LOOP STATEMENTS', '/OCA EXAM ESSENTIALS/CHAPITRE 2 : OPERATORS AND STATEMENTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(105, 'UNDERSTAND POLYMORPHISM', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(85, 'UNDERSTAND THE DIFFERENCE BETWEEN "==" AND "EQUALS"', '/OCA EXAM ESSENTIALS/CHAPITRE 3: CORE JAVA APIs/');
INSERT INTO chapter (id, name, parent_path) VALUES(67, 'UNDERSTAND THE EFFECT OF USING PACKAGES AND IMPORTS', '/OCA EXAM ESSENTIALS/');
INSERT INTO chapter (id, name, parent_path) VALUES(109, 'UNDERSTAND THE FLOW OF A "TRY" STATEMENT', '/OCA EXAM ESSENTIALS/CHAPITRE 6 : EXCEPTIONS/');
INSERT INTO chapter (id, name, parent_path) VALUES(100, 'UNDERSTAND THE RULES FOR HIDING METHODS AND VARIABLES', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(99, 'UNDERSTAND THE RULES FOR METHOD OVERRIDING', '/OCA EXAM ESSENTIALS/CHAPITRE 5 : CLASS DESIGN/');
INSERT INTO chapter (id, name, parent_path) VALUES(122, 'UNDERSTANDING DEFAULT INITIALIZATION OF VARIABLES', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(117, 'UNDERSTANDING PACKAGE DECLARATIONS AND IMPORTS', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(123, 'UNDERSTANDING VARIABLE SCOPE', '/OCA/CH 1 Java Building Blocks/');
INSERT INTO chapter (id, name, parent_path) VALUES(31, 'Unit Testing', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(436, 'Utilisation', '/OCP Exam Objectives/Concurrence en Java/Les streams parallèles/Exécuter des tâches en parallèle/');
INSERT INTO chapter (id, name, parent_path) VALUES(634, 'Utilisation des varargs', '/OCA Exam Objectives/Les Arrays en Java/Maîtriser les Varargs/');
INSERT INTO chapter (id, name, parent_path) VALUES(264, 'Utiliser collect() ', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/Créer une collection à partir d''un stream avec la méthode collect()/');
INSERT INTO chapter (id, name, parent_path) VALUES(266, 'Utiliser collect() avec les Collectors', '/OCP Exam Objectives/API Stream de Java/Utiliser les opérations terminales/Créer une collection à partir d''un stream avec la méthode collect()/');
INSERT INTO chapter (id, name, parent_path) VALUES(211, 'Utiliser des variables dans les Lambdas', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(477, 'Utiliser Fork-Join avec la classe RecursiveAction', '/OCP Exam Objectives/Concurrence en Java/Gestion des processus simultanés/Le framework Fork-Join/');
INSERT INTO chapter (id, name, parent_path) VALUES(478, 'Utiliser Fork-Join avec la classe RecursiveTask', '/OCP Exam Objectives/Concurrence en Java/Gestion des processus simultanés/Le framework Fork-Join/');
INSERT INTO chapter (id, name, parent_path) VALUES(409, 'Utiliser Future pour obtenir les résultats d''un thread', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/');
INSERT INTO chapter (id, name, parent_path) VALUES(284, 'Utiliser l''API Date-Time de Java SE 8', '/OCP Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(257, 'Utiliser l''instruction try-with-resources', '/OCP Exam Objectives/Exceptions et Assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(227, 'Utiliser l''interface java.lang.Comparable', '/OCP Exam Objectives/Généricité et Collections/Utiliser les interfaces de comparaison/');
INSERT INTO chapter (id, name, parent_path) VALUES(228, 'Utiliser l''interface java.lang.Comparator', '/OCP Exam Objectives/Généricité et Collections/Utiliser les interfaces de comparaison/');
INSERT INTO chapter (id, name, parent_path) VALUES(314, 'Utiliser l''interface Path pour faire des opérations sur les chemins de fichiers et de répertoires', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/');
INSERT INTO chapter (id, name, parent_path) VALUES(311, 'Utiliser l''objet File', '/OCP Exam Objectives/Les fondamentaux de Java IO/Conceptualiser le système de fichier avec l''objet File/');
INSERT INTO chapter (id, name, parent_path) VALUES(617, 'Utiliser l''opérateur ==', '/OCA Exam Objectives/Utiliser les opérateurs/Tester l''égalité /');
INSERT INTO chapter (id, name, parent_path) VALUES(180, 'Utiliser l''opérateur instanceOf', '/OCP Exam Objectives/Conception de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(253, 'Utiliser la clause multi-catch', '/OCP Exam Objectives/Exceptions et Assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(618, 'Utiliser la méthode equals()', '/OCA Exam Objectives/Utiliser les opérateurs/Tester l''égalité /');
INSERT INTO chapter (id, name, parent_path) VALUES(476, 'Utiliser la récursivité avec le framework Fork-Join', '/OCP Exam Objectives/Concurrence en Java/Gestion des processus simultanés/Le framework Fork-Join/');
INSERT INTO chapter (id, name, parent_path) VALUES(277, 'Utiliser les assertions', '/OCP Exam Objectives/Exceptions et Assertions/Tester l''invariance avec les assertions/');
INSERT INTO chapter (id, name, parent_path) VALUES(648, 'Utiliser les boucles', '/OCA Exam Objectives/Les structures de contrôle/');
INSERT INTO chapter (id, name, parent_path) VALUES(357, 'Utiliser les classes BufferedInputStream et BufferedOutputStream', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Les Classes de Stream/');
INSERT INTO chapter (id, name, parent_path) VALUES(354, 'Utiliser les classes FileInputStream et FileOutputStream', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Les Classes de Stream/');
INSERT INTO chapter (id, name, parent_path) VALUES(366, 'Utiliser les classes FileReader et FileWriter et les classes de Buffer associées', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Les Classes de Stream/');
INSERT INTO chapter (id, name, parent_path) VALUES(294, 'Utiliser les Durations', '/OCP Exam Objectives/Utiliser l''API Date-Time de Java SE 8/');
INSERT INTO chapter (id, name, parent_path) VALUES(295, 'Utiliser les Instants', '/OCP Exam Objectives/Utiliser l''API Date-Time de Java SE 8/');
INSERT INTO chapter (id, name, parent_path) VALUES(225, 'Utiliser les interfaces de comparaison', '/OCP Exam Objectives/Généricité et Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(199, 'Utiliser les interfaces fonctionnelles avec les Lambda', '/OCP Exam Objectives/Conception avancée de Classes Java/Créer et utiliser des interfaces fonctionnelles avec des Lambda/');
INSERT INTO chapter (id, name, parent_path) VALUES(215, 'Utiliser les interfaces intégrées dans le package java.util.fonction', '/OCP Exam Objectives/Interfaces fonctionnelles intégrées à Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(391, 'Utiliser les méthodes format() et print()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Formatter des représentations d''objets avec les classes PrintStream et PrintWriter/');
INSERT INTO chapter (id, name, parent_path) VALUES(387, 'Utiliser les méthodes print() et println()', '/OCP Exam Objectives/Les fondamentaux de Java IO/Lire et écrire des données/Formatter des représentations d''objets avec les classes PrintStream et PrintWriter/');
INSERT INTO chapter (id, name, parent_path) VALUES(176, 'Utiliser les modificateurs de visibilité', '/OCP Exam Objectives/Conception de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(746, 'Utiliser les mots-clé throw et throws', '/OCA Exam Objectives/Les exceptions/Connaître les effets de try-catch et l''impact sur le flux du programme/');
INSERT INTO chapter (id, name, parent_path) VALUES(502, 'Utiliser les opérateurs', '/OCA Exam Objectives/');
INSERT INTO chapter (id, name, parent_path) VALUES(250, 'Utiliser les opérations intermédiaires', '/OCP Exam Objectives/API Stream de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(247, 'Utiliser les opérations sources', '/OCP Exam Objectives/API Stream de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(248, 'Utiliser les opérations terminales', '/OCP Exam Objectives/API Stream de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(616, 'utiliser les parenthèses pour réécrire l''ordre d''execution des opérations', '/OCA Exam Objectives/Utiliser les opérateurs/Maîtriser l''ordre des opérations/');
INSERT INTO chapter (id, name, parent_path) VALUES(293, 'Utiliser les Period', '/OCP Exam Objectives/Utiliser l''API Date-Time de Java SE 8/');
INSERT INTO chapter (id, name, parent_path) VALUES(236, 'Utiliser les références de méthodes', '/OCP Exam Objectives/Généricité et Collections/');
INSERT INTO chapter (id, name, parent_path) VALUES(655, 'Utiliser les structures de contrôles avancées', '/OCA Exam Objectives/Les structures de contrôle/');
INSERT INTO chapter (id, name, parent_path) VALUES(194, 'Utiliser les types énumérés avec méthodes et constructeurs', '/OCP Exam Objectives/Conception avancée de Classes Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(728, 'Utiliser super et this pour accéder aux champs et constructeurs', '/OCA Exam Objectives/L''héritage en Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(564, 'Utiliser un champ statique', '/OCA Exam Objectives/Les méthodes et l''encapsulation/Appliquer le mot clé static /');
INSERT INTO chapter (id, name, parent_path) VALUES(404, 'Utiliser un thread Executor simple', '/OCP Exam Objectives/Concurrence en Java/Créer des threads de travail pour éxecuter des tâches de manière concurrente/Le ExecutorService/Les threads Executor simples/');
INSERT INTO chapter (id, name, parent_path) VALUES(650, 'Utiliser une structure switch', '/OCA Exam Objectives/Les structures de contrôle/');
INSERT INTO chapter (id, name, parent_path) VALUES(774, 'Valider mes modifications - git commit', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(340, 'Vérifier l''existence d''un chemin avec la méthode exists()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(338, 'Vérifier l''existence d''un fichier avec la méthode toRealPath()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(360, 'Vérifier la visibilité dd''un fichier avec la méthode isHidden()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Comprendre les attributs de fichier (metadata)/');
INSERT INTO chapter (id, name, parent_path) VALUES(331, 'Vérifier le type de chemin avec les méthode isAbsolute() et toAbsolutePath()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(341, 'Vérifier si deux chemins pointent vers le même fichier avec isSameFile()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(319, 'Via la class FileSystem', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/La classe Path/Créer un objet Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(318, 'Via la méthode get()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/La classe Path/Créer un objet Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(329, 'Visionner le chemin racine avec getRoot()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(326, 'Visionner le nom du fichier avec la méthode getFileName()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(324, 'Visionner le path avec les méthodes toString(), getNameCount() et getName()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(327, 'Visionner les chemins parent avec getParent()', '/OCP Exam Objectives/Java I-O de fichiers (NIO.2)/Interactions entre Paths et Files/L''object Path/');
INSERT INTO chapter (id, name, parent_path) VALUES(771, 'Voir mes modifications', '/Modifier un projet/');
INSERT INTO chapter (id, name, parent_path) VALUES(842, 'Vue.JS version 3', '/');
INSERT INTO chapter (id, name, parent_path) VALUES(849, 'VueX en pratique', '/Introduction à VueX/');
INSERT INTO chapter (id, name, parent_path) VALUES(127, 'WILDCARDS', '/OCA/CH 1 Java Building Blocks/UNDERSTANDING PACKAGE DECLARATIONS AND IMPORTS/');
INSERT INTO chapter (id, name, parent_path) VALUES(205, 'Wildcards', '/OCP Exam Objectives/Généricité et Collections/Créer et utiliser une classe générique/');
INSERT INTO chapter (id, name, parent_path) VALUES(758, 'Windows', '/Les bases/Comment installer Git/');
INSERT INTO chapter (id, name, parent_path) VALUES(645, 'Wrapper Classes', '/OCA Exam Objectives/Les API de base de Java/');
INSERT INTO chapter (id, name, parent_path) VALUES(96, 'WRITE SIMPLE LAMBDA EXPRESSIONS', '/OCA EXAM ESSENTIALS/CHAPITRE 4 : METHODS AND ENCAPSULATION/');
INSERT INTO chapter (id, name, parent_path) VALUES(116, 'WRITING A "MAIN()" METHOD', '/OCA/CH 1 Java Building Blocks/');
