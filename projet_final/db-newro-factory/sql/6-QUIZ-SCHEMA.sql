-- ===================================
-- QUIZ MANAGEMENT SCHEMA
-- ===================================
USE `newro-factory-db`;
SELECT 'Creating quiz and quiz_questions tables...' AS message;

-- Création de la table quiz
CREATE TABLE `quiz` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `title` VARCHAR(255) NOT NULL,
  `description` TEXT NOT NULL,
  `creation_date` DATETIME NOT NULL,
  `created_by` VARCHAR(255) NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_quiz_creator` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Création de la table de liaison entre quiz et question
CREATE TABLE `quiz_questions` (
  `quiz_id` BIGINT NOT NULL,
  `question_id` INT NOT NULL,
  PRIMARY KEY (`quiz_id`, `question_id`),
  CONSTRAINT `fk_quiz` FOREIGN KEY (`quiz_id`) REFERENCES `quiz` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_question` FOREIGN KEY (`question_id`) REFERENCES `question` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- INSERTION DES QUIZ DE TEST
-- ===================================

-- Exemple de quiz 1
INSERT INTO `quiz` (
  `title`, 
  `description`, 
  `creation_date`, 
  `created_by`
) VALUES (
  'Quiz de culture générale',
  'Un quiz pour tester vos connaissances générales.',
  NOW(),
  '<EMAIL>'
);

-- Exemple de quiz 2
INSERT INTO `quiz` (
  `title`, 
  `description`, 
  `creation_date`, 
  `created_by`
) VALUES (
  'Quiz de programmation Java',
  'Évaluez vos compétences en Java avec ce quiz technique.',
  NOW(),
  '<EMAIL>'
);

-- Liaison d'exemple dans quiz_questions (à adapter selon vos données dans `question`)
-- Exemple : le quiz 1 est lié aux questions 1 et 2
INSERT INTO `quiz_questions` (`quiz_id`, `question_id`) VALUES (1, 1), (1, 2);

-- Exemple : le quiz 2 est lié aux questions 3 et 4
INSERT INTO `quiz_questions` (`quiz_id`, `question_id`) VALUES (2, 3), (2, 4);
