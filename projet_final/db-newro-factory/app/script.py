import pymysql

conn = pymysql.connect(
    host='db',
    port=3306,
    user='adminnewro',
    password='Qwerty1234',
    database='newro-factory-db',
    charset='utf8mb4',
    cursorclass=pymysql.cursors.DictCursor
)

try:
    with conn.cursor() as cursor:
        # 1. Renommer la colonne parent_path en parent_chapter (si pas déjà fait)
        rename_column_sql = """
            ALTER TABLE chapter 
            CHANGE COLUMN parent_path parent_chapter VARCHAR(500) NULL;
        """
        cursor.execute(rename_column_sql)
        print("✅ Colonne renommée parent_path → parent_chapter")

        # 2. Récupérer tous les chapitres avec un parent_chapter renseigné (non vide)
        cursor.execute("""
            SELECT id, parent_chapter 
            FROM chapter 
            WHERE parent_chapter IS NOT NULL AND parent_chapter != '' AND parent_chapter != '/'
        """)
        chapters = cursor.fetchall()

        # 3. Pour chaque chapitre, chercher l'ID du parent via son name, puis mettre à jour parent_chapter
        for chapter in chapters:
            raw_value = chapter['parent_chapter']
            parent_name = raw_value.strip('/')

            cursor.execute("SELECT id FROM chapter WHERE name = %s", (parent_name,))
            parent = cursor.fetchone()

            if parent:
                print(f"🔁 Mise à jour chapter {chapter['id']} → parent_chapter = {parent['id']} (ancien: '{raw_value}')")
                cursor.execute(
                    "UPDATE chapter SET parent_chapter = %s WHERE id = %s",
                    (str(parent['id']), chapter['id'])
                )
            else:
                print(f"⚠️ Aucun parent trouvé pour chapter {chapter['id']} (name='{parent_name}'), mise à NULL")
                cursor.execute(
                    "UPDATE chapter SET parent_chapter = NULL WHERE id = %s",
                    (chapter['id'],)
                )

    conn.commit()
    print("🎉 Mise à jour complète terminée.")

finally:
    conn.close()
