import pymysql
import time
import sys

def connect_with_retry(max_retries=5):
    for attempt in range(max_retries):
        try:
            print(f"Tentative de connexion {attempt + 1}/{max_retries}...")
            conn = pymysql.connect(
                host='db',
                port=3306,
                user='adminnewro',
                password='Qwerty1234',
                database='newro-factory-db',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print("✅ Connexion réussie!")
            return conn
        except pymysql.Error as e:
            print(f"❌ Erreur de connexion: {e}")
            if attempt < max_retries - 1:
                print("⏳ Nouvelle tentative dans 3 secondes...")
                time.sleep(3)
            else:
                print("💥 Impossible de se connecter après toutes les tentatives")
                sys.exit(1)

conn = connect_with_retry()

try:
    with conn.cursor() as cursor:
        # 1. Vérifier si la colonne parent_path existe
        cursor.execute("SHOW COLUMNS FROM chapter LIKE 'parent_path'")
        if cursor.fetchone():
            # La colonne existe, on peut la renommer
            rename_column_sql = """
                ALTER TABLE chapter
                CHANGE COLUMN parent_path parent_chapter VARCHAR(500) NULL;
            """
            cursor.execute(rename_column_sql)
            print("✅ Colonne renommée parent_path → parent_chapter")
        else:
            print("ℹ️ Colonne parent_path n'existe pas, migration déjà effectuée")

        # 2. Récupérer tous les chapitres avec un parent_chapter renseigné (non vide)
        cursor.execute("""
            SELECT id, parent_chapter 
            FROM chapter 
            WHERE parent_chapter IS NOT NULL AND parent_chapter != '' AND parent_chapter != '/'
        """)
        chapters = cursor.fetchall()

        # 3. Pour chaque chapitre, chercher l'ID du parent via son name, puis mettre à jour parent_chapter
        for chapter in chapters:
            raw_value = chapter['parent_chapter']
            parent_name = raw_value.strip('/')

            cursor.execute("SELECT id FROM chapter WHERE name = %s", (parent_name,))
            parent = cursor.fetchone()

            if parent:
                print(f"🔁 Mise à jour chapter {chapter['id']} → parent_chapter = {parent['id']} (ancien: '{raw_value}')")
                cursor.execute(
                    "UPDATE chapter SET parent_chapter = %s WHERE id = %s",
                    (str(parent['id']), chapter['id'])
                )
            else:
                print(f"⚠️ Aucun parent trouvé pour chapter {chapter['id']} (name='{parent_name}'), mise à NULL")
                cursor.execute(
                    "UPDATE chapter SET parent_chapter = NULL WHERE id = %s",
                    (chapter['id'],)
                )

    conn.commit()
    print("🎉 Mise à jour complète terminée.")

finally:
    conn.close()
