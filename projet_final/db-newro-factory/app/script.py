import pymysql
import time
import sys

def connect_with_retry(max_retries=5):
    for attempt in range(max_retries):
        try:
            print(f"Tentative de connexion {attempt + 1}/{max_retries}...")
            conn = pymysql.connect(
                host='db',
                port=3306,
                user='adminnewro',
                password='Qwerty1234',
                database='newro-factory-db',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print("✅ Connexion réussie!")
            return conn
        except pymysql.Error as e:
            print(f"❌ Erreur de connexion: {e}")
            if attempt < max_retries - 1:
                print("⏳ Nouvelle tentative dans 3 secondes...")
                time.sleep(3)
            else:
                print("💥 Impossible de se connecter après toutes les tentatives")
                sys.exit(1)

conn = connect_with_retry()

try:
    with conn.cursor() as cursor:
        # 1. Vérifier si la colonne parent_path existe
        cursor.execute("SHOW COLUMNS FROM chapter LIKE 'parent_path'")
        has_parent_path = cursor.fetchone() is not None

        cursor.execute("SHOW COLUMNS FROM chapter LIKE 'parent_chapter'")
        has_parent_chapter = cursor.fetchone() is not None

        if has_parent_path and not has_parent_chapter:
            # La colonne parent_path existe, on peut la renommer
            rename_column_sql = """
                ALTER TABLE chapter
                CHANGE COLUMN parent_path parent_chapter VARCHAR(500) NULL;
            """
            cursor.execute(rename_column_sql)
            print("✅ Colonne renommée parent_path → parent_chapter")
        elif has_parent_chapter:
            print("ℹ️ Colonne parent_chapter existe déjà, migration du nom déjà effectuée")
        else:
            print("⚠️ Aucune colonne parent_path ou parent_chapter trouvée")

        # 2. Vérifier si la migration a déjà été effectuée (si on trouve des IDs numériques)
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM chapter
            WHERE parent_chapter REGEXP '^[0-9]+$'
        """)
        already_migrated = cursor.fetchone()['count'] > 0

        if already_migrated:
            print("ℹ️ Migration déjà effectuée (IDs numériques détectés)")
        else:
            # Vérifier si on a des données avec des chemins complexes
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM chapter
                WHERE parent_chapter LIKE '%/%' AND parent_chapter != '/'
            """)
            has_complex_paths = cursor.fetchone()['count'] > 0

            if not has_complex_paths:
                print("⚠️ Aucun chemin complexe détecté, les données originales ont été perdues")
                print("ℹ️ Impossible de faire la migration sans les données parent_path originales")
                print("� Solution : Restaurer la base de données depuis les scripts SQL originaux")
            else:
                print("�🔄 Début de la migration des relations parent-enfant...")

                # Récupérer tous les chapitres avec un parent_chapter renseigné (chemins)
                cursor.execute("""
                    SELECT id, name, parent_chapter
                    FROM chapter
                    WHERE parent_chapter IS NOT NULL
                    AND parent_chapter != ''
                    AND parent_chapter != '/'
                    AND parent_chapter NOT REGEXP '^[0-9]+$'
                """)
                chapters = cursor.fetchall()

                print(f"📊 {len(chapters)} chapitres à migrer")

                # Pour chaque chapitre, chercher l'ID du parent via son name
                for chapter in chapters:
                    raw_value = chapter['parent_chapter']
                    parent_name = raw_value.strip('/')

                    cursor.execute("SELECT id FROM chapter WHERE name = %s", (parent_name,))
                    parent = cursor.fetchone()

                    if parent:
                        print(f"🔁 Mise à jour chapter {chapter['id']} ({chapter['name']}) → parent_chapter = {parent['id']} (ancien: '{raw_value}')")
                        cursor.execute(
                            "UPDATE chapter SET parent_chapter = %s WHERE id = %s",
                            (str(parent['id']), chapter['id'])
                        )
                    else:
                        print(f"⚠️ Aucun parent trouvé pour chapter {chapter['id']} ({chapter['name']}) avec parent_name='{parent_name}', mise à NULL")
                        cursor.execute(
                            "UPDATE chapter SET parent_chapter = NULL WHERE id = %s",
                            (chapter['id'],)
                        )

                # Mettre à NULL les chapitres racines (parent_chapter = '/')
                cursor.execute("UPDATE chapter SET parent_chapter = NULL WHERE parent_chapter = '/'")
                print("🔄 Chapitres racines mis à NULL")

    conn.commit()
    print("🎉 Mise à jour complète terminée.")

finally:
    conn.close()
