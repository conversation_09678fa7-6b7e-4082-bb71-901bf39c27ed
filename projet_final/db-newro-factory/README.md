
# 🗄️ Base de Données Newro Factory

Ce projet contient la base de données MySQL pour l'application Newro Factory avec un **script de migration automatique** pour optimiser les relations hiérarchiques des chapitres.

## 🚀 Démarrage rapide

### ⚡ Script automatique (recommandé)

```bash
# Démarrage en une commande
./scripts/quick-start.sh
```

### ▶️ Démarrage manuel

```bash
# Cloner et naviguer vers le dossier
cd db-newro-factory

# Lancer la base de données avec migration automatique
docker-compose up --build
```

**⚠️ Important :** Lors du premier lancement, le script de migration s'exécute automatiquement pour optimiser la structure des données.

### 🔄 Lancements suivants

```bash
# Lancer en arrière-plan
docker-compose up -d
```

## 🔧 Migration automatique des données

### 📋 Qu'est-ce qui se passe ?

Le script de migration (`app/script.py`) s'exécute automatiquement au démarrage et :

1. **Renomme la colonne** : `parent_path` → `parent_chapter`
2. **Convertit les chemins** en IDs de chapitres parents
3. **Optimise la hiérarchie** : Chaque chapitre pointe vers son parent le plus général



### ✅ Vérification de la migration

La migration est **idempotente** (peut être relancée sans problème). Le script détecte automatiquement si la migration a déjà été effectuée.



## 🗃️ Détails de connexion

- **Host** : `localhost`
- **Port** : `33006` (mappé vers 3306 dans le conteneur)
- **User** : `adminnewro`
- **Password** : `Qwerty1234`
- **Database** : `newro-factory-db`

### � Connexion depuis l'application

```bash
# Depuis l'extérieur de Docker
mysql -h localhost -P 33006 -u adminnewro -pQwerty1234 newro-factory-db

# Depuis un autre conteneur Docker
mysql -h db -P 3306 -u adminnewro -pQwerty1234 newro-factory-db
```
## 🛠️ Commandes utiles

### 🛑 Arrêter les services

```bash
docker-compose down
```

### ♻️ Réinitialiser complètement (supprimer toutes les données)

```bash
docker-compose down -v
```

### 🔍 Vérifier l'état de la migration

**Script automatique (recommandé) :**
```bash
./scripts/check-migration.sh
```

**Commande manuelle :**
```bash
# Vérifier quelques relations parent-enfant
docker exec mysql-newro mysql -u adminnewro -pQwerty1234 newro-factory-db -e "
SELECT c1.id, c1.name, c1.parent_chapter, c2.name as parent_name
FROM chapter c1
LEFT JOIN chapter c2 ON c1.parent_chapter = c2.id
WHERE c1.parent_chapter IS NOT NULL
LIMIT 10;"
```

### 📊 Statistiques de la base

```bash
# Compter les chapitres par parent
docker exec mysql-newro mysql -u adminnewro -pQwerty1234 newro-factory-db -e "
SELECT c2.name as parent_name, COUNT(c1.id) as nb_children
FROM chapter c1
JOIN chapter c2 ON c1.parent_chapter = c2.id
GROUP BY c2.id, c2.name
ORDER BY nb_children DESC
LIMIT 10;"
```

## � Résolution de problèmes

### ❌ Erreur "Connection refused"

**Cause :** MySQL n'est pas encore prêt
**Solution :** Attendre quelques secondes, le script retry automatiquement

### ❌ Migration échoue

**Cause :** Données corrompues ou incomplètes
**Solution :**
```bash
# Réinitialiser complètement
docker-compose down -v
docker-compose up --build
```


## 📝 Notes importantes

1. **Premier démarrage** : Peut prendre 1-2 minutes (initialisation + migration)
2. **Démarrages suivants** : ~30 secondes
3. **Migration** : Automatique et sécurisée
4. **Données** : Persistantes entre les redémarrages
5. **Logs** : Visibles avec `docker-compose up` (sans `-d`)