
## ▶️ Lancer le service

Exécutez la commande suivante à la racine du projet :

```bash
docker-compose up -d
```

Cela va :

- C<PERSON>er un conteneur MySQL (`mysql-newro`)
- Exposer le port `3306`
- Initialiser la base `newro-factory-db` (si elle n'existe pas)
- <PERSON><PERSON><PERSON> l'utilisateur `adminnewro`

## 🗃️ Détails de connexion

- **Host** : `localhost`
- **Port** : `3306`
- **User** : `adminnewro`
- **Password** :
- **Database** : `newro-factory-db`

## 💾 Données persistantes

- Les données sont stockées dans un volume Docker nommé `mysql-data`.
- Les scripts `.sql` dans le dossier `./sql/` seront exécutés une seule fois à la première initialisation.

## 🛑 Arrêter le service

```bash
docker-compose down
```

## ♻️ Réinitialiser (supprimer les données)

```bash
docker-compose down -v
```