version: '3.8'

services:
  db:
    image: mysql:8.0
    container_name: mysql-newro
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: Qwerty1234
      MYSQL_DATABASE: newro-factory-db
      MYSQL_USER: adminnewro
      MYSQL_PASSWORD: Qwerty1234
    ports:
      - "33006:3306"
    volumes:
      - ./sql:/docker-entrypoint-initdb.d
      - mysql-data:/var/lib/mysql

  app:
    build: ./app
    depends_on:
      - db
    environment:
      DB_HOST: db
      DB_PORT: 3306
    command: ["sh", "-c", "./wait-for-it.sh db:3306 -- python3 script.py"]
    volumes:
      - ./app:/app

volumes:
  mysql-data:
