services:
  db:
    image: mysql:8.0
    container_name: mysql-newro
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: Qwerty1234
      MYSQL_DATABASE: newro-factory-db
      MYSQL_USER: adminnewro
      MYSQL_PASSWORD: Qwerty1234
    ports:
      - "3306:3306"
    volumes:
      - ./sql:/docker-entrypoint-initdb.d   # scripts à exécuter une seule fois
      - mysql-data:/var/lib/mysql           # données persistantes

volumes:
  mysql-data:
