# JWT Token Information

## Overview
This document explains how the JWT token is structured and how to access the information it contains from the frontend.

## Token Structure
The JWT token now includes the following claims:
- `sub`: The subject (username/email)
- `roles`: The user's roles
- `tokenType`: The token type (USER or ADMIN)
- `firstname`: The user's first name
- `lastname`: The user's last name
- `password`: The user's password (hashed)
- `iat`: The issued at timestamp
- `exp`: The expiration timestamp

## Accessing Token Information in Frontend
There are two ways to access the token information in the frontend:

### 1. From the Authentication Response
When a user logs in or registers, the server returns a `TokenJwt` object containing:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "email": "<EMAIL>",
  "firstname": "John",
  "lastname": "Doe",
  "role": "ROLE_USER",
  "tokenType": "USER"
}
```

You can directly access these fields from the response:
```typescript
loginService.login(email, password).subscribe(response => {
  const token = response.token;
  const email = response.email;
  const firstname = response.firstname;
  const lastname = response.lastname;
  const role = response.role;
  const tokenType = response.tokenType;
});
```

### 2. Decoding the JWT Token
You can also decode the JWT token to access its claims directly:

```typescript
import { JwtHelperService } from '@auth0/angular-jwt';

const jwtHelper = new JwtHelperService();
const token = localStorage.getItem('token');
const decodedToken = jwtHelper.decodeToken(token);

const username = decodedToken.sub;
const firstname = decodedToken.firstname;
const lastname = decodedToken.lastname;
const password = decodedToken.password;
const roles = decodedToken.roles;
const tokenType = decodedToken.tokenType;
```

## Security Considerations
- The password included in the token is the hashed version, not the plain text password.
- The token should be stored securely in the frontend (e.g., in HttpOnly cookies or in memory).
- The token should be transmitted securely (e.g., over HTTPS).