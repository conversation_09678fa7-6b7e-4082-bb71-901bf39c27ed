<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" 
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
         
  <modelVersion>4.0.0</modelVersion>
  
  <parent>
    <artifactId>newro-factory</artifactId>
    <groupId>fr.oxyl</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <groupId>fr.oxyl.newrofactory</groupId>
  <artifactId>service</artifactId>
  <version>1.0-SNAPSHOT</version>

  <name>service</name>

  <properties>
    <jwt.version>0.12.6</jwt.version>
  </properties>

  <dependencies>

    <!-- modules -->
    <dependency>
      <groupId>fr.oxyl.newrofactory</groupId>
      <artifactId>core</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>fr.oxyl.newrofactory</groupId>
      <artifactId>persistence</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- Depuis le parent -->
     
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>

    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <scope>test</scope>
      </dependency>

      <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt</artifactId>
      <version>${jwt.version}</version>
    </dependency>

  </dependencies>
</project>
