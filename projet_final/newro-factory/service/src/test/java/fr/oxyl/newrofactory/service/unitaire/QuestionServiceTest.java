package fr.oxyl.newrofactory.service.unitaire;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.ArrayList;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.persistence.service.QuestionDaoService;
import fr.oxyl.newrofactory.service.QuestionService;

@ExtendWith(MockitoExtension.class)
class QuestionServiceTest {

    @Mock  QuestionDaoService questionDao;
    @InjectMocks  QuestionService service;

    @Test
    void count() {
        when(questionDao.count()).thenReturn(12L);
        assertEquals(12L, service.count());
    }

    @Test
    void findAll() {
        List<Question> questions = new ArrayList<>();
        questions.add(new Question(1L, "Title 1", "Content 1", null, null));
        questions.add(new Question(2L, "Title 2", "Content 2", null, null));

        when(questionDao.findAll()).thenReturn(questions);

        List<Question> result = service.findAll();

        assertEquals(2, result.size());
        assertEquals("Title 1", result.get(0).getTitre());
        assertEquals("Title 2", result.get(1).getTitre());
    }
}
