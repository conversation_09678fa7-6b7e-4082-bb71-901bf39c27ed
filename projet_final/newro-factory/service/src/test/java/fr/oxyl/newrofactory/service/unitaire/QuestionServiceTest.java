package fr.oxyl.newrofactory.service.unitaire;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import fr.oxyl.newrofactory.persistence.service.QuestionDaoService;
import fr.oxyl.newrofactory.service.QuestionService;

@ExtendWith(MockitoExtension.class)
class QuestionServiceTest {

    @Mock  QuestionDaoService questionDao;
    @InjectMocks  QuestionService service;

    @Test
    void count() {
        when(questionDao.count()).thenReturn(12L);
        assertEquals(12L, service.count());
    }
}
