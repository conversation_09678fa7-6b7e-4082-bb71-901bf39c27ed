package fr.oxyl.newrofactory.service;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.persistence.service.ChapitreDaoService;

@Service
public class ChapitreService {
    
    private final ChapitreDaoService chapitreDao;

    public ChapitreService(ChapitreDaoService chapitreDao) {
        this.chapitreDao = chapitreDao;
    }

    public long compterChapitres() {
        return chapitreDao.count();
    }

}
