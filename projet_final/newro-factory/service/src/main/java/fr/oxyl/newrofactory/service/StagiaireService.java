package fr.oxyl.newrofactory.service;


import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Stagiaire;
import fr.oxyl.newrofactory.persistence.repository.StagiaireRepo;


@Service
public final class StagiaireService {

    private final StagiaireRepo stagiaireDao;

    public StagiaireService(StagiaireRepo stagiaireDao) {
        this.stagiaireDao = stagiaireDao;
    }

    public Page<Stagiaire> findAll(String search, Pageable pageable){
      return stagiaireDao.findAll(search, pageable);
    }

    public Optional<Stagiaire> findById(long id){
        return stagiaireDao.findById(id);
    }

    public void create(Stagiaire stagiaire){
        if (stagiaire.getDateDepart() != null && stagiaire.getDateArrivee() != null &&
            stagiaire.getDateArrivee().isBefore(stagiaire.getDateDepart())) 
        {
            throw new IllegalArgumentException("La date de fin doit être postérieure à la date de début.");
        }
        stagiaireDao.create(stagiaire);
    }

    public void update(Stagiaire stagiaire) {
        if (stagiaire.getDateDepart() != null && stagiaire.getDateArrivee() != null &&
            stagiaire.getDateArrivee().isBefore(stagiaire.getDateDepart())) 
        {
            throw new IllegalArgumentException("La date de fin doit être postérieure à la date de début.");
        }
        stagiaireDao.create(stagiaire);
    }

    public void delete(long id) {
        stagiaireDao.deleteById(id);
    }

    public long countAll()
    {
        return stagiaireDao.getCount();
    }
}
