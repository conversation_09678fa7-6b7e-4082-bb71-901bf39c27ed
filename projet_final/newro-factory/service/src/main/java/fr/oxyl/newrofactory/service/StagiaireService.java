package fr.oxyl.newrofactory.service;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Stagiaire;
import fr.oxyl.newrofactory.persistence.service.StagiaireDaoService;


@Service
public final class StagiaireService {

    private final StagiaireDaoService stagiaireDao;

    public StagiaireService(StagiaireDaoService stagiaireDao) {
        this.stagiaireDao = stagiaireDao;
    }

    public Page<Stagiaire> findAll(String search){
      return stagiaireDao.findAll(search);
    }

    public Optional<Stagiaire> findById(long id){
        return stagiaireDao.findById(id);
    }

    public void create(Stagiaire stagiaire){
        stagiaireDao.create(stagiaire);
    }

    public void update(Stagiaire stagiaire) {
        stagiaireDao.create(stagiaire);
    }

    public void delete(long id) {
        stagiaireDao.deleteById(id);
    }
    
}
