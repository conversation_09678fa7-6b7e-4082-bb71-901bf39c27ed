package fr.oxyl.newrofactory.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Quiz;
import fr.oxyl.newrofactory.persistence.service.QuizDaoService;

@Service
public final class QuizService {

    private final QuizDaoService quizDao;

    public QuizService(QuizDaoService quizDao) {
        this.quizDao = quizDao;
    }

    public Optional<Quiz> findById(long id) {
        return quizDao.findById(id);
    }

    public Iterable<Quiz> findAll() {
        return quizDao.findAll();
    }

    public Iterable<Quiz> findByCreatedBy(String createdBy) {
        return quizDao.findByCreatedBy(createdBy);
    }

    public Quiz create(Quiz quiz) {
        // Set creation date to now if not already set
        if (quiz.getCreationDate() == null) {
            quiz.setCreationDate(LocalDateTime.now());
        }
        return quizDao.save(quiz);
    }

    public Quiz update(Quiz quiz) {
        // Ensure the quiz exists
        if (quiz.getId() > 0 && quizDao.findById(quiz.getId()).isPresent()) {
            return quizDao.save(quiz);
        }
        throw new IllegalArgumentException("Quiz not found with id: " + quiz.getId());
    }

    public void delete(long id) {
        quizDao.deleteById(id);
    }
}