package fr.oxyl.newrofactory.service;

import fr.oxyl.newrofactory.persistence.internal.entity.UserEntity;
import fr.oxyl.newrofactory.persistence.repository.UtilisateurRepo;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public final class UserService {

    private final UtilisateurRepo userDao;

    public UserService(UtilisateurRepo userDao) {
        this.userDao = userDao;
    }

    /**
     * Find all users in the system
     * @return Iterable of UserDetails objects
     */
    public Iterable<UserDetails> findAll() {
        Iterable<UserEntity> users = userDao.findAll();
        List<UserDetails> userDetailsList = new ArrayList<>();

        for (UserEntity user : users) {
            userDetailsList.add(user);
        }

        return userDetailsList;
    }

    /**
     * Find a user by ID
     * @param id The user ID (username/email)
     * @return Optional containing the user if found
     */
    public Optional<? extends UserDetails> findById(String id) {
        return userDao.findById(id);
    }

    /**
     * Create a new user
     * @param user The user to create
     * @return The created user
     */
    public UserDetails create(UserDetails user) {
        if (user instanceof UserEntity) {
            return userDao.save((UserEntity) user);
        } else {
            throw new IllegalArgumentException("User must be an instance of UserEntity");
        }
    }

    /**
     * Update an existing user
     * @param user The user to update
     * @return The updated user
     */
    public UserDetails update(UserDetails user) {
        if (user instanceof UserEntity) {
            return userDao.save((UserEntity) user);
        } else {
            throw new IllegalArgumentException("User must be an instance of UserEntity");
        }
    }

    /**
     * Delete a user by ID
     * @param id The user ID (username/email)
     */
    public void delete(String id) {
        userDao.deleteById(id);
    }

    /**
     * Count all users in the system
     * @return The number of users
     */
    public long countAll() {
        return userDao.getCount();
    }
}
