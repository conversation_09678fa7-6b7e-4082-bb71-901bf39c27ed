package fr.oxyl.newrofactory.service;

import java.util.Optional;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.persistence.service.QuestionDaoService;

@Service
public final class QuestionService {

    private final QuestionDaoService questionDao;

    public QuestionService(QuestionDaoService questionDao) {
        this.questionDao = questionDao;
    }

    public Optional<Question> findById(long id) {
        return questionDao.findById(id);
    }

    public long count() {
        return questionDao.count();
    }

    public void deleteById(long id) {
        questionDao.deleteById(id);
    }
}
