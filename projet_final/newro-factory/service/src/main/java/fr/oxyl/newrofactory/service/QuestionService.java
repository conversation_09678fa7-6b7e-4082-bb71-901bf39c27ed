package fr.oxyl.newrofactory.service;

import java.util.Optional;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.persistence.service.QuestionDaoService;

@Service
public final class QuestionService {

    private final QuestionDaoService questionDao;

    public QuestionService(QuestionDaoService questionDao) {
        this.questionDao = questionDao;
    }

    public Optional<Question> findById(long id) {
        return questionDao.findById(id);
    }

    public long count() {
        return questionDao.count();
    }

    public void deleteById(long id) {
        questionDao.deleteById(id);
    }

    public List<Question> findAll() {
        return questionDao.findAll();
    }

    public List<Question> findByChapitreId(long chapitreId) {
        // Filter questions by chapter ID
        return findAll().stream()
                .filter(question -> question.getChapitre() != null && question.getChapitre().getId() == chapitreId)
                .collect(Collectors.toList());
    }
}
