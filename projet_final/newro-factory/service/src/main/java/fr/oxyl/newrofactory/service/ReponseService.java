package fr.oxyl.newrofactory.service;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Reponse;
import fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity;
import fr.oxyl.newrofactory.persistence.service.ResponseDaoService;

@Service
public class ReponseService {

    private final ResponseDaoService responseDaoService;

    public ReponseService(ResponseDaoService responseDaoService) {
        this.responseDaoService = responseDaoService;
    }

    public long count() {
        return responseDaoService.count();
    }

    public Iterable<ResponseEntity> findAll() {
        return responseDaoService.findAll();
    }

    public Optional<ResponseEntity> findById(long id) {
        return responseDaoService.findById(id);
    }

    public ResponseEntity save(ResponseEntity responseEntity) {
        return responseDaoService.save(responseEntity);
    }

    public void deleteById(long id) {
        responseDaoService.deleteById(id);
    }

    /**
     * Find all responses for a specific question
     * @param questionId The ID of the question
     * @return List of responses for the question
     */
    public List<ResponseEntity> findByQuestionId(long questionId) {
        return responseDaoService.findByQuestionId(questionId);
    }
}
