package fr.oxyl.newrofactory.service;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.crypto.SecretKey;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.persistence.internal.entity.UserEntity;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.JwtParser;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;

@Service
public class JwtService {

    private final long validitySeconds;
    private final SecretKey secretKey;

    public JwtService(
            @Value("${jwt.secretKey}") String secretHash,
            @Value("${jwt.expirationTime}") long validitySeconds) {
        this.validitySeconds = validitySeconds;
        this.secretKey = Keys.hmacShaKeyFor(secretHash.getBytes());
    }

    /**
     * Generate a token for a user based on their role
     * @param user The user details
     * @return A JWT token
     */
    public String generateToken(UserDetails user) {
        // Check if the user has admin role
        boolean isAdmin = user.getAuthorities().stream()
                .anyMatch(auth -> RoleConstants.ROLE_ADMIN.equals(auth.getAuthority()));

        if (isAdmin) {
            return generateAdminToken(user);
        } else {
            return generateUserToken(user);
        }
    }

    /**
     * Generate a token for a regular user
     * @param user The user details
     * @return A JWT token for regular users
     */
    private String generateUserToken(UserDetails user) {
        Instant now = Instant.now();
        Instant exp = now.plusSeconds(validitySeconds);

        // Cast to UserEntity to access firstname and lastname
        UserEntity userEntity = (UserEntity) user;

        return Jwts.builder()
                .subject(user.getUsername())
                .claim("roles",
                        user.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()))
                .claim("tokenType", "USER")
                .claim("firstname", userEntity.getFirstname())
                .claim("lastname", userEntity.getLastname())
                .claim("password", userEntity.getPassword())
                .issuedAt(Date.from(now))
                .expiration(Date.from(exp))
                .signWith(secretKey, Jwts.SIG.HS256)
                .compact();
    }

    /**
     * Generate a token for an admin user
     * @param user The user details
     * @return A JWT token for admin users
     */
    private String generateAdminToken(UserDetails user) {
        Instant now = Instant.now();
        Instant exp = now.plusSeconds(validitySeconds);

        // Cast to UserEntity to access firstname and lastname
        UserEntity userEntity = (UserEntity) user;

        return Jwts.builder()
                .subject(user.getUsername())
                .claim("roles",
                        user.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()))
                .claim("tokenType", "ADMIN")
                .claim("firstname", userEntity.getFirstname())
                .claim("lastname", userEntity.getLastname())
                .claim("password", userEntity.getPassword())
                .issuedAt(Date.from(now))
                .expiration(Date.from(exp))
                .signWith(secretKey, Jwts.SIG.HS256)
                .compact();
    }

    public boolean isValid(String token) {
        try {
            getParser().parseSignedClaims(token);
            return true;
        } catch (JwtException jwtException) {
            return false;
        }

    }

    public Authentication toAuthentication(String token) {
        Claims claims = getParser()
                .parseSignedClaims(token)
                .getPayload();
        String username = claims.getSubject();

        @SuppressWarnings("unchecked")
        var roles = ((List<String>) claims.get("roles"))
                .stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());

        // Extract additional claims
        String firstname = (String) claims.get("firstname");
        String lastname = (String) claims.get("lastname");
        String password = (String) claims.get("password");

        // Create a UserEntity with the extracted claims
        UserEntity userEntity = new UserEntity();
        userEntity.setUsername(username);
        userEntity.setFirstname(firstname != null ? firstname : "");
        userEntity.setLastname(lastname != null ? lastname : "");
        userEntity.setPassword(password != null ? password : "");

        // Set the role from the roles list
        if (!roles.isEmpty()) {
            userEntity.setRole(roles.get(0).getAuthority());
        }

        // Create authentication with the UserEntity as the principal
        return new UsernamePasswordAuthenticationToken(userEntity, null, roles);
    }

    public String extractUsername(String token) {
        try {
            return getParser()
                    .parseSignedClaims(token)
                    .getPayload()
                    .getSubject();
        } catch (JwtException e) {
            return null;
        }
    }

    /**
     * Extract firstname from token
     * @param token JWT token
     * @return firstname or null if not found
     */
    public String extractFirstname(String token) {
        try {
            return (String) getParser()
                    .parseSignedClaims(token)
                    .getPayload()
                    .get("firstname");
        } catch (JwtException e) {
            return null;
        }
    }

    /**
     * Extract lastname from token
     * @param token JWT token
     * @return lastname or null if not found
     */
    public String extractLastname(String token) {
        try {
            return (String) getParser()
                    .parseSignedClaims(token)
                    .getPayload()
                    .get("lastname");
        } catch (JwtException e) {
            return null;
        }
    }

    /**
     * Extract password from token
     * @param token JWT token
     * @return password or null if not found
     */
    public String extractPassword(String token) {
        try {
            return (String) getParser()
                    .parseSignedClaims(token)
                    .getPayload()
                    .get("password");
        } catch (JwtException e) {
            return null;
        }
    }

    private JwtParser getParser() {
        return Jwts.parser().verifyWith(secretKey).build();
    }

}
