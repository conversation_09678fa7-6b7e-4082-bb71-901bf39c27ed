package fr.oxyl.newrofactory.service;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Promotion;
import fr.oxyl.newrofactory.persistence.repository.PromotionRepo;


@Service
public final class PromotionService {
    private final PromotionRepo promotionDao;

    private PromotionService(PromotionRepo promotionDao) {
        this.promotionDao = promotionDao;
    }

    public Page<Promotion> findAll(String search, Pageable pageable){
        return promotionDao.findAll(search, pageable);
    }

    public Optional<Promotion> findById(long id){
        return promotionDao.findById(id);
    }

    public void delete(long id) {
        promotionDao.deleteById(id);
    }

    public void create(Promotion promotion){
        promotionDao.create(promotion);
    }

    public void update(Promotion promotion) {
        promotionDao.create(promotion);
    }
    public long countAll()
    {
        return promotionDao.getCount();
    }

}
