package fr.oxyl.newrofactory.webapp.mapper;

import java.util.Optional;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.webapp.dto.question.QuestionDto;

@Component
public class QuestionMapperDto {

    private final ChapitreMapperDto chapitreMapperDto;
    private final ReponseMapperDto reponseMapperDto;

    public QuestionMapperDto(ChapitreMapperDto chapitreMapperDto,
            ReponseMapperDto reponseMapperDto) {
        this.chapitreMapperDto = chapitreMapperDto;
        this.reponseMapperDto = reponseMapperDto;
    }

    public QuestionDto mapToDto(Question question) {
        return new QuestionDto(
                question.getId(),
                question.getTitre(),
                question.getContenu(),
                chapitreMapperDto.mapToDtoWithoutParent(question.getChapitre()),
                reponseMapperDto.mapToDto(question.getReponses())    
            );
    }

    // TODO : Exception personnalisé
    public QuestionDto mapToDto(Optional<Question> optional) {
        return optional.map(this::mapToDto).orElseThrow();
    }

}
