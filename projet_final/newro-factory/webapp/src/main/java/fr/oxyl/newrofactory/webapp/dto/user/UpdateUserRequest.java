package fr.oxyl.newrofactory.webapp.dto.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;

/**
 * DTO for user profile update
 * Note: Email/username cannot be changed as it's the primary identifier
 */
public record UpdateUserRequest(
        @NotBlank @JsonProperty("email") String email,
        @NotBlank @JsonProperty("firstName") String firstName,
        @NotBlank @JsonProperty("lastName") String lastName,
        @JsonProperty("password") String password) {
}
