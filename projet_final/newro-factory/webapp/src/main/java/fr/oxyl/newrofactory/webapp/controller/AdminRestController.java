package fr.oxyl.newrofactory.webapp.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.persistence.internal.entity.UserEntity;
import fr.oxyl.newrofactory.persistence.service.UtilisateurDaoService;
import fr.oxyl.newrofactory.webapp.dto.admin.UserDto;
import fr.oxyl.newrofactory.webapp.dto.admin.UsersDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * REST controller for admin-only operations
 */
@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
public class AdminRestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminRestController.class);

    private final UtilisateurDaoService utilisateurDaoService;

    public AdminRestController(UtilisateurDaoService utilisateurDaoService) {
        this.utilisateurDaoService = utilisateurDaoService;
    }

    /**
     * Get all users in the system
     * Only accessible to admins
     */
    @GetMapping("/users")
    public UsersDto getAllUsers() {
        LOGGER.debug("Admin requesting all users");

        Iterable<UserEntity> users = utilisateurDaoService.findAll();
        List<UserDto> userDtos = new ArrayList<>();

        for (UserEntity user : users) {
            userDtos.add(new UserDto(
                user.getUsername(), // Email is stored in username field
                user.getFirstname(),
                user.getLastname(),
                user.getRole()
            ));
        }

        return new UsersDto(userDtos);
    }

    /**
     * Change a user's role
     * Only accessible to admins
     */
    @PostMapping("/users/{username}/role")
    public ResponseEntity<String> changeUserRole(
            @PathVariable("username") String username,
            @RequestBody String newRole) {

        LOGGER.debug("Admin changing role for user {} to {}", username, newRole);

        // Validate role
        if (!newRole.equals(RoleConstants.ROLE_USER) && !newRole.equals(RoleConstants.ROLE_ADMIN)) {
            return new ResponseEntity<>("Invalid role: " + newRole, HttpStatus.BAD_REQUEST);
        }

        // Find user
        Optional<? extends UserDetails> userDetailsOpt = utilisateurDaoService.findById(username);
        if (userDetailsOpt.isEmpty()) {
            return new ResponseEntity<>("User not found: " + username, HttpStatus.NOT_FOUND);
        }

        // Update role
        UserEntity user = (UserEntity) userDetailsOpt.get();
        user.setRole(newRole);
        utilisateurDaoService.save(user);

        return new ResponseEntity<>("Role updated successfully", HttpStatus.OK);
    }
}
