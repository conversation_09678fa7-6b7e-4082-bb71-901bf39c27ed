package fr.oxyl.newrofactory.webapp.config;

import java.util.Locale;

import org.springframework.context.MessageSource;
import org.springframework.http.HttpMethod;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.*;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;
import org.springframework.web.servlet.view.InternalResourceViewResolver;
import org.springframework.web.servlet.view.JstlView;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@EnableWebMvc
@Configuration
@ComponentScan(basePackages = { "fr.oxyl.newrofactory.webapp.controller",
        "fr.oxyl.newrofactory.webapp.mapper",
        "fr.oxyl.newrofactory.webapp.config" })
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        registry.addResourceHandler("resources/**").addResourceLocations("/resources/").setCachePeriod(3600);
    }

    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        registry.addInterceptor(localeChangeInterceptor());
    }

    @Bean
    public ViewResolver viewResolver() {
        InternalResourceViewResolver bean = new InternalResourceViewResolver();

        bean.setViewClass(JstlView.class);
        bean.setPrefix("/WEB-INF/views/");
        bean.setSuffix(".jsp");

        return bean;
    }

    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                // Add mapping for root path
                registry.addMapping("/**")
                        .allowedOrigins(
                            "http://localhost:4200",  // Angular default
                            "http://localhost:3000",  // React default
                            "http://localhost:8080",  // Vue default
                            "http://localhost:5173",  // Vite default
                            "http://localhost:5174",  // Alternative Vite port
                            "http://127.0.0.1:5173",  // Vite with IP
                            "http://127.0.0.1:5174",  // Alternative Vite with IP
                            "http://127.0.0.1:4200",  // Angular with IP
                            "http://127.0.0.1:8080"   // Local server with IP
                        )
                        .allowedMethods("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS")
                        .allowedHeaders("*", "content-type", "Content-Type")
                        .exposedHeaders("Authorization", "Content-Type", "Access-Control-Allow-Origin", "Access-Control-Allow-Methods", "Access-Control-Allow-Headers")  // Expose important headers
                        .allowCredentials(true)
                        .maxAge(3600); // 1 hour

                // Add explicit mapping for /qlf context path
                registry.addMapping("/qlf/**")
                        .allowedOrigins(
                            "http://localhost:4200",  // Angular default
                            "http://localhost:3000",  // React default
                            "http://localhost:8080",  // Vue default
                            "http://localhost:5173",  // Vite default
                            "http://localhost:5174",  // Alternative Vite port
                            "http://127.0.0.1:5173",  // Vite with IP
                            "http://127.0.0.1:5174",  // Alternative Vite with IP
                            "http://127.0.0.1:4200",  // Angular with IP
                            "http://127.0.0.1:8080"   // Local server with IP
                        )
                        .allowedMethods("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS")
                        .allowedHeaders("*", "content-type", "Content-Type")
                        .exposedHeaders("Authorization", "Content-Type", "Access-Control-Allow-Origin", "Access-Control-Allow-Methods", "Access-Control-Allow-Headers")  // Expose important headers
                        .allowCredentials(true)
                        .maxAge(3600); // 1 hour

                // Add explicit mapping for authentication endpoints with /qlf context path
                registry.addMapping("/qlf/api/authentification/**")
                        .allowedOrigins(
                            "http://localhost:4200",  // Angular default
                            "http://localhost:3000",  // React default
                            "http://localhost:8080",  // Vue default
                            "http://localhost:5173",  // Vite default
                            "http://localhost:5174",  // Alternative Vite port
                            "http://127.0.0.1:5173",  // Vite with IP
                            "http://127.0.0.1:5174",  // Alternative Vite with IP
                            "http://127.0.0.1:4200",  // Angular with IP
                            "http://127.0.0.1:8080"   // Local server with IP
                        )
                        .allowedMethods("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS")
                        .allowedHeaders("*", "content-type", "Content-Type")
                        .exposedHeaders("Authorization", "Content-Type", "Access-Control-Allow-Origin", "Access-Control-Allow-Methods", "Access-Control-Allow-Headers")
                        .allowCredentials(true)
                        .maxAge(3600); // 1 hour
            }
        };
    }

    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver sessionLocaleResolver = new SessionLocaleResolver();
        sessionLocaleResolver.setDefaultLocale(Locale.FRENCH);
        return sessionLocaleResolver;
    }

    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor localeChangeInterceptor = new LocaleChangeInterceptor();
        localeChangeInterceptor.setParamName("lang");
        return localeChangeInterceptor;
    }

    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasename("lang/messages");
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }

    /**
     * Creates a CorsFilter bean that applies CORS headers to all responses.
     * This provides an additional layer of CORS support beyond the WebMvcConfigurer.
     */
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();

        // Allow all origins listed in corsConfigurer
        config.addAllowedOrigin("http://localhost:4200");
        config.addAllowedOrigin("http://localhost:3000");
        config.addAllowedOrigin("http://localhost:8080");
        config.addAllowedOrigin("http://localhost:5173");
        config.addAllowedOrigin("http://localhost:5174");
        config.addAllowedOrigin("http://127.0.0.1:5173");
        config.addAllowedOrigin("http://127.0.0.1:5174");
        config.addAllowedOrigin("http://127.0.0.1:4200");
        config.addAllowedOrigin("http://127.0.0.1:8080");

        // Allow all methods and headers
        config.addAllowedMethod("*");
        config.addAllowedHeader("*");

        // Explicitly allow content-type header for all requests
        config.addAllowedHeader("content-type");
        config.addAllowedHeader("Content-Type");

        // Explicitly allow OPTIONS method for preflight requests
        config.addAllowedMethod(HttpMethod.OPTIONS.name());

        // Expose Authorization and Content-Type headers
        config.addExposedHeader("Authorization");
        config.addExposedHeader("Content-Type");
        config.addExposedHeader("Access-Control-Allow-Origin");
        config.addExposedHeader("Access-Control-Allow-Methods");
        config.addExposedHeader("Access-Control-Allow-Headers");
        config.addExposedHeader("Access-Control-Max-Age");
        config.addExposedHeader("Access-Control-Allow-Credentials");

        // Allow credentials
        config.setAllowCredentials(true);

        // Set max age for preflight requests
        config.setMaxAge(3600L);

        // Apply this configuration to all paths, including those with context path
        source.registerCorsConfiguration("/**", config);

        // Explicitly register for paths with /qlf context path
        source.registerCorsConfiguration("/qlf/**", config);

        // Explicitly register for authentication endpoints
        source.registerCorsConfiguration("/api/authentification/**", config);
        source.registerCorsConfiguration("/qlf/api/authentification/**", config);

        // Explicitly register for OPTIONS preflight requests to authentication endpoints
        CorsConfiguration preflightConfig = new CorsConfiguration(config);
        preflightConfig.addAllowedMethod(HttpMethod.OPTIONS.name());
        // Explicitly allow content-type header for preflight requests
        preflightConfig.addAllowedHeader("content-type");
        preflightConfig.addAllowedHeader("Content-Type");
        // Ensure all authentication endpoints are covered
        source.registerCorsConfiguration("/qlf/api/authentification/login", preflightConfig);
        source.registerCorsConfiguration("/api/authentification/login", preflightConfig);

        return new CorsFilter(source);
    }

}
