package fr.oxyl.newrofactory.webapp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.service.AuthentificationService;

@EnableWebSecurity
@Configuration
@EnableMethodSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http,
            JwtAuthentificationFilter jwtAuthentificationFilter) throws Exception {

        http.csrf(AbstractHttpConfigurer::disable)
                .cors(cors -> cors.configurationSource(request -> {
                    var corsConfiguration = new org.springframework.web.cors.CorsConfiguration();
                    corsConfiguration.setAllowedOrigins(java.util.List.of(
                        "http://localhost:4200",
                        "http://localhost:3000",
                        "http://localhost:8080",
                        "http://localhost:5173",
                        "http://localhost:5174",
                        "http://127.0.0.1:5173",
                        "http://127.0.0.1:5174",
                        "http://127.0.0.1:4200",
                        "http://127.0.0.1:8080"
                    ));
                    corsConfiguration.setAllowedMethods(java.util.List.of("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"));
                    corsConfiguration.setAllowedHeaders(java.util.List.of("*"));
                    corsConfiguration.setExposedHeaders(java.util.List.of(
                        "Authorization", "Content-Type", "Access-Control-Allow-Origin", 
                        "Access-Control-Allow-Methods", "Access-Control-Allow-Headers"
                    ));
                    corsConfiguration.setAllowCredentials(true);
                    corsConfiguration.setMaxAge(3600L);
                    return corsConfiguration;
                }))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth -> auth
                    // Public endpoints
                    .requestMatchers("/api/authentification/**", "/qlf/api/authentification/**").permitAll()
                    // Allow preflight requests
                    .requestMatchers(HttpMethod.OPTIONS, "/**").permitAll()

                    // Read operations - accessible to both USER and ADMIN
                    .requestMatchers(HttpMethod.GET, "/api/**").hasAnyAuthority(RoleConstants.ROLE_USER, RoleConstants.ROLE_ADMIN)

                    // Write operations - accessible to both USER and ADMIN
                    .requestMatchers(HttpMethod.POST, "/api/**").hasAnyAuthority(RoleConstants.ROLE_USER, RoleConstants.ROLE_ADMIN)
                    .requestMatchers(HttpMethod.PUT, "/api/**").hasAnyAuthority(RoleConstants.ROLE_USER, RoleConstants.ROLE_ADMIN)
                    .requestMatchers(HttpMethod.PATCH, "/api/**").hasAnyAuthority(RoleConstants.ROLE_USER, RoleConstants.ROLE_ADMIN)
                    .requestMatchers(HttpMethod.DELETE, "/api/**").hasAnyAuthority(RoleConstants.ROLE_USER, RoleConstants.ROLE_ADMIN)

                    // Admin-specific endpoints
                    .requestMatchers("/api/admin/**").hasAuthority(RoleConstants.ROLE_ADMIN)

                    // Any other request requires authentication
                    .anyRequest().authenticated())
                .addFilterBefore(jwtAuthentificationFilter, UsernamePasswordAuthenticationFilter.class);

        // Ensure OPTIONS requests are handled properly for CORS
        http.cors(cors -> {});  // This ensures CORS is applied
        return http.build();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationProvider authenticationProvider(AuthentificationService authentificationService) {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider(authentificationService);
        provider.setPasswordEncoder(passwordEncoder());
        return provider;
    }

    @Bean
    public AuthenticationManager authenticationManager(
            AuthentificationService authentificationService) {
        return new ProviderManager(authenticationProvider(authentificationService));
    }

}
