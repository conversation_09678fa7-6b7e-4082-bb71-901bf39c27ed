package fr.oxyl.newrofactory.webapp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
    "fr.oxyl.newrofactory.webapp",
    "fr.oxyl.newrofactory.service",
    "fr.oxyl.newrofactory.persistence"
})
public class NewroFactoryApplication {

    public static void main(String[] args) {
        SpringApplication.run(NewroFactoryApplication.class, args);
    }
}
