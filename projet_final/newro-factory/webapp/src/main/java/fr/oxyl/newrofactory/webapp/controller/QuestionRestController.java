package fr.oxyl.newrofactory.webapp.controller;

import java.util.List;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.service.QuestionService;
import fr.oxyl.newrofactory.webapp.dto.question.AddQuestionDto;
import fr.oxyl.newrofactory.webapp.dto.question.QuestionDto;
import fr.oxyl.newrofactory.webapp.dto.question.QuestionsAffichageDto;
import fr.oxyl.newrofactory.webapp.mapper.QuestionMapperDto;

@RestController
@RequestMapping("/api/questions")
@CrossOrigin(origins = "http://localhost:4200", allowedHeaders = "*", allowCredentials = "true", exposedHeaders = "Authorization")
public class QuestionRestController {

    private final QuestionService questionService;
    private final QuestionMapperDto questionMapperDto;

    public QuestionRestController(QuestionService questionService,
            QuestionMapperDto questionMapperDto) {
        this.questionService = questionService;
        this.questionMapperDto = questionMapperDto;
    }

    @GetMapping("")
    public QuestionsAffichageDto getQuestions(@RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "currentPage", required = false, defaultValue = "0") int currentPage,
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
            @RequestParam(name = "sortField", required = false, defaultValue = "title") String sortField,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "ASC") String sortDirection) 
    {
        Sort.Direction direction = Sort.Direction.fromString(sortDirection);
        Pageable pageable = PageRequest.of(currentPage, pageSize, Sort.by(direction, sortField));
        return new QuestionsAffichageDto(questionService.findAll(search, pageable).map(questionMapperDto::mapToAffichageDto).getContent());
    }

    @GetMapping("/total")
    public long getCountQuestion() {
        return questionService.count();
    }

    @GetMapping("/{questionId}")
    public QuestionDto getQuestion(@PathVariable("questionId") long questionId){
        return questionMapperDto.mapToDto(questionService.findById(questionId));
    }

    @PostMapping("/question")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> creeQuestion(@RequestBody AddQuestionDto addquestionDto) {
        Question question = questionMapperDto.map(addquestionDto);
        questionService.create(question);
        return new ResponseEntity<>("Création effectué avec succès", HttpStatus.OK);
    }

    @PatchMapping("/{stagiaireId}")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> updateQuestion(@RequestBody QuestionDto questionDto) {
        questionService.update(questionMapperDto.map(questionDto));
        return new ResponseEntity<>("Supression effectué avec succès", HttpStatus.OK);
    }

    @GetMapping("/chapter/{chapterId}")
    public List<QuestionDto> getQuestionsByChapter(@RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "currentPage", required = false, defaultValue = "0") int currentPage,
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
            @RequestParam(name = "sortField", required = false, defaultValue = "title") String sortField,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "ASC") String sortDirection,
            @PathVariable("chapterId") long chapterId)
    {
        Sort.Direction direction = Sort.Direction.fromString(sortDirection);
        Pageable pageable = PageRequest.of(currentPage, pageSize, Sort.by(direction, sortField));
        return questionMapperDto.mapToDto(questionService.findByChapitreId(search, pageable, chapterId));
    }

    @DeleteMapping("/{questionId}")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> deleteQuestion(@PathVariable("questionId") long questionId){
        questionService.deleteById(questionId);
        return new ResponseEntity<>("Supression avec succès", HttpStatus.OK);
    }

}
