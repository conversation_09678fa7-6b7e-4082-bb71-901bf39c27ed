package fr.oxyl.newrofactory.webapp.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.CrossOrigin;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.service.QuestionService;
import fr.oxyl.newrofactory.webapp.dto.question.QuestionDto;
import fr.oxyl.newrofactory.webapp.dto.question.QuestionMinimalDto;
import fr.oxyl.newrofactory.webapp.mapper.QuestionMapperDto;
import fr.oxyl.newrofactory.webapp.mapper.QuestionMinimalMapperDto;

@RestController
@RequestMapping("/api/questions")
@CrossOrigin(origins = "http://localhost:4200", allowedHeaders = "*", allowCredentials = "true", exposedHeaders = "Authorization")
public class QuestionRestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(QuestionRestController.class);

    private final QuestionService questionService;
    private final QuestionMapperDto questionMapperDto;
    private final QuestionMinimalMapperDto questionMinimalMapperDto;

    public QuestionRestController(QuestionService questionService,
            QuestionMapperDto questionMapperDto,
            QuestionMinimalMapperDto questionMinimalMapperDto) {
        this.questionService = questionService;
        this.questionMapperDto = questionMapperDto;
        this.questionMinimalMapperDto = questionMinimalMapperDto;
    }

    @GetMapping
    public List<QuestionDto> getAllQuestions() {
        return questionMapperDto.mapToDto(questionService.findAll());
    }

    @GetMapping("/{questionId}")
    public QuestionDto getQuestion(@PathVariable("questionId") long questionId){
        return questionMapperDto.mapToDto(questionService.findById(questionId));
    }

    /**
     * Get all questions for a specific chapter with full details.
     * This endpoint should only be called when the user explicitly requests to view the questions.
     * @param chapterId The ID of the chapter
     * @return List of questions with full details
     */
    @GetMapping("/chapter/{chapterId}")
    public List<QuestionDto> getQuestionsByChapter(@PathVariable("chapterId") long chapterId){
        LOGGER.debug("Getting full questions for chapter with ID: {}", chapterId);
        return questionMapperDto.mapToDto(questionService.findByChapitreId(chapterId));
    }

    /**
     * Get minimal information about questions for a specific chapter.
     * This endpoint is designed to be lightweight and can be used for displaying a list of questions
     * without loading all the details.
     * @param chapterId The ID of the chapter
     * @return List of questions with minimal information
     */
    @GetMapping("/chapter/{chapterId}/minimal")
    public List<QuestionMinimalDto> getMinimalQuestionsByChapter(@PathVariable("chapterId") long chapterId){
        LOGGER.debug("Getting minimal questions for chapter with ID: {}", chapterId);
        return questionMinimalMapperDto.mapToDto(questionService.findByChapitreId(chapterId));
    }

    @DeleteMapping("/{questionId}")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> deleteQuestion(@PathVariable("questionId") long questionId){
        questionService.deleteById(questionId);
        return new ResponseEntity<>("Supression avec succès", HttpStatus.OK);
    }

}
