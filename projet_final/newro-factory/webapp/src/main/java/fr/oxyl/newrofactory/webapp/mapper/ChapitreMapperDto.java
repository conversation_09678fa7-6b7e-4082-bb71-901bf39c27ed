package fr.oxyl.newrofactory.webapp.mapper;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Chapitre;
import fr.oxyl.newrofactory.webapp.dto.chapitre.ChapitreDto;

@Component
public class ChapitreMapperDto {

    public ChapitreDto mapToDtoWithoutParent(Chapitre chapitre){
        return new ChapitreDto(
            chapitre.getId(), 
            chapitre.getNom(),
            null);
    }
    
}
