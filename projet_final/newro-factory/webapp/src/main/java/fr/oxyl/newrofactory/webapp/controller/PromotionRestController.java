package fr.oxyl.newrofactory.webapp.controller;

import java.util.Optional;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.core.model.Promotion;
import fr.oxyl.newrofactory.service.PromotionService;
import fr.oxyl.newrofactory.webapp.dto.promotion.AddPromotionDto;
import fr.oxyl.newrofactory.webapp.dto.promotion.PromotionDto;
import fr.oxyl.newrofactory.webapp.dto.promotion.PromotionsDto;
import fr.oxyl.newrofactory.webapp.mapper.PromotionMapperDto;

@RestController
@RequestMapping("/api/promotions")
@CrossOrigin(origins = "http://localhost:4200", allowedHeaders = "*", allowCredentials = "true", exposedHeaders = "Authorization")
public class PromotionRestController {

    private final PromotionService promotionService;
    private final PromotionMapperDto promotionMapperDto;

    public PromotionRestController(PromotionService promotionService,
            PromotionMapperDto promotionMapperDto) {
        this.promotionService = promotionService;
        this.promotionMapperDto = promotionMapperDto;
    }

    /**
     * Get all chapters
     * @return List of all chapters
     */
    @GetMapping("")
    public PromotionsDto getPromotions(@RequestParam(name = "search", required = false) String search,
                                              @RequestParam(name = "currentPage", required = false, defaultValue = "0") int currentPage,
                                              @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
                                              @RequestParam(name = "sortField", required = false, defaultValue = "name") String sortField,
                                              @RequestParam(name = "sortDirection", required = false, defaultValue = "ASC") String sortDirection)
    {
        Sort.Direction direction = Sort.Direction.fromString(sortDirection);
        Pageable pageable = PageRequest.of(currentPage, pageSize, Sort.by(direction, sortField));
        return new PromotionsDto(promotionService.findAll(search, pageable).map(promotionMapperDto::mapToAffichageDto).getContent());
    }

    /**
     * Get a specific promotion by ID
     * @param promotionId The ID of the promotion to retrieve
     * @return ResponseEntity containing the promotion or an error message
     */
    @GetMapping("/{promotionId}")
    public ResponseEntity<Object> getPromotion(@PathVariable("promotionId") long promotionId) {

        Optional<Promotion> promotionOpt = promotionService.findById(promotionId);

        if (promotionOpt.isPresent()) {
            Promotion promotion = promotionOpt.get();
            PromotionDto promotionDto = promotionMapperDto.mapToDto(promotion);
            return new ResponseEntity<>(promotionDto, HttpStatus.OK);
        } else {
            return new ResponseEntity<>("Promotion not found", HttpStatus.NOT_FOUND);
        }
    }

    /**
     * @param promotionId The ID of the promotion to check
     * @return ResponseEntity containing a success message or an error message
     */
    @DeleteMapping("/{promotionId}")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> deletePromotion(@PathVariable("promotionId") long promotionId) {
        promotionService.delete(promotionId);
        return new ResponseEntity<>("Supression effectué avec succès", HttpStatus.OK);
    }

    @PostMapping("/promotion")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> creePromotion(@RequestBody AddPromotionDto addPromotionDto) {
        Promotion promotion = promotionMapperDto.map(addPromotionDto);
        promotionService.create(promotion);
        return new ResponseEntity<>("Création effectué avec succès", HttpStatus.OK);
    }

    @PatchMapping("/{promotionId}")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> updatePromotion(@RequestBody PromotionDto promotionDto) {
        promotionService.update(promotionMapperDto.map(promotionDto));
        return new ResponseEntity<>("Supression effectué avec succès", HttpStatus.OK);
    }

    @GetMapping("/total")
    public long getCountPromotion() {
        return promotionService.countAll();
    }
}
