package fr.oxyl.newrofactory.webapp.mapper;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.webapp.dto.question.QuestionMinimalDto;

/**
 * Mapper for converting Question objects to QuestionMinimalDto objects.
 * This is used to reduce the amount of data transferred when listing questions.
 */
@Component
public class QuestionMinimalMapperDto {

    /**
     * Maps a Question to a QuestionMinimalDto.
     * @param question The Question to map
     * @return The mapped QuestionMinimalDto
     */
    public QuestionMinimalDto mapToDto(Question question) {
        if (question == null) {
            return null;
        }
        return new QuestionMinimalDto(
            question.getId(),
            question.getTitre()
        );
    }

    /**
     * Maps a list of Questions to a list of QuestionMinimalDtos.
     * @param questions The list of Questions to map
     * @return The mapped list of QuestionMinimalDtos
     */
    public List<QuestionMinimalDto> mapToDto(List<Question> questions) {
        if (questions == null) {
            return null;
        }
        return questions.stream()
            .map(this::mapToDto)
            .collect(Collectors.toList());
    }
}