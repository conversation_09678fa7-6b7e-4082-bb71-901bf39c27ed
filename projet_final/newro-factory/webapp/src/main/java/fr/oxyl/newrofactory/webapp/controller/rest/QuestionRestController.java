package fr.oxyl.newrofactory.webapp.controller.rest;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.service.QuestionService;
import fr.oxyl.newrofactory.webapp.dto.question.QuestionDto;
import fr.oxyl.newrofactory.webapp.mapper.QuestionMapperDto;

@RestController
@RequestMapping("/api/questions")
public class QuestionRestController {

    private final QuestionService questionService;
    private final QuestionMapperDto questionMapperDto;

    public QuestionRestController(QuestionService questionService,
            QuestionMapperDto questionMapperDto) {
        this.questionService = questionService;
        this.questionMapperDto = questionMapperDto;
    }

    @GetMapping("/{questionId}")
    public QuestionDto getQuestion(@PathVariable("questionId") long questionId){
        return questionMapperDto.mapToDto(questionService.findById(questionId));
    }

    @DeleteMapping("/{questionId}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<String> deleteQuestion(@PathVariable("questionId") long questionId){
        questionService.deleteById(questionId);
        return new ResponseEntity<>("Supression avec succès", HttpStatus.OK);
    }

}
