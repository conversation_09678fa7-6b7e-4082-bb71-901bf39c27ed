package fr.oxyl.newrofactory.webapp.mapper;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Quiz;
import fr.oxyl.newrofactory.webapp.dto.quiz.QuizDto;

@Component
public class QuizMapperDto {

    private final QuestionMapperDto questionMapperDto;

    public QuizMapperDto(QuestionMapperDto questionMapperDto) {
        this.questionMapperDto = questionMapperDto;
    }

    public QuizDto mapToDto(Quiz quiz) {
        if (quiz == null) {
            return null;
        }
        
        return new QuizDto(
            quiz.getId(),
            quiz.getTitle(),
            quiz.getDescription(),
            quiz.getCreationDate(),
            quiz.getQuestions().stream()
                .map(questionMapperDto::mapToDto)
                .collect(Collectors.toList()),
            quiz.getCreatedBy()
        );
    }

    public List<QuizDto> mapToDto(Iterable<Quiz> quizzes) {
        return StreamSupport.stream(quizzes.spliterator(), false)
            .map(this::mapToDto)
            .collect(Collectors.toList());
    }

    public QuizDto mapToDto(Optional<Quiz> optional) {
        return optional.map(this::mapToDto).orElseThrow(() -> new IllegalArgumentException("Quiz not found"));
    }

    public Quiz map(QuizDto quizDto) {
        if (quizDto == null) {
            return null;
        }
        
        // For simplicity, we're not mapping questions here
        // This is typically used when creating a new quiz with existing questions
        return new Quiz(
            quizDto.getId(),
            quizDto.getTitle(),
            quizDto.getDescription(),
            quizDto.getCreationDate(),
            List.of(), // Empty list, questions will be handled separately
            quizDto.getCreatedBy()
        );
    }
}