package fr.oxyl.newrofactory.webapp.controller;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.service.ReponseService;
import fr.oxyl.newrofactory.webapp.dto.reponse.ReponseDto;
import fr.oxyl.newrofactory.webapp.dto.reponse.ReponsesDto;
import fr.oxyl.newrofactory.webapp.mapper.ResponseEntityMapperDto;
import fr.oxyl.newrofactory.webapp.mapper.ResponseEntityToDtoMapper;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.service.ReponseService;
import fr.oxyl.newrofactory.webapp.dto.reponse.ReponseDto;
import fr.oxyl.newrofactory.webapp.dto.reponse.ReponsesDto;
import fr.oxyl.newrofactory.webapp.mapper.ResponseEntityMapperDto;
import fr.oxyl.newrofactory.webapp.mapper.ResponseEntityToDtoMapper;

@RestController
@RequestMapping("/api/reponses")
@CrossOrigin(origins = "http://localhost:4200", allowedHeaders = "*", allowCredentials = "true", exposedHeaders = "Authorization")
public class ReponseRestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReponseRestController.class);

    private final ReponseService reponseService;
    private final ResponseEntityToDtoMapper responseEntityToDtoMapper;
    private final ResponseEntityMapperDto reponseMapperDto;

    public ReponseRestController(ReponseService reponseService, ResponseEntityToDtoMapper responseEntityToDtoMapper,
                                 ResponseEntityMapperDto reponseMapperDto) {
        this.reponseService = reponseService;
        this.responseEntityToDtoMapper = responseEntityToDtoMapper;
        this.reponseMapperDto = reponseMapperDto;
    }

    /**
     * Get all stagiaires
     * @return List of all stagiaires
     */
    @GetMapping("")
    public ReponsesDto getResponses(@RequestParam(name = "search", required = false) String search,
                                    @RequestParam(name = "currentPage", required = false, defaultValue = "0") int currentPage,
                                     @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
            @RequestParam(name = "sortField", required = false, defaultValue = "text") String sortField,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "ASC") String sortDirection)
    {
        Sort.Direction direction = Sort.Direction.fromString(sortDirection);
        Pageable pageable = PageRequest.of(currentPage, pageSize, Sort.by(direction, sortField));
        return new ReponsesDto(reponseService.findAll(search, pageable).map(reponseMapperDto::mapToDto).getContent());
    }


    /**
     * Get a specific response by ID
     * @param reponseId The ID of the response to retrieve
     * @return ResponseEntity containing the response or an error message
     */
    @GetMapping("/{reponseId}")
    public ResponseEntity<Object> getReponse(@PathVariable("reponseId") long reponseId) {
        LOGGER.debug("Getting response with ID: {}", reponseId);

        Optional<fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity> reponseOpt = reponseService.findById(reponseId);

        if (reponseOpt.isPresent()) {
            fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity reponseEntity = reponseOpt.get();
            ReponseDto reponseDto = responseEntityToDtoMapper.mapToDto(reponseEntity);
            return new ResponseEntity<>(reponseDto, HttpStatus.OK);
        } else {
            LOGGER.error("Response with ID {} not found", reponseId);
            return new ResponseEntity<>("Response not found", HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("/total")
    public long getCountReponse() {
        return reponseService.countAll();
    }

    /**
     * Get all responses for a specific question
     * @param questionId The ID of the question
     * @return List of all responses for the question
     */
    @GetMapping("/question/{questionId}")
    public ReponsesDto getResponsesByQuestionId(@PathVariable("questionId") long questionId) {
        LOGGER.debug("Getting responses for question with ID: {}", questionId);

        List<fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity> responses = reponseService.findAllByQuestionId(questionId);
        List<ReponseDto> responseDtos = responses.stream()
                .map(responseEntityToDtoMapper::mapToDto)
                .collect(Collectors.toList());

        return new ReponsesDto(responseDtos);
    }

    /**
     * Create a new response
     * @param reponseDto The response data to create
     * @return ResponseEntity containing success message or error
     */
    @PostMapping
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> createReponse(@RequestBody ReponseDto reponseDto) {
        LOGGER.debug("Creating new response");

        // Convert ReponseDto to ResponseEntity
        fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity reponseEntity = new fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity();
        reponseEntity.setLabel(reponseDto.label());
        reponseEntity.setText(reponseDto.contenu());
        reponseEntity.setValidAnswer(reponseDto.isCorrect());

        reponseService.save(reponseEntity);
        return new ResponseEntity<>("Response created successfully", HttpStatus.CREATED);
    }

    /**
     * Update an existing response
     * @param reponseId The ID of the response to update
     * @param reponseDto The updated response data
     * @return ResponseEntity containing success message or error
     */
    @PutMapping("/{reponseId}")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> updateReponse(@PathVariable("reponseId") long reponseId, @RequestBody ReponseDto reponseDto) {
        LOGGER.debug("Updating response with ID: {}", reponseId);

        Optional<fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity> reponseOpt = reponseService.findById(reponseId);

        if (reponseOpt.isPresent()) {
            fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity reponseEntity = reponseOpt.get();
            reponseEntity.setLabel(reponseDto.label());
            reponseEntity.setText(reponseDto.contenu());
            reponseEntity.setValidAnswer(reponseDto.isCorrect());

            reponseService.save(reponseEntity);
            return new ResponseEntity<>("Response updated successfully", HttpStatus.OK);
        } else {
            LOGGER.error("Response with ID {} not found", reponseId);
            return new ResponseEntity<>("Response not found", HttpStatus.NOT_FOUND);
        }
    }

    /**
     * Delete a response
     * @param reponseId The ID of the response to delete
     * @return ResponseEntity containing success message or error
     */
    @DeleteMapping("/{reponseId}")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<String> deleteReponse(@PathVariable("reponseId") long reponseId) {
        LOGGER.debug("Deleting response with ID: {}", reponseId);

        Optional<fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity> reponseOpt = reponseService.findById(reponseId);

        if (reponseOpt.isPresent()) {
            reponseService.deleteById(reponseId);
            return new ResponseEntity<>("Response deleted successfully", HttpStatus.OK);
        } else {
            LOGGER.error("Response with ID {} not found", reponseId);
            return new ResponseEntity<>("Response not found", HttpStatus.NOT_FOUND);
        }
    }
}
