package fr.oxyl.newrofactory.webapp.controller.user;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.service.ReponseService;
import fr.oxyl.newrofactory.webapp.dto.reponse.ReponseDto;
import fr.oxyl.newrofactory.webapp.mapper.ResponseEntityToDtoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/answer")
@CrossOrigin(origins = "http://localhost:4200", allowedHeaders = "*", allowCredentials = "true", exposedHeaders = "Authorization")
public class ReponseUserRestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReponseUserRestController.class);

    private final ReponseService reponseService;
    private final ResponseEntityToDtoMapper responseEntityToDtoMapper;

    public ReponseUserRestController(ReponseService reponseService, ResponseEntityToDtoMapper responseEntityToDtoMapper) {
        this.reponseService = reponseService;
        this.responseEntityToDtoMapper = responseEntityToDtoMapper;
    }

    /**
     * Get all responses for a specific question
     * @param questionId The ID of the question
     * @return List of responses for the question
     */
    @GetMapping("/question/{questionId}")
    @PreAuthorize("hasAuthority('" + RoleConstants.ROLE_USER + "')")
    public List<ReponseDto> getResponsesByQuestionId(@PathVariable("questionId") long questionId) {
        LOGGER.debug("Getting responses for question with ID: {}", questionId);

        Iterable<fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity> allResponses = reponseService.findAll();
        List<fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity> questionResponses = new java.util.ArrayList<>();

        for (fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity response : allResponses) {
            if (response.getQuestionEntity() != null && response.getQuestionEntity().getId() == questionId) {
                questionResponses.add(response);
            }
        }

        return responseEntityToDtoMapper.mapToDto(questionResponses);
    }
}
