package fr.oxyl.newrofactory.webapp.mapper;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Promotion;
import fr.oxyl.newrofactory.core.model.Stagiaire;
import fr.oxyl.newrofactory.webapp.dto.promotion.PromotionDto;
import fr.oxyl.newrofactory.webapp.dto.stagiaire.AddStagiaireDto;
import fr.oxyl.newrofactory.webapp.dto.stagiaire.DeleteStagiairesDto;
import fr.oxyl.newrofactory.webapp.dto.stagiaire.StagiaireDto;
import jakarta.servlet.http.HttpServletRequest;

@Component
public final class StagiaireMapperDto {
    private final PromotionMapperDto promotionMapperDto;

    public StagiaireMapperDto(PromotionMapperDto promotionMapperDto) {
        this.promotionMapperDto = promotionMapperDto;
    }

    public Stagiaire map(AddStagiaireDto addStagiaireDto) {
        return new Stagiaire.Builder()
                .id(null)
                .nom(addStagiaireDto.nom())
                .prenom(addStagiaireDto.prenom())
                .dateArrivee(addStagiaireDto.dateArrivee())
                .dateDepart(addStagiaireDto.dateDepart())
                .promotion(new Promotion(addStagiaireDto.promotionId(), null))
                .build();
    }

    public Stagiaire map(StagiaireDto stagiaireDto) {
        return new Stagiaire.Builder()
                .id(stagiaireDto.id())
                .nom(stagiaireDto.nom())
                .prenom(stagiaireDto.prenom())
                .dateArrivee(stagiaireDto.dateArrivee())
                .dateDepart(stagiaireDto.dateDepart())
                .promotion(new Promotion(stagiaireDto.promotion().id(), stagiaireDto.promotion().nom()))
                .build();
    }

    public StagiaireDto map(HttpServletRequest request){
        return new StagiaireDto(
            request.getParameter("id") != null ? Long.parseLong(request.getParameter("id")) : null, 
            request.getParameter("prenom"), 
            request.getParameter("nom"), 
            LocalDate.parse(request.getParameter("dateArrivee")), 
            LocalDate.parse(request.getParameter("dateDepart")), 
            new PromotionDto(Long.parseLong(request.getParameter("promotionId")), request.getParameter("promotionName")));
    }

    public StagiaireDto mapId(HttpServletRequest request){
        return new StagiaireDto(
            request.getParameter("id") != null ? Long.parseLong(request.getParameter("id")) : null, 
            null, null, null, null, null);
    }

    public DeleteStagiairesDto mapIdForDelete(HttpServletRequest request){
        var selected = request.getParameterValues("selection")[0].split(",");
        return new DeleteStagiairesDto(
            selected != null ? 
                Arrays.stream(selected)
                    .map(Long::parseLong)
                    .map(id -> new StagiaireDto(id, null, null, null, null, null))
                    .toList() :
                List.of()
        );
    }

    public List<Stagiaire> map(DeleteStagiairesDto deleteStagiairesDto) {
        return deleteStagiairesDto.stagiaireDtos().stream()
                .map(stagiaireDto -> new Stagiaire.Builder()
                        .id(stagiaireDto.id())
                        .nom(null)
                        .prenom(null)
                        .dateArrivee(null)
                        .dateDepart(null)
                        .promotion(null)
                        .build())
                .toList();
    }

    public StagiaireDto mapToDto(Stagiaire stagiaire){
        return new StagiaireDto(
            stagiaire.getId(),
            stagiaire.getPrenom(),
            stagiaire.getNom(), 
            stagiaire.getDateArrivee(),
            stagiaire.getDateDepart(),
            promotionMapperDto.mapToDto(stagiaire.getPromotion()));
    }

    public List<StagiaireDto> mapToDto(List<Stagiaire> stagiaires){
        return stagiaires.stream().map(this::mapToDto).toList();
    }

}
