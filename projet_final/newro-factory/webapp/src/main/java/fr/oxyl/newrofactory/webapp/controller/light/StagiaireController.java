package fr.oxyl.newrofactory.webapp.controller.light;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import fr.oxyl.newrofactory.core.model.Stagiaire;
import fr.oxyl.newrofactory.service.PromotionService;
import fr.oxyl.newrofactory.service.StagiaireService;
import fr.oxyl.newrofactory.webapp.dto.stagiaire.AddStagiaireDto;
import fr.oxyl.newrofactory.webapp.dto.stagiaire.StagiaireDto;
import fr.oxyl.newrofactory.webapp.mapper.PromotionMapperDto;
import fr.oxyl.newrofactory.webapp.mapper.StagiaireMapperDto;



@Controller
public class StagiaireController {

    //private final static Logger LOGGER = LoggerFactory.getLogger(StagiaireController.class);

    private final StagiaireService stagiaireService;
    private final StagiaireMapperDto stagiaireMapperDto;
    private final PromotionService promotionService;
    private final PromotionMapperDto promotionMapperDto;


    public StagiaireController(StagiaireService stagiaireService, StagiaireMapperDto stagiaireMapperDto,
            PromotionService promotionService, PromotionMapperDto promotionMapperDto) {
        this.stagiaireService = stagiaireService;
        this.stagiaireMapperDto = stagiaireMapperDto;
        this.promotionService = promotionService;
        this.promotionMapperDto = promotionMapperDto;
    }

    @GetMapping("/dashboard")
    public String getDashboard(Model model, @RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
            @RequestParam(name = "currentPage", required = false, defaultValue = "1") int currentPage,
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
            @RequestParam(name = "totalPages", required = false, defaultValue = "0") int totalPages,
            @RequestParam(name = "totalElements", required = false, defaultValue = "0") int totalElements) {

        Page<Stagiaire> stagiairesPage = stagiaireService.findAll(search);
        model.addAttribute("sortBy", sortBy);
        model.addAttribute("search", search);
        model.addAttribute("currentPage", currentPage);
        model.addAttribute("pageSize", stagiairesPage.getSize());
        model.addAttribute("totalPages", stagiairesPage.getTotalPages());
        model.addAttribute("totalElements", stagiairesPage.getTotalElements());
        model.addAttribute("stagiaires", stagiairesPage.getContent());
        return "dashboard";
    }

    @GetMapping("/stagiaires/add")
    public String getAddStagiaire(Model model) {
        model.addAttribute("promotions", promotionMapperDto.mapToDto(promotionService.findAll()));
        return "addStagiaire";
    }

    @PostMapping("/stagiaire/add")
    public String addStagiaire(Model model, AddStagiaireDto addStagiaireDto) {
        Stagiaire stagiaire = stagiaireMapperDto.map(addStagiaireDto);
        stagiaireService.create(stagiaire);
        return "redirect:/dashboard";
    }

    @GetMapping("/stagiaire/{stagiaireId}")
    public String getMethodName(Model model, @RequestParam(name = "stagiaireId", required = true) long stagiaireId) {
        // TODO : GERER LE CAS NUL
        Stagiaire stagiaire = stagiaireService.findById(stagiaireId).orElse(null);
        model.addAttribute("stagiaire", stagiaireMapperDto.mapToDto(stagiaire));
        model.addAttribute("promotions", promotionMapperDto.mapToDto(promotionService.findAll()));
        return "editStagiaire";
    }

    @PatchMapping("/stagiaire/{stagiaireId}")
    public String updateStagiaire(Model model, StagiaireDto stagiaireDto) {
        stagiaireService.update(stagiaireMapperDto.map(stagiaireDto));
        return "redirect:/dashboard";
    }

    @DeleteMapping("/stagiaire/{stagiaireId}")
    public String deleteStagiaire(Model model, @RequestParam(name = "stagiaireId", required = true) long stagiaireId) {
        stagiaireService.delete(stagiaireId);
        return "redirect:/dashboard";
    }

    // TODO : LE EXCEPTION HANDLER
}
