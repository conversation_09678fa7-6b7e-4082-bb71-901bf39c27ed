package fr.oxyl.newrofactory.webapp.dto.user;

/**
 * DTO for adding a user to favorites
 */
public class FavoriteUserRequest {
    private String favoriteUsername;

    // Default constructor for JSON deserialization
    public FavoriteUserRequest() {
    }

    public FavoriteUserRequest(String favoriteUsername) {
        this.favoriteUsername = favoriteUsername;
    }

    public String getFavoriteUsername() {
        return favoriteUsername;
    }

    public void setFavoriteUsername(String favoriteUsername) {
        this.favoriteUsername = favoriteUsername;
    }
}