package fr.oxyl.newrofactory.webapp.controller.rest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.service.PromotionService;
import fr.oxyl.newrofactory.webapp.dto.promotion.PromotionsDto;
import fr.oxyl.newrofactory.webapp.mapper.PromotionMapperDto;

@RestController
@RequestMapping("/api/promotions")
public class PromotionRestController {

    private final PromotionService promotionService;
    private final PromotionMapperDto promotionMapperDto;

    public PromotionRestController(PromotionService promotionService,
            PromotionMapperDto promotionMapperDto) {
        this.promotionService = promotionService;
        this.promotionMapperDto = promotionMapperDto;
    }

    @GetMapping
    public PromotionsDto getPromotions() {
        return new PromotionsDto(
                promotionMapperDto.mapToDto(
                        promotionService.findAll()));
    }
}