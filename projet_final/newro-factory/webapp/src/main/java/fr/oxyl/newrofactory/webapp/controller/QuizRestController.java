package fr.oxyl.newrofactory.webapp.controller;

import java.security.Principal;
import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.core.model.Quiz;
import fr.oxyl.newrofactory.service.QuizService;
import fr.oxyl.newrofactory.webapp.dto.quiz.QuizDto;
import fr.oxyl.newrofactory.webapp.mapper.QuizMapperDto;

@RestController
@RequestMapping("/api/quizzes")
public class QuizRestController {

    private final QuizService quizService;
    private final QuizMapperDto quizMapperDto;

    public QuizRestController(QuizService quizService, QuizMapperDto quizMapperDto) {
        this.quizService = quizService;
        this.quizMapperDto = quizMapperDto;
    }

    @GetMapping("")
    public List<QuizDto> getAllQuizzes() {
        return quizMapperDto.mapToDto(quizService.findAll());
    }

    @GetMapping("/{quizId}")
    public QuizDto getQuiz(@PathVariable("quizId") long quizId) {
        return quizMapperDto.mapToDto(quizService.findById(quizId));
    }

    @GetMapping("/history")
    public List<QuizDto> getQuizHistory(Principal principal) {
        // Get quizzes created by the current user
        return quizMapperDto.mapToDto(quizService.findByCreatedBy(principal.getName()));
    }

    @PostMapping("")
    public ResponseEntity<QuizDto> createQuiz(@RequestBody QuizDto quizDto, Principal principal) {
        // Set the creator to the current user
        quizDto.setCreatedBy(principal.getName());
        
        // Map to domain model, save, and map back to DTO
        Quiz quiz = quizMapperDto.map(quizDto);
        Quiz savedQuiz = quizService.create(quiz);
        
        return new ResponseEntity<>(quizMapperDto.mapToDto(savedQuiz), HttpStatus.CREATED);
    }

    @PutMapping("/{quizId}")
    public ResponseEntity<QuizDto> updateQuiz(@PathVariable("quizId") long quizId, @RequestBody QuizDto quizDto) {
        // Ensure the ID in the path matches the ID in the body
        if (quizId != quizDto.getId()) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        
        // Map to domain model, update, and map back to DTO
        Quiz quiz = quizMapperDto.map(quizDto);
        Quiz updatedQuiz = quizService.update(quiz);
        
        return new ResponseEntity<>(quizMapperDto.mapToDto(updatedQuiz), HttpStatus.OK);
    }

    @DeleteMapping("/{quizId}")
    public ResponseEntity<String> deleteQuiz(@PathVariable("quizId") long quizId) {
        quizService.delete(quizId);
        return new ResponseEntity<>("Quiz deleted successfully", HttpStatus.OK);
    }
}