package fr.oxyl.newrofactory.webapp.dto.quiz;

import java.util.List;

/**
 * DTO for creating a new quiz with a list of question IDs.
 */
public class CreateQuizDto {
    private String name;
    private String description;
    private long userId;
    private List<Long> questions;
    private Integer maxQuestions; // Optional parameter to limit the number of questions

    public CreateQuizDto() {
    }

    public CreateQuizDto(String name, String description, long userId, List<Long> questions) {
        this.name = name;
        this.description = description;
        this.userId = userId;
        this.questions = questions;
        this.maxQuestions = null; // Default to null (use all questions)
    }

    public CreateQuizDto(String name, String description, long userId, List<Long> questions, Integer maxQuestions) {
        this.name = name;
        this.description = description;
        this.userId = userId;
        this.questions = questions;
        this.maxQuestions = maxQuestions;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public List<Long> getQuestions() {
        return questions;
    }

    public void setQuestions(List<Long> questions) {
        this.questions = questions;
    }

    public Integer getMaxQuestions() {
        return maxQuestions;
    }

    public void setMaxQuestions(Integer maxQuestions) {
        this.maxQuestions = maxQuestions;
    }
}
