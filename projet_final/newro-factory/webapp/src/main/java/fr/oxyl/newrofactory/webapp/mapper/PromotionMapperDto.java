package fr.oxyl.newrofactory.webapp.mapper;
import java.util.List;

import fr.oxyl.newrofactory.webapp.dto.promotion.AddPromotionDto;
import fr.oxyl.newrofactory.webapp.dto.promotion.DeletePromotionsDto;
import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Promotion;
import fr.oxyl.newrofactory.webapp.dto.promotion.PromotionDto;

@Component
public final class PromotionMapperDto {

    public PromotionMapperDto(){
    }

    public PromotionDto mapToDto(Promotion promotion){
        return new PromotionDto(
            promotion.getId(),
            promotion.getNom());
    }

    public List<PromotionDto> mapToDto(List<Promotion> promotions){
        return promotions.stream().map(this::mapToDto).toList();
    }

    public Promotion map(AddPromotionDto addPromotionDto) {
        return new Promotion.Builder()
                .nom(addPromotionDto.nom())
                .build();
    }

    public Promotion map(PromotionDto promotionDto) {
        return new Promotion.Builder()
                .id(promotionDto.id())
                .nom(promotionDto.nom())
                .build();
    }

    public List<Promotion> map(DeletePromotionsDto deletePromotionsDto) {
        return deletePromotionsDto.promotionDtos().stream()
                .map(promotionDto -> new Promotion.Builder()
                        .id(promotionDto.id())
                        .build())
                .toList();
    }

    public PromotionDto mapToAffichageDto(Promotion promotion)
    {
        return new PromotionDto(
                promotion.getId(),
                promotion.getNom());
    }

}
