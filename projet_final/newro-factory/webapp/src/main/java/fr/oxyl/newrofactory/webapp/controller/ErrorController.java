package fr.oxyl.newrofactory.webapp.controller;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;

import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;

/**
 * Contrôleur pour la gestion des pages d'erreur
 */
@Controller
public class ErrorController {

    /**
     * Gestion générale des erreurs
     */
    @RequestMapping("/error")
    public String handleError(HttpServletRequest request, Model model) {
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);
        
        if (status != null) {
            Integer statusCode = Integer.valueOf(status.toString());
            
            switch (statusCode) {
                case 403:
                    return "403";
                case 404:
                    return "404";
                case 500:
                    return "500";
                default:
                    return "error";
            }
        }
        return "error";
    }

    /**
     * Page d'erreur 403 - Accès interdit
     */
    @RequestMapping("/403")
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public String error403() {
        return "403";
    }

    /**
     * Page d'erreur 404 - Page non trouvée
     */
    @RequestMapping("/404")
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public String error404() {
        return "404";
    }

    /**
     * Page d'erreur 500 - Erreur interne du serveur
     */
    @RequestMapping("/500")
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public String error500() {
        return "500";
    }
}
