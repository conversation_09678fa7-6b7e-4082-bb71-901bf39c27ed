package fr.oxyl.newrofactory.webapp.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.persistence.internal.entity.UserEntity;
import fr.oxyl.newrofactory.persistence.service.UtilisateurDaoService;
import fr.oxyl.newrofactory.webapp.dto.admin.UserDto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

/**
 * REST controller for user profile operations
 */
@RestController
@RequestMapping("/api/user")
public class UserRestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserRestController.class);

    private final UtilisateurDaoService utilisateurDaoService;

    public UserRestController(UtilisateurDaoService utilisateurDaoService) {
        this.utilisateurDaoService = utilisateurDaoService;
    }

    /**
     * Response class for profile errors
     */
    private static class ProfileErrorResponse {
        private final String error;

        public ProfileErrorResponse(String error) {
            this.error = error;
        }

        public String getError() {
            return error;
        }
    }

    /**
     * Get the profile of the currently authenticated user
     * This endpoint returns the user's information including email, firstname, lastname, and role
     * It requires authentication and will return 401 if not authenticated
     * 
     * @return ResponseEntity containing UserDto with user information or error response
     */
    @GetMapping("/profile")
    public ResponseEntity<Object> getCurrentUserProfile() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            LOGGER.warn("Attempt to access profile without authentication");
            return new ResponseEntity<>(
                new ProfileErrorResponse("Not authenticated"), 
                HttpStatus.UNAUTHORIZED
            );
        }

        String username = authentication.getName();
        LOGGER.debug("User {} requesting their profile", username);

        return utilisateurDaoService.findById(username)
            .map(userDetails -> {
                UserEntity user = (UserEntity) userDetails;
                UserDto userDto = new UserDto(
                    user.getUsername(),  // Email is stored in username field
                    user.getFirstname(),
                    user.getLastname(),
                    user.getRole()
                );
                return new ResponseEntity<Object>(userDto, HttpStatus.OK);
            })
            .orElseGet(() -> {
                LOGGER.error("User {} found in authentication but not in database", username);
                return new ResponseEntity<>(
                    new ProfileErrorResponse("User not found"), 
                    HttpStatus.NOT_FOUND
                );
            });
    }
}
