package fr.oxyl.newrofactory.webapp.controller;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import fr.oxyl.newrofactory.webapp.dto.stagiaire.StagiairesDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import fr.oxyl.newrofactory.core.constants.RoleConstants;
import fr.oxyl.newrofactory.persistence.internal.entity.UserEntity;
import fr.oxyl.newrofactory.persistence.internal.entity.UserFavoriteEntity;
import fr.oxyl.newrofactory.persistence.repository.UserFavoriteRepo;
import fr.oxyl.newrofactory.persistence.repository.UtilisateurRepo;
import fr.oxyl.newrofactory.webapp.dto.admin.UserDto;
import fr.oxyl.newrofactory.webapp.dto.user.FavoriteUserRequest;
import fr.oxyl.newrofactory.webapp.dto.user.UpdateUserRequest;
import jakarta.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * REST controller for user profile operations
 */
@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "http://localhost:4200", allowedHeaders = "*", allowCredentials = "true", exposedHeaders = "Authorization")
@PreAuthorize("hasAnyAuthority('" + RoleConstants.ROLE_USER + "', '" + RoleConstants.ROLE_ADMIN + "')")
public class UserRestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserRestController.class);

    private final UtilisateurRepo utilisateurDaoService;
    private final UserFavoriteRepo userFavoriteRepo;

    public UserRestController(UtilisateurRepo utilisateurDaoService, UserFavoriteRepo userFavoriteRepo) {
        this.utilisateurDaoService = utilisateurDaoService;
        this.userFavoriteRepo = userFavoriteRepo;
    }

    /**
     * Response class for profile errors
     */
    private static class ProfileErrorResponse {
        private final String error;

        public ProfileErrorResponse(String error) {
            this.error = error;
        }

        public String getError() {
            return error;
        }
    }

    /**
     * Get all users except admins with pagination and filtering
     * @param search Optional search term to filter users
     * @param sortBy Field to sort by (default: "username")
     * @param currentPage Current page number (0-based, default: 0)
     * @param pageSize Number of items per page (default: 10)
     * @return Page of UserDto objects representing non-admin users
     */
    @GetMapping("")
    public Page<UserDto> getUsers(@RequestParam(name = "search", required = false) String search,
                                  @RequestParam(name = "sortBy", required = false, defaultValue = "username") String sortBy,
                                  @RequestParam(name = "currentPage", required = false, defaultValue = "0") int currentPage,
                                  @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize)
    {
        Page<UserEntity> userPage = utilisateurDaoService.findAll(search, currentPage, pageSize, sortBy);

        List<UserDto> userDtos = userPage.getContent().stream()
            .filter(user -> !RoleConstants.ROLE_ADMIN.equals(user.getRole()))
            .map(user -> new UserDto(
                user.getUsername(),
                user.getFirstname(),
                user.getLastname(),
                user.getRole()
            ))
            .collect(Collectors.toList());

        return new PageImpl<>(
            userDtos, 
            userPage.getPageable(), 
            userPage.getTotalElements() - userPage.getContent().stream()
                .filter(user -> RoleConstants.ROLE_ADMIN.equals(user.getRole()))
                .count()
        );
    }


    /**
     * Get the profile of the currently authenticated user
     * This endpoint returns the user's information including email, firstname, lastname, and role
     * It requires authentication and will return 401 if not authenticated
     * 
     * @return ResponseEntity containing UserDto with user information or error response
     */
    @GetMapping("/profile")
    public ResponseEntity<Object> getCurrentUserProfile() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            LOGGER.warn("Attempt to access profile without authentication");
            return new ResponseEntity<>(
                new ProfileErrorResponse("Not authenticated"), 
                HttpStatus.UNAUTHORIZED
            );
        }

        String username = authentication.getName();
        LOGGER.debug("User {} requesting their profile", username);

        return utilisateurDaoService.findById(username)
            .map(userDetails -> {
                UserEntity user = (UserEntity) userDetails;
                UserDto userDto = new UserDto(
                    user.getUsername(),  // Email is stored in username field
                    user.getFirstname(),
                    user.getLastname(),
                    user.getRole()
                );
                return new ResponseEntity<Object>(userDto, HttpStatus.OK);
            })
            .orElseGet(() -> {
                LOGGER.error("User {} found in authentication but not in database", username);
                return new ResponseEntity<>(
                    new ProfileErrorResponse("User not found"), 
                    HttpStatus.NOT_FOUND
                );
            });
    }

    /**
     * Get user data by username
     * This endpoint is protected and only accessible to authenticated users
     * Users can only access their own data
     * 
     * @param username The username (email) of the user to retrieve
     * @return ResponseEntity containing UserDto with user information or error response
     */
    @GetMapping("/{username}")
    @PreAuthorize("authentication.name == #username or hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<Object> getUserByUsername(@PathVariable String username) {
        LOGGER.debug("Request to get user data for username: {}", username);

        return utilisateurDaoService.findById(username)
            .map(userDetails -> {
                UserEntity user = (UserEntity) userDetails;
                UserDto userDto = new UserDto(
                    user.getUsername(),
                    user.getFirstname(),
                    user.getLastname(),
                    user.getRole()
                );
                return new ResponseEntity<Object>(userDto, HttpStatus.OK);
            })
            .orElseGet(() -> {
                LOGGER.error("User {} not found in database", username);
                return new ResponseEntity<>(
                    new ProfileErrorResponse("User not found"), 
                    HttpStatus.NOT_FOUND
                );
            });
    }

    /**
     * Update user data
     * This endpoint is protected and only accessible to authenticated users
     * Users can only update their own data
     * 
     * @param username The username (email) of the user to update
     * @param updateRequest The request containing updated user details
     * @return ResponseEntity containing success message or error response
     */
    @PatchMapping("/{username}")
    @PreAuthorize("authentication.name == #username or hasAuthority('" + RoleConstants.ROLE_ADMIN + "')")
    public ResponseEntity<Object> updateUser(@PathVariable String username, @Valid @RequestBody UpdateUserRequest updateRequest) {
        LOGGER.debug("Request to update user data for username: {}", username);

        return utilisateurDaoService.findById(username)
            .map(userDetails -> {
                UserEntity user = (UserEntity) userDetails;

                // Update user details
                user.setPassword(updateRequest.password() == null ? user.getPassword() : updateRequest.password());
                user.setUsername(updateRequest.email());
                user.setFirstname(updateRequest.firstName());
                user.setLastname(updateRequest.lastName());

                // Save updated user
                utilisateurDaoService.save(user);

                return new ResponseEntity<Object>(
                    new ProfileErrorResponse("User updated successfully"), 
                    HttpStatus.OK
                );
            })
            .orElseGet(() -> {
                LOGGER.error("User {} not found in database", username);
                return new ResponseEntity<>(
                    new ProfileErrorResponse("User not found"), 
                    HttpStatus.NOT_FOUND
                );
            });
    }

    /**
     * Get all favorite users for the current authenticated user.
     * 
     * @return ResponseEntity containing a list of UserDto objects or error response
     */
    @GetMapping("/favorites")
    public ResponseEntity<Object> getFavoriteUsers() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            LOGGER.warn("Attempt to access favorites without authentication");
            return new ResponseEntity<Object>(
                new ProfileErrorResponse("Not authenticated"), 
                HttpStatus.UNAUTHORIZED
            );
        }

        String username = authentication.getName();
        LOGGER.debug("User {} requesting their favorite users", username);

        List<UserFavoriteEntity> favorites = userFavoriteRepo.findByUserId(username);
        List<UserDto> favoriteUsers = favorites.stream()
            .map(favorite -> {
                UserEntity favoriteUser = favorite.getFavoriteUser();
                return new UserDto(
                    favoriteUser.getUsername(),
                    favoriteUser.getFirstname(),
                    favoriteUser.getLastname(),
                    favoriteUser.getRole()
                );
            })
            .collect(Collectors.toList());

        return new ResponseEntity<Object>(favoriteUsers, HttpStatus.OK);
    }

    /**
     * Add a user to favorites.
     * 
     * @param request the request containing the username of the user to add to favorites
     * @return ResponseEntity containing success message or error response
     */
    @PostMapping("/favorites")
    public ResponseEntity<Object> addFavoriteUser(@RequestBody FavoriteUserRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            LOGGER.warn("Attempt to add favorite without authentication");
            return new ResponseEntity<Object>(
                new ProfileErrorResponse("Not authenticated"), 
                HttpStatus.UNAUTHORIZED
            );
        }

        String username = authentication.getName();
        String favoriteUsername = request.getFavoriteUsername();

        // Check if the user to favorite exists
        return utilisateurDaoService.findById(favoriteUsername)
            .map(favoriteUserDetails -> {
                // Check if already favorited
                if (userFavoriteRepo.existsByUserIdAndFavoriteUserId(username, favoriteUsername)) {
                    return new ResponseEntity<Object>(
                        new ProfileErrorResponse("User already in favorites"), 
                        HttpStatus.BAD_REQUEST
                    );
                }

                // Add to favorites
                userFavoriteRepo.addFavorite(username, favoriteUsername);

                return new ResponseEntity<Object>(
                    new ProfileErrorResponse("User added to favorites"), 
                    HttpStatus.CREATED
                );
            })
            .orElseGet(() -> {
                LOGGER.error("User {} not found in database", favoriteUsername);
                return new ResponseEntity<Object>(
                    new ProfileErrorResponse("User to favorite not found"), 
                    HttpStatus.NOT_FOUND
                );
            });
    }

    /**
     * Remove a user from favorites.
     * 
     * @param favoriteUsername the username of the user to remove from favorites
     * @return ResponseEntity containing success message or error response
     */
    @DeleteMapping("/favorites/{favoriteUsername}")
    public ResponseEntity<Object> removeFavoriteUser(@PathVariable String favoriteUsername) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            LOGGER.warn("Attempt to remove favorite without authentication");
            return new ResponseEntity<Object>(
                new ProfileErrorResponse("Not authenticated"), 
                HttpStatus.UNAUTHORIZED
            );
        }

        String username = authentication.getName();
        LOGGER.debug("User {} removing {} from favorites", username, favoriteUsername);

        // Check if the favorite exists
        if (!userFavoriteRepo.existsByUserIdAndFavoriteUserId(username, favoriteUsername)) {
            return new ResponseEntity<Object>(
                new ProfileErrorResponse("User not in favorites"), 
                HttpStatus.NOT_FOUND
            );
        }

        // Remove from favorites
        userFavoriteRepo.removeFavorite(username, favoriteUsername);

        return new ResponseEntity<Object>(
            new ProfileErrorResponse("User removed from favorites"), 
            HttpStatus.OK
        );
    }
}
