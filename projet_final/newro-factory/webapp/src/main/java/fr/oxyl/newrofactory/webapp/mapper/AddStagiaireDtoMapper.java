package fr.oxyl.newrofactory.webapp.mapper;

import java.time.LocalDate;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.webapp.dto.stagiaire.AddStagiaireDto;
import jakarta.servlet.http.HttpServletRequest;

@Component
public final class AddStagiaireDtoMapper {

    private AddStagiaireDtoMapper() {
    }

    public AddStagiaireDto mapToDto(HttpServletRequest request) {
        return new AddStagiaireDto(
            request.getParameter("nom"),
            request.getParameter("prenom"),
            parseDate(request.getParameter("dateArrivee")),
            parseDate(request.getParameter("dateDepart")),
            Long.parseLong(request.getParameter("promotionId"))
        );
    }

            
    private LocalDate parseDate(String dateString) {
        if (dateString == null || dateString.isEmpty()) {
            return null;
        }
        return LocalDate.parse(dateString);
    }
}
