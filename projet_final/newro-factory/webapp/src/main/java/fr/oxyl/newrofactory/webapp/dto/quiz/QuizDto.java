package fr.oxyl.newrofactory.webapp.dto.quiz;

import java.time.LocalDateTime;
import java.util.List;

import fr.oxyl.newrofactory.webapp.dto.question.QuestionDto;

public class QuizDto {
    private long id;
    private String title;
    private String description;
    private LocalDateTime creationDate;
    private List<QuestionDto> questions;
    private String createdBy;

    public QuizDto() {
    }

    public QuizDto(long id, String title, String description, LocalDateTime creationDate, List<QuestionDto> questions, String createdBy) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.creationDate = creationDate;
        this.questions = questions;
        this.createdBy = createdBy;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public List<QuestionDto> getQuestions() {
        return questions;
    }

    public void setQuestions(List<QuestionDto> questions) {
        this.questions = questions;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
}