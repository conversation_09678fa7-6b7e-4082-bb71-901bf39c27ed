package fr.oxyl.newrofactory.webapp.controller.rest;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import fr.oxyl.newrofactory.service.JwtService;
import fr.oxyl.newrofactory.webapp.dto.auth.LoginRequest;
import fr.oxyl.newrofactory.webapp.dto.auth.TokenJwt;
import jakarta.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RestController
@RequestMapping("/api/authentification")
public class AuthentificationRestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthentificationRestController.class);

    private final AuthenticationManager authenticationManager;
    private final JwtService jwtService;

    public AuthentificationRestController(AuthenticationManager authenticationManager,
            JwtService jwtService) {
        this.authenticationManager = authenticationManager;
        this.jwtService = jwtService;
    }

    @PostMapping("/login")
    public TokenJwt authentification(@Valid @RequestBody LoginRequest loginRequest) {
        LOGGER.debug("DEBUG AUTHENTIFICATION : utilisateur essyant de se connecter avec {} : {}".format(loginRequest.username(), loginRequest.password()));
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.username(), loginRequest.password()));
        String jwt = jwtService.generateToken((UserDetails) authentication.getPrincipal());
        return new TokenJwt(jwt);
    }


    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handlerDegu(Exception e){
        return new ResponseEntity<String>(e.getStackTrace().toString(), HttpStatus.PRECONDITION_FAILED);
    }


}
