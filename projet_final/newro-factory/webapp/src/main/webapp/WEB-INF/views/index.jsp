<%@ page contentType="text/html; charset=UTF-8" language="java" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="spring" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>NewRo-Factory</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .api-info {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .endpoint {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><spring:message code="index.title" /></h1>
        
        <div class="api-info">
            <h2>API REST Endpoints</h2>
            <div class="endpoint">
                <strong>Authentication:</strong> /api/authentification/login, /api/authentification/register
            </div>
            <div class="endpoint">
                <strong>Users:</strong> /api/users
            </div>
            <div class="endpoint">
                <strong>Stagiaires:</strong> /api/stagiaires
            </div>
            <div class="endpoint">
                <strong>Promotions:</strong> /api/promotions
            </div>
            <div class="endpoint">
                <strong>Questions:</strong> /api/questions
            </div>
            <div class="endpoint">
                <strong>Quiz:</strong> /api/quiz
            </div>
        </div>
        
        <p>Backend API is running successfully!</p>
    </div>
</body>
</html>
