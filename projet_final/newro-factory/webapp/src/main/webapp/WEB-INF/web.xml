<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee
                             https://jakarta.ee/xml/ns/jakartaee/web-app_6_0.xsd"
         version="6.0">
         <display-name>Gestion Stagiaires</display-name>

         <!-- Context Configuration -->
         <context-param>
            <param-name>contextClass</param-name>
            <param-value>org.springframework.web.context.support.AnnotationConfigWebApplicationContext</param-value>
         </context-param>
         <context-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>fr.oxyl.newrofactory.persistence.config.PersistenceConfig fr.oxyl.newrofactory.service.config.ServiceConfig fr.oxyl.newrofactory.webapp.config.WebConfig fr.oxyl.newrofactory.webapp.config.SecurityConfig</param-value>
         </context-param>

         <!-- Spring Context Loader -->
         <listener>
            <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
         </listener>

         <!-- Spring MVC Dispatcher Servlet -->
         <servlet>
            <servlet-name>dispatcher</servlet-name>
            <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
            <init-param>
               <param-name>contextClass</param-name>
               <param-value>org.springframework.web.context.support.AnnotationConfigWebApplicationContext</param-value>
            </init-param>
            <init-param>
               <param-name>contextConfigLocation</param-name>
               <param-value>fr.oxyl.newrofactory.webapp.config.WebConfig</param-value>
            </init-param>
            <load-on-startup>1</load-on-startup>
         </servlet>
         <servlet-mapping>
            <servlet-name>dispatcher</servlet-name>
            <url-pattern>/</url-pattern>
         </servlet-mapping>

         <!-- Spring Security Filter -->
         <filter>
            <filter-name>springSecurityFilterChain</filter-name>
            <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        </filter>
        <filter-mapping>
            <filter-name>springSecurityFilterChain</filter-name>
            <url-pattern>/*</url-pattern>
        </filter-mapping>
</web-app>
