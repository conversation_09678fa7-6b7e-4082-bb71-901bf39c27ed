<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>newro-factory</artifactId>
    <groupId>fr.oxyl</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <groupId>fr.oxyl.newrofactory</groupId>
  <artifactId>webapp</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>war</packaging>

  <name>webapp</name>

  <properties>
    <jakarta.version>6.1.0</jakarta.version>
    <jakarta.validation.version>3.1.1</jakarta.validation.version>
    <jakarta.jstl.version>3.0.2</jakarta.jstl.version>
    <glassfish.version>3.0.1</glassfish.version>
    <jwt.version>0.12.6</jwt.version>
    <jackson.version>2.19.0</jackson.version>
  </properties>

  <dependencies>

    <!-- modules -->
    <dependency>
      <groupId>fr.oxyl.newrofactory</groupId>
      <artifactId>core</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>fr.oxyl.newrofactory</groupId>
      <artifactId>persistence</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>fr.oxyl.newrofactory</groupId>
      <artifactId>service</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- Depuis le parent -->

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-config</artifactId>
    </dependency>

    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- autres -->

    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>${jakarta.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
      <version>${jakarta.validation.version}</version>
    </dependency>

    <dependency>
      <groupId>jakarta.servlet.jsp.jstl</groupId>
      <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
      <version>${jakarta.jstl.version}</version>
    </dependency>

    <dependency>
      <groupId>org.glassfish.web</groupId>
      <artifactId>jakarta.servlet.jsp.jstl</artifactId>
      <version>${glassfish.version}</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt</artifactId>
      <version>${jwt.version}</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>${jackson.version}</version>
    </dependency>

  </dependencies>

  <build>
    <finalName>qlf</finalName>
  </build>
</project>
