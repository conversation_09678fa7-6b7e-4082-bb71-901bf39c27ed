<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>newro-factory</artifactId>
    <groupId>fr.oxyl</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <groupId>fr.oxyl.newrofactory</groupId>
  <artifactId>persistence</artifactId>
  <version>1.0-SNAPSHOT</version>

  <name>persistence</name>

  <properties>
    <mysql.version>8.0.28</mysql.version>
    <mockito.version>5.2.0</mockito.version>
    <spring.data.version>4.0.0-M3</spring.data.version>
    <hikari.version>5.1.0</hikari.version>
    <hibernate.version>7.0.0.Final</hibernate.version>
    <querydsl.version>5.1.0</querydsl.version>
    <jakarta.persistence.version>3.2.0</jakarta.persistence.version>
  </properties>

  <dependencies>

    <!-- modules -->
    <dependency>
      <groupId>fr.oxyl.newrofactory</groupId>
      <artifactId>core</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- Depuis le parent -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aop</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjrt</artifactId>
    </dependency>

    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>


    <!-- Autres -->
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>${mysql.version}</version>
    </dependency>

    <dependency>
      <groupId>com.zaxxer</groupId>
      <artifactId>HikariCP</artifactId>
      <version>${hikari.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jdbc</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-jpa</artifactId>
      <version>${spring.data.version}</version>
    </dependency>

    <dependency>
      <groupId>org.hibernate.orm</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>${hibernate.version}</version>
    </dependency>

    <dependency>
      <groupId>org.hibernate.orm</groupId>
      <artifactId>hibernate-community-dialects</artifactId>
      <version>${hibernate.version}</version>
    </dependency>

    <dependency>
      <groupId>org.jspecify</groupId>
      <artifactId>jspecify</artifactId>
      <version>${jspecify.version}</version>
    </dependency>

    <dependency>
      <groupId>com.querydsl</groupId>
      <artifactId>querydsl-apt</artifactId>
      <version>${querydsl.version}</version>
      <classifier>jakarta</classifier>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.querydsl</groupId>
      <artifactId>querydsl-jpa</artifactId>
      <classifier>jakarta</classifier>
      <version>${querydsl.version}</version>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.13.0</version>
        <configuration>
          <annotationProcessorPaths>
            <path>
              <groupId>com.querydsl</groupId>
              <artifactId>querydsl-apt</artifactId>
              <version>${querydsl.version}</version>
              <classifier>jakarta</classifier>
            </path>
            <path>
              <groupId>jakarta.persistence</groupId>
              <artifactId>jakarta.persistence-api</artifactId>
              <version>${jakarta.persistence.version}</version> <!-- ou une version compatible avec Hibernate 7 -->
            </path>
            <!-- si vous avez d'autres annotationProcessorPaths, listez-les ici -->
          </annotationProcessorPaths>
          <compilerArgs>
            <arg>-processor</arg>
            <arg>com.querydsl.apt.jpa.JPAAnnotationProcessor</arg>
          </compilerArgs>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>