package fr.oxyl.newrofactory.persistence.internal.entity;

import org.jspecify.annotations.NonNull;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "answer")
public class ResponseEntity {
    
    @Id
    @GeneratedValue
    private Long id;

    @NonNull
    private String label;

    @NonNull
    private String text;

    @NonNull
    @Column(name= "valid_answer")
    private boolean validAnswer;

    //TODO : ajouter question

    public ResponseEntity() {
    }

    public ResponseEntity(Long id, String label, String statement, boolean validAnswer) {
        this.id = id;
        this.label = label;
        this.text = statement;
        this.validAnswer = validAnswer;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public boolean isValidAnswer() {
        return validAnswer;
    }

    public void setValidAnswer(boolean validAnswer) {
        this.validAnswer = validAnswer;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        result = prime * result + ((label == null) ? 0 : label.hashCode());
        result = prime * result + ((text == null) ? 0 : text.hashCode());
        result = prime * result + (validAnswer ? 1231 : 1237);
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ResponseEntity other = (ResponseEntity) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        } else if (!id.equals(other.id))
            return false;
        if (label == null) {
            if (other.label != null)
                return false;
        } else if (!label.equals(other.label))
            return false;
        if (text == null) {
            if (other.text != null)
                return false;
        } else if (!text.equals(other.text))
            return false;
        if (validAnswer != other.validAnswer)
            return false;
        return true;
    }

    @Override
    public String toString() {
        return "ResponseEntity [id=" + id + ", label=" + label + ", statement=" + text + ", valid_answer=" + validAnswer
                + "]";
    }

    
}
