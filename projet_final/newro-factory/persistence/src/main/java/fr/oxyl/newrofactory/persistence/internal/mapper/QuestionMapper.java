package fr.oxyl.newrofactory.persistence.internal.mapper;

import java.util.Optional;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.persistence.internal.entity.QuestionEntity;


@Component
public final class QuestionMapper {

    private final ReponseMapper reponseMapperDao;
    private final ChapitreMapper chapitreMapperDao;

    public QuestionMapper(ReponseMapper reponseMapperDao,
            ChapitreMapper chapitreMapperDao) {
        this.reponseMapperDao = reponseMapperDao;
        this.chapitreMapperDao = chapitreMapperDao;
    }

    public Question map(QuestionEntity questionEntity){
        if (questionEntity == null) {
            return null;
        }

        return new Question(
            questionEntity.getId(),
            questionEntity.getTitle(), 
            questionEntity.getStatement(), 
            chapitreMapperDao.map(questionEntity.getChapitreEntity()), 
            reponseMapperDao.map(questionEntity.getReponsesEntities()));
    }

    public Optional<Question> map(Optional<QuestionEntity> questionEntity) {
        return questionEntity.map(this::map);
    }

}
