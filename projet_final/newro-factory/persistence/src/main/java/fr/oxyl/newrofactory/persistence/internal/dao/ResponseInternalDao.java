package fr.oxyl.newrofactory.persistence.internal.dao;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity;

import java.util.List;
import java.util.Optional;

@Repository
public interface ResponseInternalDao extends PagingAndSortingRepository<ResponseEntity, Long>,
        CrudRepository<ResponseEntity, Long> {

    @Query("""
            SELECT r
            FROM ResponseEntity r
            WHERE :search IS NULL 
                OR r.text LIKE CONCAT('%', :search, '%')
            """)
    Page<ResponseEntity> findAll(@Param("search") String search, Pageable pageable);

    @Override
    Optional<ResponseEntity> findById(Long id);

    @Query("""
            SELECT r
            FROM ResponseEntity r
            WHERE r.questionEntity.id = :questionId
            """)
    List<ResponseEntity> findAllByQuestionId(@Param("questionId") Long questionId);
}
