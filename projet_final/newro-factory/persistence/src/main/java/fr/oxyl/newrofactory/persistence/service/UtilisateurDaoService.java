package fr.oxyl.newrofactory.persistence.service;

import java.util.Optional;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.persistence.internal.dao.UserInternalDao;

@Service
public class UtilisateurDaoService {

    private final UserInternalDao userInternalDao;

    public UtilisateurDaoService(UserInternalDao userInternalDao){
        this.userInternalDao = userInternalDao;
    }

    public Optional<? extends UserDetails> findById(String username){
        return userInternalDao.findById(username);
    }
    
}
