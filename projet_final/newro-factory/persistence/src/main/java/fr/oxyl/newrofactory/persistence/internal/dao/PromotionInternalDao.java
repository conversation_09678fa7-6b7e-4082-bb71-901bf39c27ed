package fr.oxyl.newrofactory.persistence.internal.dao;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import fr.oxyl.newrofactory.persistence.internal.entity.PromotionEntity;

@Repository
public interface PromotionInternalDao extends PagingAndSortingRepository<PromotionEntity, Long>,
        CrudRepository<PromotionEntity, Long> {

    @Query("""
            SELECT p
            FROM PromotionEntity p
            WHERE :search IS NULL 
                OR p.name LIKE CONCAT('%', :search, '%')
            """)
    Page<PromotionEntity> findAll(@Param("search") String search, Pageable pageable);

    Optional<PromotionEntity> findById(long id);
}
