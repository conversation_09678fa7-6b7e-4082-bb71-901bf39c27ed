package fr.oxyl.newrofactory.persistence.internal.entity;

import java.sql.Timestamp;
import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

/**
 * Entity representing a user's favorite users.
 * This is a many-to-many relationship between users.
 */
@Entity
@Table(name = "user_favorite")
@IdClass(UserFavoriteId.class)
public class UserFavoriteEntity {

    @Id
    @Column(name = "user_id")
    private String userId;

    @Id
    @Column(name = "favorite_user_id")
    private String favoriteUserId;

    @Column(name = "created_at", nullable = false, updatable = false, insertable = false)
    private Timestamp createdAt;

    @ManyToOne
    @JoinColumn(name = "user_id", referencedColumnName = "email", insertable = false, updatable = false)
    private UserEntity user;

    @ManyToOne
    @JoinColumn(name = "favorite_user_id", referencedColumnName = "email", insertable = false, updatable = false)
    private UserEntity favoriteUser;

    public UserFavoriteEntity() {
    }

    public UserFavoriteEntity(String userId, String favoriteUserId) {
        this.userId = userId;
        this.favoriteUserId = favoriteUserId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFavoriteUserId() {
        return favoriteUserId;
    }

    public void setFavoriteUserId(String favoriteUserId) {
        this.favoriteUserId = favoriteUserId;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public UserEntity getUser() {
        return user;
    }

    public UserEntity getFavoriteUser() {
        return favoriteUser;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserFavoriteEntity that = (UserFavoriteEntity) o;
        return Objects.equals(userId, that.userId) && 
               Objects.equals(favoriteUserId, that.favoriteUserId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, favoriteUserId);
    }

    @Override
    public String toString() {
        return "UserFavoriteEntity{" +
                "userId='" + userId + '\'' +
                ", favoriteUserId='" + favoriteUserId + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}