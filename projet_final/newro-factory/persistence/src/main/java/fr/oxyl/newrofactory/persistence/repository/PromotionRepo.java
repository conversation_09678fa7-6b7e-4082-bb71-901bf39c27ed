package fr.oxyl.newrofactory.persistence.repository;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Promotion;
import fr.oxyl.newrofactory.persistence.internal.dao.PromotionInternalDao;
import fr.oxyl.newrofactory.persistence.internal.mapper.PromotionMapper;

@Service
public class PromotionRepo {

    private final PromotionInternalDao promotionInternalDao;
    private final PromotionMapper promotionMapper;

    public PromotionRepo(PromotionInternalDao promotionInternalDao,
                         PromotionMapper promotionMapper){
        this.promotionInternalDao = promotionInternalDao;
        this.promotionMapper = promotionMapper;
    }

    public Page<Promotion> findAll(String search, Pageable pageable){
        return promotionInternalDao.findAll(search, pageable).map(promotionMapper::map);
    }

    public Optional<Promotion> findById(long id){
        return promotionMapper.map(promotionInternalDao.findById(id));
    }

    public void create(Promotion promotion){
        promotionInternalDao.save(promotionMapper.map(promotion));
    }

    public void update(Promotion promotion){
        promotionInternalDao.save(promotionMapper.map(promotion));
    }

    public void deleteById(long id){
        promotionInternalDao.deleteById(id);
    }

    public long getCount() {
        return promotionInternalDao.count();
    }

}
