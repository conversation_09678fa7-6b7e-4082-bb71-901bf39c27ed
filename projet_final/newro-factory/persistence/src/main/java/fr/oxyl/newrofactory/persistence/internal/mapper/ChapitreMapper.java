package fr.oxyl.newrofactory.persistence.internal.mapper;

import java.util.List;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Chapitre;
import fr.oxyl.newrofactory.persistence.internal.entity.ChapitreEntity;

@Component
public class ChapitreMapper {

    public ChapitreMapper() {}

    public Chapitre map(ChapitreEntity chapitreEntity) {
        if (chapitreEntity == null) {
            return null;
        }
        return new Chapitre(
            chapitreEntity.getId(),
            chapitreEntity.getName(),
            map(chapitreEntity.getParent())
        );
    }

    public List<Chapitre> map(List<ChapitreEntity> chapitreEntities) {
        if (chapitreEntities == null) {
            return null;
        }
        return chapitreEntities.stream()
                .map(this::map)
                .toList();
    }
    
}
