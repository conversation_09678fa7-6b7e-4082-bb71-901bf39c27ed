package fr.oxyl.newrofactory.persistence.service;

import java.util.Optional;
import java.util.List;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.persistence.internal.dao.QuestionInternalDao;
import fr.oxyl.newrofactory.persistence.internal.mapper.QuestionMapper;

@Service
public class QuestionDaoService {

    private final QuestionInternalDao questionInternalDao;
    private final QuestionMapper questionMapper;

    public QuestionDaoService(QuestionInternalDao questionInternalDao,
    QuestionMapper questionMapper){
        this.questionInternalDao = questionInternalDao;
        this.questionMapper = questionMapper;
    }

    public Optional<Question> findById(long id){
        return questionMapper.map(questionInternalDao.findById(id));
    }

    public long count(){
        return questionInternalDao.count();
    }

    public void deleteById(long id){
        questionInternalDao.deleteById(id);
    }

    public List<Question> findAll() {
        return questionMapper.map(questionInternalDao.findAll());
    }
}
