package fr.oxyl.newrofactory.persistence.internal.mapper;

import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.springframework.stereotype.Component;

import fr.oxyl.newrofactory.core.model.Quiz;
import fr.oxyl.newrofactory.persistence.internal.entity.QuizEntity;

@Component
public class QuizMapper {

    private final QuestionMapper questionMapper;

    public QuizMapper(QuestionMapper questionMapper) {
        this.questionMapper = questionMapper;
    }

    public Quiz map(QuizEntity quizEntity) {
        if (quizEntity == null) {
            return null;
        }

        return new Quiz(
            quizEntity.getId(),
            quizEntity.getTitle(),
            quizEntity.getDescription(),
            quizEntity.getCreationDate(),
            quizEntity.getQuestions().stream()
                .map(questionMapper::map)
                .collect(Collectors.toList()),
            quizEntity.getCreatedBy()
        );
    }

    public Optional<Quiz> map(Optional<QuizEntity> optionalQuizEntity) {
        return optionalQuizEntity.map(this::map);
    }

    public Iterable<Quiz> map(Iterable<QuizEntity> quizEntities) {
        return StreamSupport.stream(quizEntities.spliterator(), false)
            .map(this::map)
            .collect(Collectors.toList());
    }

    public QuizEntity map(Quiz quiz) {
        if (quiz == null) {
            return null;
        }

        return new QuizEntity(
            quiz.getId(),
            quiz.getTitle(),
            quiz.getDescription(),
            quiz.getCreationDate(),
            questionMapper.mapToEntities(quiz.getQuestions()),
            quiz.getCreatedBy()
        );
    }
}
