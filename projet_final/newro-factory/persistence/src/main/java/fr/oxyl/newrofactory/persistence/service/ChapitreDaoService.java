package fr.oxyl.newrofactory.persistence.service;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.persistence.internal.dao.ChapitreInternalDao;
import fr.oxyl.newrofactory.persistence.internal.mapper.ChapitreMapper;

@Service
public class ChapitreDaoService {

    private final ChapitreInternalDao chapitreInternalDao;

    public ChapitreDaoService(ChapitreInternalDao chapitreInternalDao,
    ChapitreMapper chapitreMapper){
        this.chapitreInternalDao = chapitreInternalDao;
    }

    public long count(){
        return chapitreInternalDao.count();
    }
    
}
