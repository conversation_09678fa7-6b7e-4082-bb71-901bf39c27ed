package fr.oxyl.newrofactory.persistence.internal.dao;

import java.util.List;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import fr.oxyl.newrofactory.persistence.internal.entity.UserFavoriteEntity;
import fr.oxyl.newrofactory.persistence.internal.entity.UserFavoriteId;

/**
 * Repository interface for UserFavoriteEntity.
 * Provides CRUD operations and custom queries for user favorites.
 */
@Repository
public interface UserFavoriteInternalDao extends CrudRepository<UserFavoriteEntity, UserFavoriteId> {
    
    /**
     * Find all favorites for a specific user.
     * 
     * @param userId the ID of the user
     * @return list of UserFavoriteEntity objects
     */
    List<UserFavoriteEntity> findByUserId(String userId);
    
    /**
     * Check if a user has favorited another user.
     * 
     * @param userId the ID of the user
     * @param favoriteUserId the ID of the favorite user
     * @return true if the user has favorited the other user, false otherwise
     */
    boolean existsByUserIdAndFavoriteUserId(String userId, String favoriteUserId);
    
    /**
     * Delete a favorite relationship between two users.
     * 
     * @param userId the ID of the user
     * @param favoriteUserId the ID of the favorite user
     */
    void deleteByUserIdAndFavoriteUserId(String userId, String favoriteUserId);
}