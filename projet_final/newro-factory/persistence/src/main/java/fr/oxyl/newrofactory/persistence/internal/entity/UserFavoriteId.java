package fr.oxyl.newrofactory.persistence.internal.entity;

import java.io.Serializable;
import java.util.Objects;

/**
 * Composite primary key class for UserFavoriteEntity.
 * This class represents the composite key consisting of userId and favoriteUserId.
 */
public class UserFavoriteId implements Serializable {

    private static final long serialVersionUID = 1L;

    private String userId;
    private String favoriteUserId;

    public UserFavoriteId() {
    }

    public UserFavoriteId(String userId, String favoriteUserId) {
        this.userId = userId;
        this.favoriteUserId = favoriteUserId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFavoriteUserId() {
        return favoriteUserId;
    }

    public void setFavoriteUserId(String favoriteUserId) {
        this.favoriteUserId = favoriteUserId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserFavoriteId that = (UserFavoriteId) o;
        return Objects.equals(userId, that.userId) && 
               Objects.equals(favoriteUserId, that.favoriteUserId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, favoriteUserId);
    }
}