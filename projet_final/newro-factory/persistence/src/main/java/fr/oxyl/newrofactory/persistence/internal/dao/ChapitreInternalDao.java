package fr.oxyl.newrofactory.persistence.internal.dao;

import java.util.List;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import fr.oxyl.newrofactory.persistence.internal.entity.ChapitreEntity;

@Repository
public interface ChapitreInternalDao extends CrudRepository<ChapitreEntity, Long> {

    /**
     * Find all chapters that have the specified chapter as a parent
     * @param parent the parent chapter
     * @return list of chapters that have the specified parent
     */
    List<ChapitreEntity> findByParentContaining(ChapitreEntity parent);
}
