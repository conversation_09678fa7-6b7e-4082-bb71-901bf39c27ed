package fr.oxyl.newrofactory.persistence.repository;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import fr.oxyl.newrofactory.core.model.Question;
import fr.oxyl.newrofactory.persistence.internal.dao.QuestionInternalDao;
import fr.oxyl.newrofactory.persistence.internal.mapper.QuestionMapper;

@Service
public class QuestionRepo {

    private final QuestionInternalDao questionInternalDao;
    private final QuestionMapper questionMapper;

    public QuestionRepo(QuestionInternalDao questionInternalDao,
                        QuestionMapper questionMapper){
        this.questionInternalDao = questionInternalDao;
        this.questionMapper = questionMapper;
    }

    public Optional<Question> findById(long id){
        return questionMapper.map(questionInternalDao.findById(id));
    }

    public long count(){
        return questionInternalDao.count();
    }

    public void deleteById(long id){
        questionInternalDao.deleteById(id);
    }

    public void create(Question question){
        questionInternalDao.save(questionMapper.map(question));
    }

    public void update(Question question){
        questionInternalDao.save(questionMapper.map(question));
    }

    @Transactional(readOnly = true)
    public Page<Question> findAll(String search, Pageable pageable){
        return questionInternalDao.findAll(search, pageable).map(questionMapper::map);
    }
}
