package fr.oxyl.newrofactory.persistence.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.persistence.internal.dao.UserFavoriteInternalDao;
import fr.oxyl.newrofactory.persistence.internal.entity.UserFavoriteEntity;
import fr.oxyl.newrofactory.persistence.internal.entity.UserFavoriteId;

/**
 * Repository service for user favorites.
 * Provides methods to manage user favorites.
 */
@Service
public class UserFavoriteRepo {

    private final UserFavoriteInternalDao userFavoriteInternalDao;

    public UserFavoriteRepo(UserFavoriteInternalDao userFavoriteInternalDao) {
        this.userFavoriteInternalDao = userFavoriteInternalDao;
    }

    /**
     * Find all favorites for a specific user.
     * 
     * @param userId the ID of the user
     * @return list of UserFavoriteEntity objects
     */
    public List<UserFavoriteEntity> findByUserId(String userId) {
        return userFavoriteInternalDao.findByUserId(userId);
    }

    /**
     * Check if a user has favorited another user.
     * 
     * @param userId the ID of the user
     * @param favoriteUserId the ID of the favorite user
     * @return true if the user has favorited the other user, false otherwise
     */
    public boolean existsByUserIdAndFavoriteUserId(String userId, String favoriteUserId) {
        return userFavoriteInternalDao.existsByUserIdAndFavoriteUserId(userId, favoriteUserId);
    }

    /**
     * Add a user to favorites.
     * 
     * @param userId the ID of the user
     * @param favoriteUserId the ID of the user to favorite
     * @return the created UserFavoriteEntity
     */
    public UserFavoriteEntity addFavorite(String userId, String favoriteUserId) {
        UserFavoriteEntity userFavorite = new UserFavoriteEntity(userId, favoriteUserId);
        return userFavoriteInternalDao.save(userFavorite);
    }

    /**
     * Remove a user from favorites.
     * 
     * @param userId the ID of the user
     * @param favoriteUserId the ID of the user to remove from favorites
     */
    public void removeFavorite(String userId, String favoriteUserId) {
        userFavoriteInternalDao.deleteByUserIdAndFavoriteUserId(userId, favoriteUserId);
    }

    /**
     * Find a specific favorite relationship.
     * 
     * @param userId the ID of the user
     * @param favoriteUserId the ID of the favorite user
     * @return Optional containing the UserFavoriteEntity if found
     */
    public Optional<UserFavoriteEntity> findById(String userId, String favoriteUserId) {
        UserFavoriteId id = new UserFavoriteId(userId, favoriteUserId);
        return userFavoriteInternalDao.findById(id);
    }
}