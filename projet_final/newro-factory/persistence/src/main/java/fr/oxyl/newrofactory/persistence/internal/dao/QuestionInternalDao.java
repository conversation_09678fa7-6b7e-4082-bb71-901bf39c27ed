package fr.oxyl.newrofactory.persistence.internal.dao;

import java.util.Optional;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import fr.oxyl.newrofactory.persistence.internal.entity.QuestionEntity;

@Repository
public interface QuestionInternalDao extends CrudRepository<QuestionEntity, Long> {
    
    Optional<QuestionEntity> findById(long id);
}
