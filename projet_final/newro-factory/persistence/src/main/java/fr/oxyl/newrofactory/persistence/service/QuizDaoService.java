package fr.oxyl.newrofactory.persistence.service;

import java.util.Optional;

import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.core.model.Quiz;
import fr.oxyl.newrofactory.persistence.internal.dao.QuizInternalDao;
import fr.oxyl.newrofactory.persistence.internal.mapper.QuizMapper;

@Service
public class QuizDaoService {

    private final QuizInternalDao quizInternalDao;
    private final QuizMapper quizMapper;

    public QuizDaoService(QuizInternalDao quizInternalDao, QuizMapper quizMapper) {
        this.quizInternalDao = quizInternalDao;
        this.quizMapper = quizMapper;
    }

    public Optional<Quiz> findById(long id) {
        return quizMapper.map(quizInternalDao.findById(id));
    }

    public Iterable<Quiz> findAll() {
        return quizMapper.map(quizInternalDao.findAll());
    }

    public Iterable<Quiz> findByCreatedBy(String createdBy) {
        return quizMapper.map(quizInternalDao.findByCreatedBy(createdBy));
    }

    public Quiz save(Quiz quiz) {
        return quizMapper.map(quizInternalDao.save(quizMapper.map(quiz)));
    }

    public void deleteById(long id) {
        quizInternalDao.deleteById(id);
    }

    public long count() {
        return quizInternalDao.count();
    }
}