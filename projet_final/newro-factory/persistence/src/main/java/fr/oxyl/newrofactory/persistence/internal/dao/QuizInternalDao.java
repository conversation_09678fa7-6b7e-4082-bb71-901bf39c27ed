package fr.oxyl.newrofactory.persistence.internal.dao;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import fr.oxyl.newrofactory.persistence.internal.entity.QuizEntity;

@Repository
public interface QuizInternalDao extends CrudRepository<QuizEntity, Long> {
    // Find quizzes created by a specific user
    Iterable<QuizEntity> findByCreatedBy(String createdBy);
}