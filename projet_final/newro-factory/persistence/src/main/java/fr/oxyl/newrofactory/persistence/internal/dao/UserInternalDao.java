package fr.oxyl.newrofactory.persistence.internal.dao;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import fr.oxyl.newrofactory.persistence.internal.entity.UserEntity;

@Repository
public interface UserInternalDao extends PagingAndSortingRepository<UserEntity, String>,
        CrudRepository<UserEntity, String> {

    @Query("""
            SELECT u
            FROM UserEntity u
            WHERE :search IS NULL 
                OR u.username LIKE CONCAT('%', :search, '%')
                OR u.firstname LIKE CONCAT('%', :search, '%')
                OR u.lastname LIKE CONCAT('%', :search, '%')
            """)
    Page<UserEntity> findAll(@Param("search") String search, Pageable pageable);
}
