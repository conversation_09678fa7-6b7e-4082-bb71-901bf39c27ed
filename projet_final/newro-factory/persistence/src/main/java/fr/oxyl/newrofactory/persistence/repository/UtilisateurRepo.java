package fr.oxyl.newrofactory.persistence.repository;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.persistence.internal.dao.UserInternalDao;
import fr.oxyl.newrofactory.persistence.internal.entity.UserEntity;

@Service
public class UtilisateurRepo {

    private final UserInternalDao userInternalDao;

    public UtilisateurRepo(UserInternalDao userInternalDao){
        this.userInternalDao = userInternalDao;
    }

    public Optional<? extends UserDetails> findById(String username){
        return userInternalDao.findById(username);
    }

    public UserEntity save(UserEntity user) {
        return userInternalDao.save(user);
    }

    /**
     * Find all users in the system
     * @return Iterable of UserEntity objects
     */
    public Iterable<UserEntity> findAll() {
        return userInternalDao.findAll();
    }

    /**
     * Find all users with pagination and filtering
     * @param search Search term to filter users
     * @param currentPage Current page number (0-based)
     * @param pageSize Number of items per page
     * @param sortBy Field to sort by
     * @return Page of UserEntity objects
     */
    public Page<UserEntity> findAll(String search, int currentPage, int pageSize, String sortBy) {
        Sort sort = Sort.by(sortBy);
        Pageable pageable = PageRequest.of(currentPage, pageSize, sort);
        return userInternalDao.findAll(search, pageable);
    }

    public void deleteById(String id){
        userInternalDao.deleteById(id);
    }

    public long getCount() {
        return userInternalDao.count();
    }
}
