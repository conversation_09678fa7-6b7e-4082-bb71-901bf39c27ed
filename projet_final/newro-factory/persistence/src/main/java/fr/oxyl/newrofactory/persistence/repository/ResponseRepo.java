package fr.oxyl.newrofactory.persistence.repository;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import fr.oxyl.newrofactory.persistence.internal.dao.ResponseInternalDao;
import fr.oxyl.newrofactory.persistence.internal.entity.ResponseEntity;
import fr.oxyl.newrofactory.persistence.internal.mapper.ReponseMapper;

import java.util.List;
import java.util.Optional;

@Service
public class ResponseRepo {

    private final ResponseInternalDao responseInternalDao;

    public ResponseRepo(ResponseInternalDao responseInternalDao,
                        ReponseMapper reponseMapper) {
        this.responseInternalDao = responseInternalDao;
    }

    public long count() {
        return responseInternalDao.count();
    }


    public Page<ResponseEntity> findAll(String search, Pageable pageable)
    {
        return responseInternalDao.findAll(search, pageable);
    }

    public Optional<ResponseEntity> findById(long id) {
        return responseInternalDao.findById(id);
    }

    public ResponseEntity save(ResponseEntity responseEntity) {
        return responseInternalDao.save(responseEntity);
    }

    public void deleteById(long id) {
        responseInternalDao.deleteById(id);
    }

    public long getCount() {
        return responseInternalDao.count();
    }

    public List<ResponseEntity> findAllByQuestionId(long questionId) {
        return responseInternalDao.findAllByQuestionId(questionId);
    }
}
